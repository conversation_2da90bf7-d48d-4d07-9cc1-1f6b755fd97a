package com.yuanchuan.user.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName UserInfo
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/16 10:13
 * @Version 1.0
 */

@Data
@Schema(description = "用户信息")
public class UserInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "用户ID", example = "123")
    Long userId;

    @Schema(description = "账户ID", example = "123")
    private Long businessAccountId;

    @Schema(description = "用户昵称", example = "美食客_1234")
    private String nickname;

    @Schema(description = "手机号", example = "**********")
    private String phone;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "用户头像")
    private String avatar;
}
