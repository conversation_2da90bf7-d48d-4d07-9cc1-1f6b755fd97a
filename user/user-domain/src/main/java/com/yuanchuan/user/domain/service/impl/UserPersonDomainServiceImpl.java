package com.yuanchuan.user.domain.service.impl;

import com.yuanchuan.authentication.api.request.PhoneBindingRequest;
import com.yuanchuan.authentication.api.service.AuthenticationService;
import com.yuanchuan.common.enums.users.Gender;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.user.context.enums.AccountStatus;
import com.yuanchuan.user.context.enums.AccountType;
import com.yuanchuan.user.context.enums.RegisterSourceEnum;
import com.yuanchuan.user.context.enums.RegistrationTypeEnum;
import com.yuanchuan.user.domain.model.*;
import com.yuanchuan.user.domain.repository.*;
import com.yuanchuan.user.domain.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 用户领域服务实现类
 * 包含所有用户相关的业务逻辑
 */
@Slf4j
@Service
public class UserPersonDomainServiceImpl implements UserPersonDomainService {


    @Autowired
    private UserPersonRepository userPersonRepository;

    @Autowired
    private CustomerAccountRepository customerAccountRepository;

    @Autowired
    private BusinessAccountRepository businessAccountRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private BusinessAccountRoleRepository businessAccountRoleRepository;

    @Autowired
    private AccountDeviceRepository accountDeviceRepository;

    @Autowired
    private DeviceLimitService deviceLimitService;

    @Autowired
    private PasswordPolicyService passwordPolicyService;

    @Autowired
    private UserThirdPartyAuthRespoitory userThirdPartyAuthRespoitory;

    @Autowired
    private UserThirdPartyBindingRespoitory userThirdPartyBindingRespoitory;

    @Autowired
    private UserChangeLogsService userChangeLogsService;

    @Autowired
    private ThirdPartyBindingService thirdPartyBindingService;

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.authentication}")
    private AuthenticationService authenticationService;


    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();


    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserPerson verifySmsCodeAndLogin(UserRegisterDomain registerDomain) {


        // 查询手机号是否已注册
        UserPerson userPerson = userPersonRepository.findByPhoneAndSource(registerDomain.getPhone(), registerDomain.getSource().getCode())
                .orElse(null);
        // 手机号已注册
        if (Objects.nonNull(userPerson) && userPerson.getId() != null) {
            CustomerAccount account = customerAccountRepository.findByUserPersonId(userPerson.getId())
                    .orElseThrow(() -> new IllegalArgumentException("用户账号不存在"));

            userPerson.setCustomerAccount(account);
            userPerson.setRegisterFlag(true);

            // 登录判断设备是否更改，新增设备
            AccountDevice accountDevice = registerDomain.getAccountDevice();
            if (Objects.nonNull(accountDevice) && StringUtils.isNotEmpty(accountDevice.getDeviceId())) {
                // 检查设备是否已绑定
                Optional<AccountDevice> existingDevice = accountDeviceRepository.findByAccountIdAndDeviceId(
                        account.getId(), accountDevice.getDeviceId());

                // 如果是新设备，记录设备变更日志
                if (!existingDevice.isPresent()) {
                    try {
                        // 绑定新设备
                        accountDevice.setAccountId(account.getId());
                        this.bindDevice(accountDevice);

                        // 记录设备变更日志
                        userChangeLogsService.createFieldChangeLogs(
                            userPerson.getId(),
                            "device_id",
                            null,
                            accountDevice.getDeviceId(),
                            accountDevice.getDeviceId(),
                            registerDomain.getSource().getCode()
                        );
                    } catch (Exception e) {
                        // 绑定设备失败不影响登录，只记录日志
                        log.error("绑定设备或记录设备变更失败: " + e.getMessage());
                    }
                }
            }

            return userPerson;
        } else {
            // 手机号未注册
            return this.register(registerDomain);


        }
    }

    @Override
    public UserPerson verifyEmailCodeAndLogin(UserRegisterDomain registerDomain) {
        // 验证邮箱是否存在
        String email = registerDomain.getEmail();
        if (StringUtils.isEmpty(email)) {
            throw new BusinessException(UsersErrorCode.EMAIL_EMPTY_VERIFY.getCode(), UsersErrorCode.EMAIL_EMPTY_VERIFY.getMsg());
        }
        AccountDevice accountDevice = registerDomain.getAccountDevice();

        // 查询邮箱是否已注册
        UserPerson userPerson = userPersonRepository.findByEmailAndSource(email, registerDomain.getSource().getCode())
                .orElse(null);

        if (userPerson != null) {
            // 邮箱已注册
            CustomerAccount account = customerAccountRepository.findByUserPersonId(userPerson.getId())
                    .orElseThrow(() -> new IllegalArgumentException("用户账号不存在"));

            userPerson.setCustomerAccount(account);
            userPerson.setRegisterFlag(true);

            // 绑定设备
            if (Objects.nonNull(accountDevice) && accountDevice.getId() != null) {
                accountDevice.setAccountId(account.getId());
                accountDevice.setSource(registerDomain.getSource().getCode());

                try {
                    this.bindDevice(accountDevice);
                } catch (Exception e) {
                    // 绑定设备失败不影响登录，只记录日志
                    log.error("绑定设备失败: " + e.getMessage());
                }
            }

            return userPerson;
        } else {
            PhoneBindingRequest request = new PhoneBindingRequest();
            request.setDeviceId(accountDevice.getDeviceId());
            request.setEmail(email);
            authenticationService.createPhoneBinding(request);
            // 邮箱未注册，需要绑定手机号
            throw new BusinessException(UsersErrorCode.EMAIL_NOT_BINDING_PHONE.getCode(), UsersErrorCode.EMAIL_NOT_BINDING_PHONE.getMsg());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserPerson register(UserRegisterDomain request) {
        // 创建用户基本信息
        UserPerson userPerson = new UserPerson();
        userPerson.setPhone(request.getPhone());
        userPerson.setEmail(request.getEmail());
        userPerson.setSource(request.getSource().getCode());
        userPerson.setCreatedAt(LocalDateTime.now());
        userPerson.setUpdatedAt(LocalDateTime.now());
        userPerson.setCreatedBy("system");
        userPerson.setUpdatedBy("system");
        userPerson.setActive(true);
        userPerson.setRegisterFlag(true);

        // 保存用户基本信息
        userPerson = userPersonRepository.save(userPerson);

        // 创建用户账号信息
        CustomerAccount account = new CustomerAccount();
        account.setUserPersonId(userPerson.getId());
        account.setNickName(request.getNickname() != null ? request.getNickname() : CustomerAccount.generateDefaultNickname());
        account.setPasswordUpdatedAt(LocalDateTime.now());
        // 设置注册来源（平台渠道）
        account.setRegisterSource(request.getRegisterSource().getName());
        if (request.getRegisterSource().getName().equals(RegisterSourceEnum.APP.getName())){
            account.setFirstAppLoginAt(LocalDateTime.now());
        }
        // 设置注册类型（注册方式）
        account.setRegisterType(request.getRegistrationType().getName());
        account.setRegisterIp(request.getRegisterIp());
        account.setRegisterDevice(request.getRegisterDevice());
        account.setRegisterTime(LocalDateTime.now());
        account.setUserStatus("ACTIVE");
        account.setCreatedAt(LocalDateTime.now());
        account.setUpdatedAt(LocalDateTime.now());
        account.setCreatedBy("system");
        account.setUpdatedBy("system");
        account.setActive(true);

        // 保存用户账号信息
        account = customerAccountRepository.save(account);

        // 绑定三方
        if (RegistrationTypeEnum.THIRD_BINDING.equals(request.getRegistrationType())) {

            UserThirdPartyBinding saveBinding = request.getUserThirdPartyBinding();
            saveBinding.setCustomerAccountId(account.getId());
            UserThirdPartyBinding binding = userThirdPartyBindingRespoitory.save(saveBinding);


            UserThirdPartyAuth saveAuth = request.getUserThirdPartyAuth();
            saveAuth.setCustomerAccountId(account.getId());
            saveAuth.setBindingId(binding.getId());
            userThirdPartyAuthRespoitory.save(saveAuth);
        }

        // 设置关联关系
        userPerson.setCustomerAccount(account);

        try {
            // 绑定设备
            AccountDevice accountDevice = request.getAccountDevice();
            if (Objects.nonNull(accountDevice) && accountDevice.getDeviceId() != null) {
                // 创建用户设备信息
                accountDevice.setAccountId(account.getId());
                accountDevice.setSource(request.getSource().getCode());
                try {

                    this.bindDevice(accountDevice);
                } catch (Exception e) {
                    // 绑定设备失败不影响登录，只记录日志
                    log.error("绑定设备失败: " + e.getMessage());
                }
            }

            // 注册用户
        } catch (Exception ex) {
            log.error("注册用户失败: " + ex.getMessage());
            throw new BusinessException(UsersErrorCode.USER_PUBLIC_CODE.getCode(), "註冊失敗" );
        }

        // 返回用户领域对象
        return userPerson;
    }

    @Override
    public UserPerson verifyPassword(UserRegisterDomain registerDomain) {
        UserPerson userPerson = null;
        CustomerAccount account = null;
        BusinessAccount businessAccount = null;

        // 根据手机号或邮箱查询用户
        if (StringUtils.isNotEmpty(registerDomain.getPhone())) {
            userPerson = userPersonRepository.findByPhoneAndSource(registerDomain.getPhone(), registerDomain.getSource().getCode())
                    .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));

            // 查询用户账号信息
            account = customerAccountRepository.findByUserPersonId(userPerson.getId())
                    .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));

        } else if (StringUtils.isNotEmpty(registerDomain.getEmail())) {
            userPerson = userPersonRepository.findByEmailAndSource(registerDomain.getEmail(), registerDomain.getSource().getCode())
                    .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));

            // 查询用户账号信息
            account = customerAccountRepository.findByUserPersonId(userPerson.getId())
                    .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));

        } else if (registerDomain.getSource() != null && registerDomain.getSource() == PlatformType.ADMIN
                && StringUtils.isNotEmpty(registerDomain.getAccountName())) {
            // 管理员账号直接查询business_account表
            businessAccount = businessAccountRepository.findByAccountNameAndAccountType(registerDomain.getAccountName(), AccountType.ADMIN.getCode())
                    .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));

            // 创建一个空的UserPerson对象，用于返回
            userPerson = new UserPerson();
            userPerson.setId(businessAccount.getUserPersonId());
            userPerson.setBusinessAccount(businessAccount);
        } else {
            throw new BusinessException(UsersErrorCode.ACCOUNT_ALL_EMPTY.getCode(), UsersErrorCode.ACCOUNT_ALL_EMPTY.getMsg());
        }

        // 验证密码
        if (registerDomain.getSource() != null && registerDomain.getSource() == PlatformType.ADMIN
                && businessAccount != null) {

            // 验证密码
            if (businessAccount.getPassword() == null || !passwordPolicyService.matches(registerDomain.getPassWord(), businessAccount.getPassword())) {
                throw new BusinessException(UsersErrorCode.PASSWORD_VERIFY_ERROR.getCode(), UsersErrorCode.PASSWORD_VERIFY_ERROR.getMsg());
            }


            // 返回用户领域对象
            return userPerson;
        } else {
            // 普通用户密码验证
            if (account.getPassword() == null || !passwordPolicyService.matches(registerDomain.getPassWord(), account.getPassword())) {
                throw new BusinessException(UsersErrorCode.PASSWORD_VERIFY_ERROR.getCode(), UsersErrorCode.PASSWORD_VERIFY_ERROR.getMsg());
            }

            // 设置关联关系
            userPerson.setCustomerAccount(account);

            // 返回用户领域对象
            return userPerson;
        }
    }

    @Override
    public UserPerson findById(Long userId) {

        // 查询用户基本信息
        UserPerson userPerson = userPersonRepository.findById(userId)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));


        // 查询用户账号信息
        CustomerAccount account = customerAccountRepository.findByUserPersonId(userPerson.getId())
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));


        // 设置关联关系
        userPerson.setCustomerAccount(account);

        // 返回用户领域对象
        return userPerson;
    }

    @Override
    public UserPerson findByPhoneAndSource(String phone, String source) {
        // 根据手机号查询用户
        UserPerson userPerson = userPersonRepository.findByPhoneAndSource(phone, source)
                .orElse(null);
        if (Objects.isNull(userPerson)){
            return userPerson;
        }

        // 查询用户账号信息
        CustomerAccount account = customerAccountRepository.findByUserPersonId(userPerson.getId())
                .orElse(null);

        // 设置关联关系
        userPerson.setCustomerAccount(account);

        // 返回用户领域对象
        return userPerson;
    }

    @Override
    public boolean checkPhoneExists(String phone, String source) {
        // 根据手机号查询用户
        Optional<UserPerson> userPersonOptional = userPersonRepository.findByPhoneAndSource(phone, source);

        // 如果用户不存在，直接返回false
        if (!userPersonOptional.isPresent()) {
            return false;
        }

        // 查询用户账号信息
        UserPerson userPerson = userPersonOptional.get();
        Optional<CustomerAccount> accountOptional = customerAccountRepository.findByUserPersonId(userPerson.getId());

        // 只有用户和账号都存在时才返回true
        return accountOptional.isPresent();
    }

    @Override
    public UserPerson findByEmailAndSource(String email, String source) {
        // 根据邮箱查询用户
        UserPerson userPerson = userPersonRepository.findByEmailAndSource(email, source).orElse(null);
        if (userPerson == null) {
            return userPerson;
        }

        // 查询用户账号信息
        CustomerAccount account = customerAccountRepository.findByUserPersonId(userPerson.getId()).orElse(null);
        // 设置关联关系
        userPerson.setCustomerAccount(account);
        // 返回用户领域对象
        return userPerson;
    }

    @Override
    public boolean checkEmailExists(String email, String source) {
        // 根据邮箱查询用户
        Optional<UserPerson> userPersonOptional = userPersonRepository.findByEmailAndSource(email, source);
        if (userPersonOptional.isPresent()){
            Optional<CustomerAccount> byUserPersonId = customerAccountRepository.findByUserPersonId(userPersonOptional.get().getId());
            return byUserPersonId.isPresent();
        }
        return userPersonOptional.isPresent();
    }

    @Override
    @Transactional
    public UserPerson updateUser(Long userId, UserRegisterDomain request) {
        // 查询用户账号信息
        CustomerAccount account = customerAccountRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户不存在"));

        // 查询用户基本信息
        UserPerson userPerson = userPersonRepository.findById(account.getUserPersonId())
                .orElseThrow(() -> new IllegalArgumentException("用户基本信息不存在"));

        //// 更新用户基本信息
        //if (request.getPhone() != null) {
        //    userPerson.setPhone(request.getPhone());
        //}

        if (request.getEmail() != null) {
            userPerson.setEmail(request.getEmail());
        }

        userPerson.setUpdatedAt(LocalDateTime.now());
        userPerson.setUpdatedBy("system");

        // 保存用户基本信息
        userPerson = userPersonRepository.save(userPerson);

        // 更新用户账号信息
        if (request.getNickname() != null) {
            account.setNickName(request.getNickname());
        }


        account.setUpdatedAt(LocalDateTime.now());
        account.setUpdatedBy("system");

        // 保存用户账号信息
        account = customerAccountRepository.save(account);

        // 设置关联关系
        userPerson.setCustomerAccount(account);

        // 返回用户领域对象
        return userPerson;
    }

    @Override
    public UserPerson confirmUseExistingAccount(UserRegisterDomain registerDomain) {
        // 根据手机号查询用户
        UserPerson userPerson = null;
        try {
            userPerson = this.findByPhoneAndSource(registerDomain.getPhone(), registerDomain.getSource().getCode());
        } catch (Exception e) {
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }

        if (userPerson == null || userPerson.getId() == null) {
            throw new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg());
        }      // 检查跨平台用户
        boolean isCrossPlatformUser = false;
        UserRegisterDomain updateRequest = new UserRegisterDomain();
        if (registerDomain.getRegisterSource() == RegisterSourceEnum.APP) {
            CustomerAccount customerAccount = userPerson.getCustomerAccount();
            if (customerAccount != null) {
                String userRegisterSource = customerAccount.getRegisterSource();
                boolean isFirstAppLogin = customerAccount.getFirstAppLoginAt() == null;
                boolean isNonAppUser = StringUtils.isNotBlank(userRegisterSource) &&
                        !RegisterSourceEnum.APP.getName().equalsIgnoreCase(userRegisterSource);
                isCrossPlatformUser = isNonAppUser && isFirstAppLogin;
                updateRequest.setFirstAppLoginAt(LocalDateTime.now());

            }
        }

        // 更新用户邮箱
        if (!StringUtils.isBlank(registerDomain.getEmail()) && !registerDomain.getEmail().equals(userPerson.getEmail()) ){
            updateRequest.setEmail(registerDomain.getEmail());
            // 记录修改
            Map<String, Map<String, Object>> changedFields = new HashMap<>();

            Map<String, Object> map = new HashMap<>();
            map.put("oldValue",  null);
            map.put("newValue", registerDomain.getEmail());
            changedFields.put("email",map);
            // 记录地址修改
            if (!changedFields.isEmpty()) {
                userChangeLogsService.createMultiFieldChangeLogs(
                        userPerson.getId(),
                        changedFields,
                        userPerson.getSource()
                );
            }
        }

        if (Objects.nonNull(updateRequest)) {
            userPerson = this.updateUser(userPerson.getCustomerAccount().getId(), updateRequest);
        }


        // 绑定设备
        AccountDevice accountDevice = registerDomain.getAccountDevice();
        if (Objects.nonNull(accountDevice) && accountDevice.getDeviceId() != null) {
            // 创建用户设备信息
            accountDevice.setAccountId(userPerson.getCustomerAccount().getId());
            accountDevice.setSource(registerDomain.getSource().getCode());

            try {
                this.bindDevice(accountDevice);
            } catch (Exception e) {
                // 绑定设备失败不影响登录，只记录日志
                log.error("绑定设备失败: " + e.getMessage());
            }
        }

        // 跟新三方绑定信息
        if (registerDomain.getRegistrationType() == RegistrationTypeEnum.THIRD_BINDING) {
            // 更新三方绑定信息
            thirdPartyBindingService.updateThirdParty(userPerson.getCustomerAccount().getId(),registerDomain.getUserThirdPartyBinding(),registerDomain.getUserThirdPartyAuth());
        }
        if (isCrossPlatformUser){
            // 设置响应属性
            userPerson.setIsCrossPlatformUser(isCrossPlatformUser);
        }


        // 返回用户领域对象
        return userPerson;
    }


    @Override
    @Transactional
    public UserPerson createNewAccountWithEmail(UserRegisterDomain registerDomain) {
        // 检查手机号是否已存在
        try {
            UserPerson existingUser = this.findByPhoneAndSource(registerDomain.getPhone(), registerDomain.getSource().getCode());
            if (existingUser != null && existingUser.getId() != null) {
                // 如果手机号已存在，先解绑手机号与原账号
                UserPerson userPerson = userPersonRepository.findByPhoneAndSource(registerDomain.getPhone(), registerDomain.getSource().getCode())
                        .orElseThrow(() -> new IllegalArgumentException("用户不存在"));

                // 清除手机号
                userPersonRepository.updatePhone(userPerson.getId(), null);

                // 记录修改
                Map<String, Map<String, Object>> changedFields = new HashMap<>();

                Map<String, Object> map = new HashMap<>();
                map.put("oldValue",  userPerson.getPhone());
                map.put("newValue", null);
                changedFields.put("phone",map);
                // 记录地址修改
                if (!changedFields.isEmpty()) {
                    userChangeLogsService.createMultiFieldChangeLogs(
                            userPerson.getId(),
                            changedFields,
                            userPerson.getSource()
                    );
                }


            }
        } catch (Exception e) {
            log.error("createNewAccountWithEmail解绑手机号失败: " + e.getMessage());
        }


        if (registerDomain.getNickname() == null) {
            registerDomain.setNickname(CustomerAccount.generateDefaultNickname());
        }
        if (registerDomain.getSource() == null) {
            registerDomain.setSource(PlatformType.CUSTOMER);
        }

        // 注册用户
        return this.register(registerDomain);
    }


    /**
     * 生成随机密码
     * 生成一个包含字母和数字的随机密码，长度12位
     *
     * @return 随机密码
     */
    private String generateRandomPassword() {
        // 生成包含6个字母和6个数字的密码
        String letters = RandomStringUtils.randomAlphabetic(6);
        String numbers = RandomStringUtils.randomNumeric(6);

        // 将字母和数字组合并混淆
        char[] combined = (letters + numbers).toCharArray();
        for (int i = 0; i < combined.length; i++) {
            int randomIndex = (int) (Math.random() * combined.length);
            char temp = combined[i];
            combined[i] = combined[randomIndex];
            combined[randomIndex] = temp;
        }

        return new String(combined);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateNickname(Long userId, String nickname) {

        // 参数校验
        if (nickname == null || nickname.isEmpty()) {
            throw new IllegalArgumentException("暱稱不可為空");
        }

        // 昵称格式校验：1-20字符，允许中文/英文/数字
        if (!nickname.matches("^[\\u4e00-\\u9fa5a-zA-Z0-9]{1,20}$")) {
            throw new IllegalArgumentException("暱稱格式不正確，只能包含中文、英文與數字，長度為 1 至 20 個字元");
        }

        // TODO 敏感词检测
//        if (sensitiveWordService.containsSensitiveWord(nickname)) {
//            throw new BusinessException(UsersErrorCode.WORD_SENSITIVE_ERROR.getCode(),UsersErrorCode.WORD_SENSITIVE_ERROR.getMsg());
//        }

        // 查询用户自然人信息
        UserPerson userPerson = userPersonRepository.findById(userId)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));


        CustomerAccount account = customerAccountRepository.findByUserPersonId(userPerson.getId())
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));

        // 记录旧昵称
        String oldNickname = account.getNickName();

        // 更新昵称
        boolean updated = customerAccountRepository.updateNickName(account.getId(), nickname);
        if (!updated) {
            throw new BusinessException(UsersErrorCode.NICKNAME_UPDATE_ERROR.getCode(), UsersErrorCode.NICKNAME_UPDATE_ERROR.getMsg());
        }

        // 记录昵称修改
        userChangeLogsService.createFieldChangeLogs(
            userId,
            "nick_name",
            oldNickname,
            nickname,
            userPerson.getSource()
        );

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAvatar(Long userId, String avatarUrl) {
        // 参数校验
        if (avatarUrl == null || avatarUrl.isEmpty()) {
            throw new IllegalArgumentException("頭像 URL 不可為空");
        }

        // 查询用户账号信息
        UserPerson userPerson = userPersonRepository.findById(userId)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));


        CustomerAccount account = customerAccountRepository.findByUserPersonId(userPerson.getId())
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));


        // 记录旧头像
        String oldAvatar = account.getAvatar();

        // 更新头像
        boolean updated = customerAccountRepository.updateAvatar(account.getId(), avatarUrl);
        if (!updated) {
            throw new BusinessException(UsersErrorCode.AVATAR_UPDATE_ERROR.getCode(), UsersErrorCode.AVATAR_UPDATE_ERROR.getMsg());
        }

        // 记录头像修改
        userChangeLogsService.createFieldChangeLogs(
            userId,
            "avatar",
            oldAvatar,
            avatarUrl,
            userPerson.getSource()
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateGender(Long userId, String gender) {
        // 参数校验
        if (gender == null || gender.isEmpty()) {
            throw new BusinessException(UsersErrorCode.GENDER_EMPTY.getCode(), UsersErrorCode.GENDER_EMPTY.getMsg());
        }

        // 性别枚举值校验
        if (!Gender.isValid(gender)) {
            throw new BusinessException(UsersErrorCode.GENDER_CHECK_ERROR.getCode(), UsersErrorCode.GENDER_CHECK_ERROR.getMsg());
        }


        // 查询用户账号信息
        // 查询用户自然人信息
        UserPerson userPerson = userPersonRepository.findById(userId)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));


        CustomerAccount account = customerAccountRepository.findByUserPersonId(userPerson.getId())
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));


        // 记录旧性别
        String oldGender = account.getGender();

        // 更新性别
        boolean updated = customerAccountRepository.updateGender(account.getId(), gender);
        if (!updated) {
            throw new BusinessException(UsersErrorCode.GENDER_UPDATE_ERROR.getCode(), UsersErrorCode.GENDER_UPDATE_ERROR.getMsg());
        }

        // 记录性别修改
        userChangeLogsService.createFieldChangeLogs(
            userId,
            "gender",
            oldGender,
            gender,
            userPerson.getSource()
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBirthday(Long userId, LocalDate birthday) {
        // 参数校验
        if (birthday == null) {
            throw new BusinessException(UsersErrorCode.BIRTHDAY_EMPTY.getCode(), UsersErrorCode.BIRTHDAY_EMPTY.getMsg());
        }

        // 生日不能是未来日期
        if (birthday.isAfter(LocalDate.now())) {
            throw new BusinessException(UsersErrorCode.BIRTHDAY_FUTURE.getCode(), UsersErrorCode.BIRTHDAY_FUTURE.getMsg());
        }

        // 查询用户账号信息
        // 查询用户自然人信息
        UserPerson userPerson = userPersonRepository.findById(userId)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));


        CustomerAccount account = customerAccountRepository.findByUserPersonId(userPerson.getId())
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));

        // 记录旧生日
        LocalDate oldBirthday = account.getBirthday();

        // 更新生日
        boolean updated = customerAccountRepository.updateBirthday(account.getId(), birthday);
        if (!updated) {
            throw new BusinessException(UsersErrorCode.BIRTHDAY_UPDATE_ERROR.getCode(), UsersErrorCode.BIRTHDAY_UPDATE_ERROR.getMsg());
        }

        // 记录生日修改
        userChangeLogsService.createFieldChangeLogs(
            userId,
            "birthday",
            oldBirthday,
            birthday,
            userPerson.getSource()
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAddress(Long userId, String address, String provinceCode, String cityCode, String regionCode,
                              String provinceName, String cityName, String regionName,
                              BigDecimal longitude, BigDecimal latitude) {
        // 参数校验
        if (address == null || address.isEmpty()) {
            throw new IllegalArgumentException("常居地不可為空");
        }

        // 查询用户账号信息
        // 查询用户自然人信息
        UserPerson userPerson = userPersonRepository.findById(userId)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));


        CustomerAccount account = customerAccountRepository.findByUserPersonId(userPerson.getId())
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));


        // 创建变更字段映射
        Map<String, Map<String, Object>> changedFields = new HashMap<>();

        // 地址字段
        if (!Objects.equals(account.getAddress(), address)) {
            Map<String, Object> addressChange = new HashMap<>();
            addressChange.put("oldValue", account.getAddress());
            addressChange.put("newValue", address);
            changedFields.put("address", addressChange);
        }

        // 省份代码
        if (!Objects.equals(account.getProvinceCode(), provinceCode)) {
            Map<String, Object> provinceCodeChange = new HashMap<>();
            provinceCodeChange.put("oldValue", account.getProvinceCode());
            provinceCodeChange.put("newValue", provinceCode);
            changedFields.put("province_code", provinceCodeChange);
        }

        // 城市代码
        if (!Objects.equals(account.getCityCode(), cityCode)) {
            Map<String, Object> cityCodeChange = new HashMap<>();
            cityCodeChange.put("oldValue", account.getCityCode());
            cityCodeChange.put("newValue", cityCode);
            changedFields.put("city_code", cityCodeChange);
        }

        // 区域代码
        if (!Objects.equals(account.getRegionCode(), regionCode)) {
            Map<String, Object> regionCodeChange = new HashMap<>();
            regionCodeChange.put("oldValue", account.getRegionCode());
            regionCodeChange.put("newValue", regionCode);
            changedFields.put("region_code", regionCodeChange);
        }

        // 省份名称
        if (!Objects.equals(account.getProvinceName(), provinceName)) {
            Map<String, Object> provinceNameChange = new HashMap<>();
            provinceNameChange.put("oldValue", account.getProvinceName());
            provinceNameChange.put("newValue", provinceName);
            changedFields.put("province_name", provinceNameChange);
        }

        // 城市名称
        if (!Objects.equals(account.getCityName(), cityName)) {
            Map<String, Object> cityNameChange = new HashMap<>();
            cityNameChange.put("oldValue", account.getCityName());
            cityNameChange.put("newValue", cityName);
            changedFields.put("city_name", cityNameChange);
        }

        // 区域名称
        if (!Objects.equals(account.getRegionName(), regionName)) {
            Map<String, Object> regionNameChange = new HashMap<>();
            regionNameChange.put("oldValue", account.getRegionName());
            regionNameChange.put("newValue", regionName);
            changedFields.put("region_name", regionNameChange);
        }

        // 经度
        if (!Objects.equals(account.getCenterLongitude(), longitude)) {
            Map<String, Object> longitudeChange = new HashMap<>();
            longitudeChange.put("oldValue", account.getCenterLongitude());
            longitudeChange.put("newValue", longitude);
            changedFields.put("longitude", longitudeChange);
        }

        // 纬度
        if (!Objects.equals(account.getCenterLatitude(), latitude)) {
            Map<String, Object> latitudeChange = new HashMap<>();
            latitudeChange.put("oldValue", account.getCenterLatitude());
            latitudeChange.put("newValue", latitude);
            changedFields.put("latitude", latitudeChange);
        }

        // 更新常居地
        boolean updated = customerAccountRepository.updateAddress(account.getId(), address, provinceCode, cityCode, regionCode,
                provinceName, cityName, regionName, longitude, latitude);
        if (!updated) {
            throw new BusinessException(UsersErrorCode.ADDRESS_UPDATE_ERROR.getCode(), UsersErrorCode.ADDRESS_UPDATE_ERROR.getMsg());
        }

        // 记录地址修改
        if (!changedFields.isEmpty()) {
            userChangeLogsService.createMultiFieldChangeLogs(
                userId,
                changedFields,
                userPerson.getSource()
            );
        }
    }

    @Override
    public boolean verifyPhone(Long userId, String phone) {
        // 参数校验
        if (phone == null || phone.isEmpty()) {
            throw new IllegalArgumentException("手機號碼不可為空");
        }

        // 查询用户基本信息
        UserPerson userPerson = userPersonRepository.findById(userId)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));

        // 验证手机号是否匹配
        return phone.equals(userPerson.getPhone());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePhoneAndSource(Long userId, String newPhone, String deviceId, String source) {
        // 参数校验
        if (newPhone == null || newPhone.isEmpty()) {
            throw new BusinessException(UsersErrorCode.NEW_PHONE_EMPTY.getCode(), UsersErrorCode.NEW_PHONE_EMPTY.getMsg());
        }

        // 检查设备换绑次数限制
        if (deviceId != null && !deviceId.isEmpty() && !deviceLimitService.checkPhoneUpdateLimit(deviceId)) {
            throw new BusinessException(UsersErrorCode.DEVICE_LIMIT_EXCEEDED.getCode(), UsersErrorCode.DEVICE_LIMIT_EXCEEDED.getMsg());
        }

        // 查询用户账号信息
        // 查询用户自然人信息
        UserPerson userPerson = userPersonRepository.findById(userId)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));


        CustomerAccount account = customerAccountRepository.findByUserPersonId(userPerson.getId())
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));

        // 检查新手机号是否已被其他用户使用
        UserPerson existingUser = userPersonRepository.findByPhoneAndSource(newPhone, source).orElse(null);
        if (existingUser != null && !existingUser.getId().equals(userPerson.getId())) {
            throw new BusinessException(UsersErrorCode.PHONE_ALREADY_REGISTERED.getCode(), UsersErrorCode.PHONE_ALREADY_REGISTERED.getMsg());
        }

        // 记录旧手机号
        String oldPhone = userPerson.getPhone();

        // 更新手机号
        boolean updated = userPersonRepository.updatePhone(userPerson.getId(), newPhone);
        if (!updated) {
            throw new BusinessException(UsersErrorCode.PHONE_UPDATE_ERROR.getCode(), UsersErrorCode.PHONE_UPDATE_ERROR.getMsg());
        }

        // 记录设备换绑手机号
        userChangeLogsService.createFieldChangeLogs(
            userId,
            "phone",
            oldPhone,
            newPhone,
            deviceId,
            source
        );


        // todo 给原来手机号发送短信通知

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bindEmailAndSource(Long userId, String email, String source) {
        // 参数校验
        if (email == null || email.isEmpty()) {
            throw new BusinessException(UsersErrorCode.EMAIL_EMPTY_VERIFY.getCode(), UsersErrorCode.EMAIL_EMPTY_VERIFY.getMsg());
        }

        // 查询用户自然人信息
        UserPerson userPerson = userPersonRepository.findById(userId)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));


        CustomerAccount account = customerAccountRepository.findByUserPersonId(userPerson.getId())
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));


        // 检查邮箱是否已被其他用户使用
        UserPerson existingUser = userPersonRepository.findByEmailAndSource(email, source).orElse(null);
        if (existingUser != null && !existingUser.getId().equals(userPerson.getId())) {
            throw new BusinessException(UsersErrorCode.EMAIL_ALREADY_REGISTERED.getCode(), UsersErrorCode.EMAIL_ALREADY_REGISTERED.getMsg());
        }

        // 记录旧邮箱
        String oldEmail = userPerson.getEmail();

        // 更新邮箱
        boolean updated = userPersonRepository.updateEmail(userPerson.getId(), email);
        if (!updated) {
            throw new BusinessException(UsersErrorCode.EMAIL_UPDATE_ERROR.getCode(), UsersErrorCode.EMAIL_UPDATE_ERROR.getMsg());
        }

        // 记录邮箱绑定
        userChangeLogsService.createFieldChangeLogs(
            userId,
            "email",
            oldEmail,
            email,
            source
        );

        return true;
    }

    @Override
    public boolean verifyOriginalEmail(Long userId, String email) {
        // 参数校验
        if (email == null || email.isEmpty()) {
            throw new IllegalArgumentException("電子郵件不可為空");
        }

        // 查询用户自然人信息
        UserPerson userPerson = userPersonRepository.findById(userId)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));


        CustomerAccount account = customerAccountRepository.findByUserPersonId(userPerson.getId())
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));

        // 验证邮箱是否匹配
        if (userPerson.getEmail() == null || !email.equals(userPerson.getEmail())) {
            throw new BusinessException(UsersErrorCode.EMAIL_NOT_MATCH.getCode(), UsersErrorCode.EMAIL_NOT_MATCH.getMsg());
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setPassword(Long userId, String password) {
        // 参数校验
        if (password == null || password.isEmpty()) {
            throw new BusinessException(UsersErrorCode.PASSWORD_EMPTY_SET.getCode(), UsersErrorCode.PASSWORD_EMPTY_SET.getMsg());
        }

        // 密码策略验证
        String invalidReason = passwordPolicyService.getInvalidReason(password);
        if (invalidReason != null) {
            throw new BusinessException(UsersErrorCode.PASSWORD_POLICY_ERROR.getCode(), invalidReason);
        }

        // 查询用户自然人信息
        UserPerson userPerson = userPersonRepository.findById(userId)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));


        CustomerAccount account = customerAccountRepository.findByUserPersonId(userPerson.getId())
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));

        // 加密密码
        String encryptedPassword = passwordPolicyService.encryptPassword(password);

        // 更新密码
        boolean updated = customerAccountRepository.updatePassword(account.getId(), encryptedPassword);
        if (!updated) {
            throw new BusinessException(UsersErrorCode.PASSWORD_UPDATE_ERROR.getCode(), UsersErrorCode.PASSWORD_UPDATE_ERROR.getMsg());
        }

        // 记录密码修改（出于安全考虑，不记录具体密码值）
        Map<String, Map<String, Object>> changedFields = new HashMap<>();
        Map<String, Object> passwordChange = new HashMap<>();
        passwordChange.put("oldValue", account.getPassword() != null ? "******" : null);
        passwordChange.put("newValue",encryptedPassword);
        changedFields.put("password", passwordChange);

        Map<String, Object> passwordUpdatedAtChange = new HashMap<>();
        passwordUpdatedAtChange.put("oldValue", account.getPasswordUpdatedAt());
        passwordUpdatedAtChange.put("newValue", LocalDateTime.now());
        changedFields.put("password_updated_at", passwordUpdatedAtChange);


        // 记录密码修改
        userChangeLogsService.createMultiFieldChangeLogs(
            userId,
            changedFields,
            userPerson.getSource()
        );

        return true;
    }

    @Override
    public boolean verifyPassword(Long userId, String password) {
        // 参数校验
        if (password == null || password.isEmpty()) {
            throw new BusinessException(UsersErrorCode.PASSWORD_EMPTY_VERIFY.getCode(), UsersErrorCode.PASSWORD_EMPTY_VERIFY.getMsg());
        }

        // 查询用户账号信息
        // 查询用户自然人信息
        UserPerson userPerson = userPersonRepository.findById(userId)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));


        CustomerAccount account = customerAccountRepository.findByUserPersonId(userPerson.getId())
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));

        // 验证密码
        if (account.getPassword() == null || !passwordPolicyService.matches(password, account.getPassword())) {
            throw new BusinessException(UsersErrorCode.PASSWORD_VERIFY_ERROR.getCode(), UsersErrorCode.PASSWORD_VERIFY_ERROR.getMsg());
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean verifyOriginalPassword(Long userId, String oldPassword) {
        // 验证原密码
        return verifyPassword(userId, oldPassword);
    }

    @Override
    public boolean checkDevicePhoneUpdateLimit(String deviceId) {
        return deviceLimitService.checkPhoneUpdateLimit(deviceId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserPerson createBusinessAccount(UserRegisterDomain registerDomain) {

        // 查询手机号是否已注册
        String findAccount = registerDomain.getPhone();
        if (StringUtils.isNotBlank(registerDomain.getEmail())){
            findAccount = registerDomain.getEmail();
        }
        UserPerson userPerson = userPersonRepository.findByPhoneAndSource(findAccount, registerDomain.getSource().getCode()).orElse(null);
        if (Objects.nonNull(userPerson) && userPerson.getId() != null) {
            // 查询用户账号信息
            BusinessAccount businessAccount = businessAccountRepository.findByUserPersonId(userPerson.getId())
                    .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));

            userPerson.setBusinessAccount(businessAccount);
            userPerson.setRegisterFlag(true);
            return userPerson;
        } else {
            return this.registerMerchantAccount(registerDomain);
        }


    }

    @Override
    public BusinessAccount findBusinessAccountByUserPersonId(Long userPersonId) {
        return businessAccountRepository.findByUserPersonId(userPersonId)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));
    }

    @Override
    public BusinessAccountRole assignRoleToBusinessAccount(Long businessAccountId, Long roleId) {
        // 检查商户账户是否存在
        businessAccountRepository.findById(businessAccountId)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.USER_NOT_EXIST.getCode(), UsersErrorCode.USER_NOT_EXIST.getMsg()));

        // 检查角色是否存在
        roleRepository.findById(roleId)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.ROLE_NOT_EXIST.getCode(), "Role not exist"));

        // 检查是否已经分配该角色
        Optional<BusinessAccountRole> existingRole = businessAccountRoleRepository.findByBusinessAccountIdAndRoleId(businessAccountId, roleId);
        if (existingRole.isPresent()) {
            return existingRole.get();
        }

        // 创建商户账户角色关联
        BusinessAccountRole businessAccountRole = new BusinessAccountRole();
        businessAccountRole.setBusinessAccountId(businessAccountId);
        businessAccountRole.setRoleId(roleId);
        businessAccountRole.setCreatedAt(LocalDateTime.now());
        businessAccountRole.setUpdatedAt(LocalDateTime.now());
        businessAccountRole.setCreatedBy("system");
        businessAccountRole.setUpdatedBy("system");
        businessAccountRole.setActive(true);

        return businessAccountRoleRepository.save(businessAccountRole);
    }

    @Override
    public Role findRoleByRoleCode(String roleCode) {
        return roleRepository.findByRoleCode(roleCode)
                .orElseThrow(() -> new BusinessException(UsersErrorCode.ROLE_NOT_EXIST.getCode(), "Role not exist"));
    }

    @Override
    public boolean verifyNewEmail(Long userId,String platform, String email) {
        // 参数校验
        if (email == null || email.isEmpty()) {
            throw new BusinessException(UsersErrorCode.EMAIL_EMPTY_VERIFY.getCode(), UsersErrorCode.EMAIL_EMPTY_VERIFY.getMsg());
        }

        // 查询用户自然人信息
        Optional<UserPerson> optionalUser = userPersonRepository.findByEmailAndSource(email, platform);
        if (optionalUser.isPresent()) {
            UserPerson userPerson = optionalUser.get();
            if(!Objects.equals(userPerson.getId(), userId)) {
                throw new BusinessException(UsersErrorCode.EMAIL_ALREADY_REGISTERED.getCode(), UsersErrorCode.EMAIL_ALREADY_REGISTERED.getMsg());
            }
        }
        return true;
    }

    @Override
    public boolean bindDevice(AccountDevice accountDevice) {
        if (accountDevice == null) {
            throw new BusinessException(UsersErrorCode.DEVICE_INFO_EMPTY.getCode(), UsersErrorCode.DEVICE_INFO_EMPTY.getMsg());
        }

        if (accountDevice.getAccountId() == null) {
            throw new BusinessException(UsersErrorCode.USER_ID_EMPTY.getCode(), UsersErrorCode.USER_ID_EMPTY.getMsg());
        }

        // 检查设备是否已存在
        AccountDevice existingDevice = null;
        if (accountDevice.getDeviceId() != null) {
            existingDevice = accountDeviceRepository.findByAccountIdAndDeviceId(accountDevice.getAccountId(), accountDevice.getDeviceId())
                    .orElse(null);
        }

        if (existingDevice != null) {
            // 检查设备信息是否发生变化
            boolean needUpdate = false;

            //if (!Objects.equals(existingDevice.getDeviceType(), accountDevice.getDeviceType())) {
            //    existingDevice.setDeviceType(accountDevice.getDeviceType());
            //    needUpdate = true;
            //}
            //
            //if (!Objects.equals(existingDevice.getDeviceName(), accountDevice.getDeviceName())) {
            //    existingDevice.setDeviceName(accountDevice.getDeviceName());
            //    needUpdate = true;
            //}
            //
            //if (!Objects.equals(existingDevice.getOsVersion(), accountDevice.getOsVersion())) {
            //    existingDevice.setOsVersion(accountDevice.getOsVersion());
            //    needUpdate = true;
            //}
            //
            //if (!Objects.equals(existingDevice.getAppVersion(), accountDevice.getAppVersion())) {
            //    existingDevice.setAppVersion(accountDevice.getAppVersion());
            //    needUpdate = true;
            //}
            //
            //if (!Objects.equals(existingDevice.getIpAddress(), accountDevice.getIpAddress())) {
            //    existingDevice.setIpAddress(accountDevice.getIpAddress());
            //    needUpdate = true;
            //}
            //
            //if (!Objects.equals(existingDevice.getUserAgent(), accountDevice.getUserAgent())) {
            //    existingDevice.setUserAgent(accountDevice.getUserAgent());
            //    needUpdate = true;
            //}
            //
            //// 检查最后登录时间是否需要更新
            //LocalDateTime now = LocalDateTime.now();
            //LocalDateTime lastLoginAt = existingDevice.getLastLoginAt();
            //
            //// 如果最后登录时间为空或者与当前时间相差超过1小时，才更新
            //if (lastLoginAt == null || java.time.Duration.between(lastLoginAt, now).toHours() >= 1) {
            //    existingDevice.setLastLoginAt(now);
            //    needUpdate = true;
            //}
            //
            //// 如果设备不活跃，则设置为活跃
            //if (!Boolean.TRUE.equals(existingDevice.getIsActive())) {
            //    existingDevice.setIsActive(true);
            //    needUpdate = true;
            //}

            // 只有当设备信息发生变化时才更新数据库
            if (needUpdate) {
                existingDevice.setUpdatedAt(LocalDateTime.now());
                existingDevice.setUpdatedBy("system");

                // 保存设备信息
                accountDeviceRepository.save(existingDevice);

                log.info("更新设备信息，账户ID: {}, 设备ID: {}", existingDevice.getAccountId(), existingDevice.getDeviceId());
            } else {
                log.info("设备信息未变化，无需更新，账户ID: {}, 设备ID: {}", existingDevice.getAccountId(), existingDevice.getDeviceId());
            }
        } else {
            // 创建新设备信息
            accountDevice.setIsActive(true);
            accountDevice.setIsVerified(true);
            accountDevice.setLastLoginAt(LocalDateTime.now());
            accountDevice.setCreatedAt(LocalDateTime.now());
            accountDevice.setUpdatedAt(LocalDateTime.now());
            accountDevice.setCreatedBy("system");
            accountDevice.setUpdatedBy("system");
            accountDevice.setActive(true);
            accountDevice.setSource("mobile");

            // 保存设备信息
            accountDeviceRepository.save(accountDevice);
        }

        return true;
    }




    @Transactional(rollbackFor = Exception.class)
    public UserPerson registerMerchantAccount(UserRegisterDomain request) {
        // 创建用户基本信息
        UserPerson userPerson = new UserPerson();
        userPerson.setPhone(request.getPhone());
        userPerson.setEmail(request.getEmail());
        userPerson.setSource(request.getSource().getCode());
        userPerson.setCreatedAt(LocalDateTime.now());
        userPerson.setUpdatedAt(LocalDateTime.now());
        userPerson.setCreatedBy("system");
        userPerson.setUpdatedBy("system");
        userPerson.setActive(true);
        userPerson.setRegisterFlag(true);

        // 保存用户基本信息
        userPerson = userPersonRepository.save(userPerson);

        // 创建商户账户信息
        BusinessAccount businessAccount = new BusinessAccount();
        businessAccount.setUserPersonId(userPerson.getId());
        businessAccount.setAccountName(BusinessAccount.generateMerchantAccountName());
        //businessAccount.setPassword(passwordEncoder.encode(registerDomain.getPassWord()));
        businessAccount.setAccountType(AccountType.SHOP.getCode());
        businessAccount.setAccountStatus(AccountStatus.ACTIVE.getCode());
        businessAccount.setCreatedAt(LocalDateTime.now());
        businessAccount.setUpdatedAt(LocalDateTime.now());
        businessAccount.setCreatedBy("system");
        businessAccount.setUpdatedBy("system");
        businessAccount.setActive(true);

        // 保存商户账户信息
        businessAccount = businessAccountRepository.save(businessAccount);

        // 为商户账户分配默认角色（超级管理员）
        try {
            // 查询超级管理员角色
            Role adminRole = roleRepository.findByRoleCode("ADMIN")
                    .orElseThrow(() -> new BusinessException(UsersErrorCode.ROLE_NOT_EXIST.getCode(), "Admin role not exist"));

            // 分配角色
            BusinessAccountRole businessAccountRole = new BusinessAccountRole();
            businessAccountRole.setBusinessAccountId(businessAccount.getId());
            businessAccountRole.setRoleId(adminRole.getId());
            businessAccountRole.setCreatedAt(LocalDateTime.now());
            businessAccountRole.setUpdatedAt(LocalDateTime.now());
            businessAccountRole.setCreatedBy("system");
            businessAccountRole.setUpdatedBy("system");
            businessAccountRole.setActive(true);

            businessAccountRoleRepository.save(businessAccountRole);
        } catch (Exception e) {
            // 分配角色失败不影响账户创建，只记录日志
            log.error("分配默认角色失败: " + e.getMessage());
        }

        // 绑定设备
        AccountDevice accountDevice = request.getAccountDevice();
        if (Objects.nonNull(accountDevice) && accountDevice.getId() != null) {
            accountDevice.setAccountId(businessAccount.getId());
            accountDevice.setSource(request.getSource().getCode());

            try {
                this.bindDevice(accountDevice);
            } catch (Exception e) {
                // 绑定设备失败不影响登录，只记录日志
                log.error("绑定设备失败: " + e.getMessage());
            }
        }


        // 设置关联关系
        userPerson.setBusinessAccount(businessAccount);

        // 返回用户领域对象
        return userPerson;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCustomerAccount(CustomerAccount customerAccount) {
        try {
            customerAccount.setUpdatedAt(LocalDateTime.now());
            CustomerAccount saved = customerAccountRepository.save(customerAccount);
            return saved != null;
        } catch (Exception e) {
            log.error("更新客户账户信息失败", e);
            return false;
        }
    }
}
