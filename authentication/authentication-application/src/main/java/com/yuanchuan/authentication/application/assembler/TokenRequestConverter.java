package com.yuanchuan.authentication.application.assembler;

import com.yuanchuan.authentication.api.dto.TokenRequestDTO;
import com.yuanchuan.authentication.domain.model.TokenGenerationRequest;

/**
 * TokenRequest转换器
 * 用于在api层和domain层之间转换对象
 */
public class TokenRequestConverter {

    /**
     * 将TokenRequestDTO转换为TokenGenerationRequest
     *
     * @param dto TokenRequestDTO对象
     * @return TokenGenerationRequest对象
     */
    public static TokenGenerationRequest toDomain(TokenRequestDTO dto) {
        if (dto == null) {
            return null;
        }

        return TokenGenerationRequest.builder()
                .userId(dto.getUserId())
                .businessAccountId(dto.getBusinessAccountId())
                .deviceInfo(dto.getDeviceInfo())
                .username(dto.getUsername())
                .platform(dto.getPlatform())
                .deviceId(dto.getDeviceId())
                .roles(dto.getRoles())
                .permissions(dto.getPermissions())
                .build();
    }

    /**
     * 将TokenGenerationRequest转换为TokenRequestDTO
     *
     * @param domain TokenGenerationRequest对象
     * @return TokenRequestDTO对象
     */
    public static TokenRequestDTO toDTO(TokenGenerationRequest domain) {
        if (domain == null) {
            return null;
        }

        return TokenRequestDTO.builder()
                .userId(domain.getUserId())
                .username(domain.getUsername())
                .platform(domain.getPlatform())
                .deviceId(domain.getDeviceId())
                .roles(domain.getRoles())
                .permissions(domain.getPermissions())
                .build();
    }
}
