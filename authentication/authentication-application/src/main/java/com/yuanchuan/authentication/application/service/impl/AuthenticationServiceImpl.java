package com.yuanchuan.authentication.application.service.impl;

import com.alibaba.fastjson2.JSON;
import com.yuanchuan.authentication.api.dto.TokenDTO;
import com.yuanchuan.authentication.api.dto.TokenRequestDTO;
import com.yuanchuan.authentication.api.request.PhoneBindingRequest;
import com.yuanchuan.authentication.api.response.UserLoginEmailBindingVerifyResponse;
import com.yuanchuan.authentication.api.service.AuthenticationService;
import com.yuanchuan.authentication.application.assembler.TokenConverter;
import com.yuanchuan.authentication.application.assembler.TokenRequestConverter;
import com.yuanchuan.authentication.domain.model.Token;
import com.yuanchuan.authentication.domain.model.UserLoginEmailBindingVerify;
import com.yuanchuan.authentication.domain.service.TokenService;
import com.yuanchuan.common.context.UserContext;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@DubboService(version = "1.0", group = "${dubbo.group}", delay = -1, retries = -1, timeout = 600000)
public class AuthenticationServiceImpl implements AuthenticationService {


    @Autowired
    private TokenService tokenService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;


    @Override
    public TokenDTO generateToken(TokenRequestDTO tokenRequestDTO) {
        // 参数验证
        if (tokenRequestDTO == null) {
            throw new BusinessException(UsersErrorCode.TOKEN_REQUEST_EMPTY.getCode(), UsersErrorCode.TOKEN_REQUEST_EMPTY.getMsg());
        }
        if (tokenRequestDTO.getUserId() == null) {
            throw new BusinessException(UsersErrorCode.USER_ID_EMPTY.getCode(), UsersErrorCode.USER_ID_EMPTY.getMsg());
        }
        if (StringUtils.isBlank(tokenRequestDTO.getPlatform())) {
            throw new BusinessException(UsersErrorCode.PLATFORM_TYPE_EMPTY.getCode(), UsersErrorCode.PLATFORM_TYPE_EMPTY.getMsg());
        }
        if (StringUtils.isBlank(tokenRequestDTO.getUsername())) {
            throw new BusinessException(UsersErrorCode.USERNAME_EMPTY.getCode(), UsersErrorCode.USERNAME_EMPTY.getMsg());
        }

        // 将TokenRequestDTO转换为TokenGenerationRequest
        Token token = tokenService.generateToken(TokenRequestConverter.toDomain(tokenRequestDTO));
        return TokenConverter.toDTO(token);
    }

    @Override
    public TokenDTO refreshToken(String refreshToken) {
        Token token = tokenService.refreshToken(refreshToken);
        return TokenConverter.toDTO(token);
    }

    @Override
    public TokenDTO validateToken(String accessToken, String platformType) {
        if (StringUtils.isBlank(accessToken)) {
            throw new BusinessException(UsersErrorCode.TOKEN_EMPTY.getCode(), UsersErrorCode.TOKEN_EMPTY.getMsg());
        }
        if (StringUtils.isBlank(platformType)) {
            throw new BusinessException(UsersErrorCode.PLATFORM_TYPE_EMPTY.getCode(), UsersErrorCode.PLATFORM_TYPE_EMPTY.getMsg());
        }

        Token token = tokenService.validateToken(accessToken, platformType);
        if (token == null) {
            throw new BusinessException(UsersErrorCode.TOKEN_INVALID_OR_EXPIRED.getCode(), UsersErrorCode.TOKEN_INVALID_OR_EXPIRED.getMsg());
        }
        return TokenConverter.toDTO(token);
    }

    @Override
    public String createPhoneBinding(PhoneBindingRequest request) {
        // UserLoginEmailBindingVerify bindingVerify = new UserLoginEmailBindingVerify();
        // BeanUtils.copyProperties(request, bindingVerify);
        UserLoginEmailBindingVerify bindingVerify =
                JSON.parseObject(JSON.toJSONString(request), UserLoginEmailBindingVerify.class);

        return tokenService.createPhoneBinding(bindingVerify);
    }

    @Override
    public boolean verifyPhoneBinding(PhoneBindingRequest request) {
//        UserLoginEmailBindingVerify bindingVerify = new UserLoginEmailBindingVerify();
//        BeanUtils.copyProperties(request, bindingVerify);
        UserLoginEmailBindingVerify bindingVerify =
                JSON.parseObject(JSON.toJSONString(request), UserLoginEmailBindingVerify.class);

        return tokenService.verifyPhoneBinding(bindingVerify);
    }

    @Override
    public boolean phoneBindingStatusFlow(PhoneBindingRequest request) {
        UserLoginEmailBindingVerify bindingVerify = new UserLoginEmailBindingVerify();
        bindingVerify.setEmail(request.getEmail());
        bindingVerify.setPhone(request.getPhone());
        bindingVerify.setDeviceId(request.getDeviceId());
        bindingVerify.setBindingPhoneVerifyStatus(request.getBindingPhoneVerifyStatus());

        // 调用领域服务
        Map<String, Object> result = tokenService.phoneBindingStatusFlow(bindingVerify);
        if (result.isEmpty()) {
            return false;
        }
        // 解析JSON字符串为Map
        //return JSONObject.parseObject(value, Map.class);
        return true;
    }


    @Override
    public boolean verifyDeletePhoneBinding(PhoneBindingRequest request) {
        // 验证参数
        String token = request.getToken();
        if (StringUtils.isEmpty(token)) {
            return false;
        }

        // 调用领域服务删除令牌
        return tokenService.verifyDeletePhoneBinding(token);
    }

    @Override
    public UserLoginEmailBindingVerifyResponse getTokenVerifyInfo(String email) {
        if (StringUtils.isEmpty(email) ) {
            throw new BusinessException(UsersErrorCode.EMAIL_EMPTY_VERIFY.getCode(), UsersErrorCode.EMAIL_EMPTY_VERIFY.getMsg());
        }

        UserLoginEmailBindingVerify tokenVerifyInfo = tokenService.getTokenVerifyInfo(email);
        if (tokenVerifyInfo == null) {
            return null;
        }

//        UserLoginEmailBindingVerifyResponse response = new UserLoginEmailBindingVerifyResponse();
//        BeanUtils.copyProperties(tokenVerifyInfo, response);

        UserLoginEmailBindingVerifyResponse response =
                JSON.parseObject(JSON.toJSONString(tokenVerifyInfo), UserLoginEmailBindingVerifyResponse.class);
        return response;
    }

    @Override
    public void removeToken(Long userId, String deviceId) {
        tokenService.revokeToken(userId, deviceId);
    }

    @Override
    public UserContext parseTokenToUserContext(String token) {
        return tokenService.parseTokenToUserContext(token);
    }

    @Override
    public void revokeAllTokens(Long userId) {
        tokenService.revokeAllTokens(userId);
    }


}