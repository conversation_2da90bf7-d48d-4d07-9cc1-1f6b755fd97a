package com.yuanchuan.authentication.api.response;

import com.yuanchuan.authentication.api.dto.AuthAccountDeviceApiDTO;
import com.yuanchuan.authentication.api.dto.AuthThirdUserApiDTO;
import com.yuanchuan.authentication.context.enums.BindingPhoneVerifyStatus;
import com.yuanchuan.common.enums.users.login.PlatformType;
import lombok.Data;

import java.io.Serializable;

/**
 * 邮箱注册 -> 绑定手机号 -> 是否沿用情况 每一步需要安全验证
 */
@Data
public class UserLoginEmailBindingVerifyResponse implements Serializable {

    private static final long serialVersionUID = 2L;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 绑定手机号验证状态
     */
    private BindingPhoneVerifyStatus bindingPhoneVerifyStatus;

    /**
     * 创建时间
     */
    private Long createdTime;

    /**
     * IP地址
     */
    private String ipAddress;


    /**
     * 平台类型
     */
    private PlatformType platform;

    /**
     * 设备信息
     */
    private AuthAccountDeviceApiDTO authAccountDeviceDTO;

    /**
     * 三方认证信息
     */
    private AuthThirdUserApiDTO authThirdUserDTO;




}
