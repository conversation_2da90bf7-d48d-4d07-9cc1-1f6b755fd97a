package com.yuanchuan.authentication.api.request;

import com.yuanchuan.authentication.api.dto.AuthAccountDeviceApiDTO;
import com.yuanchuan.authentication.api.dto.AuthThirdUserApiDTO;
import com.yuanchuan.authentication.context.enums.BindingPhoneVerifyStatus;
import com.yuanchuan.common.enums.users.login.PlatformType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 邮箱注册绑定手机号请求
 */
@Data
@Schema(description = "邮箱注册绑定手机号请求")
public class PhoneBindingRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "邮箱地址", example = "<EMAIL>")
    private String email;

    @Schema(description = "手机号", example = "**********")
    private String phone;

    @Schema(description = "验证码", example = "123456")
    private String code;

    @Schema(description = "设备ID", example = "device-uuid-123")
    private String deviceId;

    @Schema(description = "临时令牌", example = "token-123456")
    private String token;

    @Schema(description = "绑定手机号验证状态")
    private BindingPhoneVerifyStatus bindingPhoneVerifyStatus;

    @Schema(description = "IP地址", example = "***********")
    private String ipAddress;

    @Schema(description = "用户代理", example = "Mozilla/5.0 (Windows NT 10.0; Win64; x64)")
    private String userAgent;

    @Schema(description = "平台类型")
    private PlatformType platform;

    @Schema(description = "设备信息对象")
    private AuthAccountDeviceApiDTO authAccountDeviceDTO;

    @Schema(description = "第三方认证信息")
    private AuthThirdUserApiDTO authThirdUserDomainDTO;


}
