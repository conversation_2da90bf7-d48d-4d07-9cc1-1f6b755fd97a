package com.yuanchuan.authentication.infrastructure.repository;

import com.alibaba.fastjson2.JSONObject;
import com.yuanchuan.authentication.domain.model.Token;
import com.yuanchuan.authentication.domain.repository.TokenRepository;
import com.yuanchuan.common.constant.user.UserLoginRedisKeys;
import com.yuanchuan.common.utils.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Repository
public class TokenRepositoryImpl implements TokenRepository {


    @Autowired
    private RedisUtil redisUtil;


    @Override
    public void save(Token token) {
        // 将Token对象转换为JSON字符串
        String tokenJson = JSONObject.toJSONString(token);

        // 计算过期时间（秒）
        long accessTokenExpireSeconds = java.time.Duration.between(
                java.time.LocalDateTime.now(), token.getExpiresAt()).getSeconds();
        long refreshTokenExpireSeconds = java.time.Duration.between(
                java.time.LocalDateTime.now(), token.getRefreshExpiresAt()).getSeconds();

        // 存储访问Token信息
        String accessTokenKey = UserLoginRedisKeys.TOKEN_KEY_PREFIX + "access:" + token.getAccessToken();
        redisUtil.set(accessTokenKey, tokenJson, accessTokenExpireSeconds, TimeUnit.SECONDS);

        // 存储刷新Token信息
        String refreshTokenKey = UserLoginRedisKeys.TOKEN_KEY_PREFIX + "refresh:" + token.getRefreshToken();
        redisUtil.set(refreshTokenKey, tokenJson, refreshTokenExpireSeconds, TimeUnit.SECONDS);

        // 存储用户设备Token映射
        // 根据平台类型处理deviceId为null的情况

        // 如果没有deviceId，根据平台类型处理
        String platform = token.getPlatform();
        if (!"CUSTOMER".equalsIgnoreCase(platform)) {
            return;
        }
        String deviceIdKey = token.getDeviceId();

        String userDeviceTokenKey = UserLoginRedisKeys.USER_TOKEN_KEY_PREFIX + token.getUserId() + ":" + deviceIdKey;
        redisUtil.hSet(userDeviceTokenKey, token.getAccessToken(), tokenJson);

        // 设置用户设备Token映射的过期时间
        redisUtil.expire(userDeviceTokenKey, refreshTokenExpireSeconds, TimeUnit.SECONDS);
    }

    @Override
    public Optional<Token> findByAccessToken(String accessToken) {
        String accessTokenKey = UserLoginRedisKeys.TOKEN_KEY_PREFIX + "access:" + accessToken;
        String tokenJson = redisUtil.get(accessTokenKey);

        if (tokenJson == null) {
            return Optional.empty();
        }

        try {
            Token token = JSONObject.parseObject(tokenJson, Token.class);
            return Optional.of(token);
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    @Override
    public Optional<Token> findByRefreshToken(String refreshToken) {
        String refreshTokenKey = UserLoginRedisKeys.TOKEN_KEY_PREFIX + "refresh:" + refreshToken;
        String tokenJson = redisUtil.get(refreshTokenKey);

        if (tokenJson == null) {
            return Optional.empty();
        }

        try {
            Token token = JSONObject.parseObject(tokenJson, Token.class);
            return Optional.of(token);
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    @Override
    public void deleteByUserIdAndDeviceId(Long userId, String deviceId) {
        // 如果没有提供设备ID，则删除所有该用户的令牌
        if (deviceId == null) {
            deleteAllByUserId(userId);
            return;
        }

        String userDeviceTokenKey = UserLoginRedisKeys.USER_TOKEN_KEY_PREFIX + userId + ":" + deviceId;

        // 获取用户设备的所有Token
        Map<Object, Object> tokenMap = redisUtil.hGetAll(userDeviceTokenKey);

        // 删除访问Token和刷新Token
        for (Object tokenJsonObj : tokenMap.values()) {
            String tokenJson = (String) tokenJsonObj;
            try {
                Token token = JSONObject.parseObject(tokenJson, Token.class);

                // 删除访问Token
                String accessTokenKey = UserLoginRedisKeys.TOKEN_KEY_PREFIX + "access:" + token.getAccessToken();
                redisUtil.delete(accessTokenKey);

                // 删除刷新Token
                String refreshTokenKey = UserLoginRedisKeys.TOKEN_KEY_PREFIX + "refresh:" + token.getRefreshToken();
                redisUtil.delete(refreshTokenKey);
            } catch (Exception e) {
                // 忽略解析错误
            }
        }

        // 删除用户设备Token映射
        redisUtil.delete(userDeviceTokenKey);
    }

    @Override
    public void deleteAllByUserId(Long userId) {
        // 获取用户所有设备的Token映射
        String pattern = UserLoginRedisKeys.USER_TOKEN_KEY_PREFIX + userId + ":*";
        Set<String> keys = redisUtil.keys(pattern);

        for (String userDeviceTokenKey : keys) {
            // 获取用户设备的所有Token
            Map<Object, Object> tokenMap = redisUtil.hGetAll(userDeviceTokenKey);

            // 删除访问Token和刷新Token
            for (Object tokenJsonObj : tokenMap.values()) {
                String tokenJson = (String) tokenJsonObj;
                try {
                    Token token = JSONObject.parseObject(tokenJson, Token.class);

                    // 删除访问Token
                    String accessTokenKey = UserLoginRedisKeys.TOKEN_KEY_PREFIX + "access:" + token.getAccessToken();
                    redisUtil.delete(accessTokenKey);

                    // 删除刷新Token
                    String refreshTokenKey = UserLoginRedisKeys.TOKEN_KEY_PREFIX + "refresh:" + token.getRefreshToken();
                    redisUtil.delete(refreshTokenKey);
                } catch (Exception e) {
                    // 忽略解析错误
                }
            }

            // 删除用户设备Token映射
            redisUtil.delete(userDeviceTokenKey);
        }
    }
}