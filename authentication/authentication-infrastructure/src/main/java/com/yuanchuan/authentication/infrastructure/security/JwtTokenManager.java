package com.yuanchuan.authentication.infrastructure.security;

import com.yuanchuan.common.context.UserContext;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class JwtTokenManager {
    @Value("${jwt.secret:your-secret-key}")
    private String secretKeyString;
    
    private SecretKey secretKey;
    
    @PostConstruct
    public void init() {
        this.secretKey = Keys.hmacShaKeyFor(secretKeyString.getBytes());
    }

    //@Value("${jwt.token-validity:7200}")
    private long tokenValidityInSeconds = ********;
    //@Value("${jwt.refresh-validity:604800}")
    private long refreshTokenValidityInSeconds = ********;



    public String generateAccessToken(Long userId, Long businessId, String username, String deviceId, String platform,
                                      List<String> roles, List<String> permissions, UserContext.AuthAccountDeviceApiDTO deviceInfo) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("uid", userId);
        claims.put("username", username);
        claims.put("device_id", deviceId);
        claims.put("platform", platform);
        claims.put("roles", roles);
        claims.put("permissions", permissions);
        claims.put("businessId", businessId);
        claims.put("deviceInfo", deviceInfo);

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime validity = now.plusSeconds(tokenValidityInSeconds);
        return Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()))
                //.setExpiration(Date.from(validity.atZone(ZoneId.systemDefault()).toInstant()))
                .setExpiration(Date.from(validity.atZone(ZoneId.systemDefault()).toInstant()))
                .signWith(secretKey, SignatureAlgorithm.HS256)
                .compact();
    }


    public String generateRefreshToken(Long userId, String deviceId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("uid", userId);
        claims.put("device_id", deviceId);
        claims.put("type", "refresh");

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime validity = now.plusSeconds(refreshTokenValidityInSeconds);

        return Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()))
                .setExpiration(Date.from(validity.atZone(ZoneId.systemDefault()).toInstant()))
                .signWith(secretKey, SignatureAlgorithm.HS256)
                .compact();
    }

    public Claims validateToken(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(secretKey)
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    public boolean isTokenExpired(Claims claims) {
        return claims.getExpiration().before(new Date());
    }

    public String getUserIdFromToken(Claims claims) {
        return claims.get("uid", String.class);
    }

    public String getDeviceIdFromToken(Claims claims) {
        return claims.get("device_id", String.class);
    }

    @SuppressWarnings("unchecked")
    public List<String> getRolesFromToken(Claims claims) {
        return claims.get("roles", List.class);
    }

    @SuppressWarnings("unchecked")
    public List<String> getPermissionsFromToken(Claims claims) {
        return claims.get("permissions", List.class);
    }
}