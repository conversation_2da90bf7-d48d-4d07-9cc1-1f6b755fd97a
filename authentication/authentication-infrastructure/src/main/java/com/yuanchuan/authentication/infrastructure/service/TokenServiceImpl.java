package com.yuanchuan.authentication.infrastructure.service;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yuanchuan.authentication.context.enums.BindingPhoneVerifyStatus;
import com.yuanchuan.authentication.domain.model.Token;
import com.yuanchuan.authentication.domain.model.TokenGenerationRequest;
import com.yuanchuan.authentication.domain.model.UserLoginEmailBindingVerify;
import com.yuanchuan.authentication.domain.repository.TokenRepository;
import com.yuanchuan.authentication.domain.service.TokenService;
import com.yuanchuan.authentication.infrastructure.security.JwtTokenManager;
import com.yuanchuan.common.context.UserContext;
import com.yuanchuan.common.constant.user.UserLoginRedisKeys;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.common.utils.RedisUtil;
import io.jsonwebtoken.Claims;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class TokenServiceImpl implements TokenService {
    private final TokenRepository tokenRepository;
    private final JwtTokenManager jwtTokenManager;

    @Autowired
    private RedisUtil redisUtil;


    public TokenServiceImpl(TokenRepository tokenRepository, JwtTokenManager jwtTokenManager) {
        this.tokenRepository = tokenRepository;
        this.jwtTokenManager = jwtTokenManager;
    }


    @Override
    public Token generateToken(TokenGenerationRequest request) {
        // 从请求对象中提取参数
        Long userId = request.getUserId();
        Long businessAccountId = request.getBusinessAccountId();
        String username = request.getUsername();
        String platform = request.getPlatform();
        Long accountDeviceId = request.getAccountDeviceId();
        String deviceId = request.getDeviceId();
        List<String> roles = request.getRoles();
        List<String> permissions = request.getPermissions();
        UserContext.AuthAccountDeviceApiDTO deviceInfo = request.getDeviceInfo();

        String accessToken = jwtTokenManager.generateAccessToken(
                userId, businessAccountId,username, deviceId, platform, roles, permissions,deviceInfo);
        String refreshToken = jwtTokenManager.generateRefreshToken(userId, deviceId);

        LocalDateTime now = LocalDateTime.now();
        Token token = Token.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .userId(userId)
                .businessAccountId(businessAccountId)
                .username(username) // 设置username属性
                .accountDeviceId(accountDeviceId)
                .deviceId(deviceId)
                .platform(platform)
                .roles(roles)
                .permissions(permissions)
                .deviceInfo(deviceInfo)
                .issuedAt(now)
                //.expiresAt(now.plusSeconds(7200)) // 2小时
                //.refreshExpiresAt(now.plusSeconds(604800)) // 7天
                .expiresAt(now.plusSeconds(********)) // 2小时
                .refreshExpiresAt(now.plusSeconds(********)) // 7天
                .build();

        tokenRepository.save(token);
        return token;
    }


    @Override
    public Token validateToken(String accessToken, String platformType) {
        // 强制要求提供platformType
        if (StringUtils.isEmpty(platformType)) {
            throw new BusinessException(UsersErrorCode.PLATFORM_TYPE_EMPTY.getCode(), UsersErrorCode.PLATFORM_TYPE_EMPTY.getMsg());
        }

        try {
            // 验证JWT签名和内容
            Claims claims = jwtTokenManager.validateToken(accessToken);

            // 验证JWT是否过期
            if (jwtTokenManager.isTokenExpired(claims)) {
                return null;
            }

            // 验证平台类型
            String tokenPlatform = claims.get("platform", String.class);
            if (!StringUtils.equals(tokenPlatform, platformType)) {
                return null;
            }

            // 从存储中获取Token对象
            return tokenRepository.findByAccessToken(accessToken)
                    .filter(token -> !token.isExpired())
                    .filter(token -> StringUtils.equals(token.getPlatform(), platformType))
                    .orElse(null);
        } catch (Exception e) {
            // JWT验证失败
            return null;
        }
    }

    @Override
    public Token refreshToken(String refreshToken) {
        try {
            // 验证JWT签名和内容
            Claims claims = jwtTokenManager.validateToken(refreshToken);

            // 验证JWT是否过期
            if (jwtTokenManager.isTokenExpired(claims)) {
                throw new BusinessException(UsersErrorCode.REFRESH_TOKEN_EXPIRED.getCode(), UsersErrorCode.REFRESH_TOKEN_EXPIRED.getMsg());
            }

            // 验证是否为刷新Token
            String tokenType = claims.get("type", String.class);
            if (!"refresh".equals(tokenType)) {
                throw new BusinessException(UsersErrorCode.INVALID_REFRESH_TOKEN.getCode(), UsersErrorCode.INVALID_REFRESH_TOKEN.getMsg());
            }

            Token oldToken = tokenRepository.findByRefreshToken(refreshToken)
                    .filter(token -> !token.isRefreshExpired())
                    .orElseThrow(() -> new BusinessException(UsersErrorCode.REFRESH_TOKEN_INVALID_OR_EXPIRED.getCode(), UsersErrorCode.REFRESH_TOKEN_INVALID_OR_EXPIRED.getMsg()));

            // 生成新的Token
            String newAccessToken = jwtTokenManager.generateAccessToken(
                    oldToken.getUserId(),
                    oldToken.getBusinessAccountId(),
                    oldToken.getUsername(),
                    oldToken.getDeviceId(),
                    oldToken.getPlatform(),
                    oldToken.getRoles(),
                    oldToken.getPermissions(),
                    oldToken.getDeviceInfo());
            String newRefreshToken = jwtTokenManager.generateRefreshToken(
                    oldToken.getUserId(),
                    oldToken.getDeviceId());

            LocalDateTime now = LocalDateTime.now();
            Token newToken = Token.builder()
                    .accessToken(newAccessToken)
                    .refreshToken(newRefreshToken)
                    .userId(oldToken.getUserId())
                    .username(oldToken.getUsername())
                    .accountDeviceId(oldToken.getAccountDeviceId()) // 复制accountDeviceId属性
                    .deviceId(oldToken.getDeviceId())
                    .platform(oldToken.getPlatform())
                    .roles(oldToken.getRoles())
                    .permissions(oldToken.getPermissions())
                    .issuedAt(now)
                    .expiresAt(now.plusSeconds(********)) // 与generate保持一致
                    .refreshExpiresAt(now.plusSeconds(********)) // 与generate保持一致
                    .build();

            // 保存新Token并删除旧Token
            tokenRepository.save(newToken);
            tokenRepository.deleteByUserIdAndDeviceId(oldToken.getUserId(), oldToken.getDeviceId());

            return newToken;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException(UsersErrorCode.REFRESH_TOKEN_VALIDATION_FAILED.getCode(), UsersErrorCode.REFRESH_TOKEN_VALIDATION_FAILED.getMsg());
        }
    }

    @Override
    public void revokeToken(Long userId, String deviceId) {
        tokenRepository.deleteByUserIdAndDeviceId(userId, deviceId);
    }

    @Override
    public void revokeAllTokens(Long userId) {
        tokenRepository.deleteAllByUserId(userId);
    }


    @Override
    public String createPhoneBinding(UserLoginEmailBindingVerify bindingVerify) {
        // 设置安全信息
        bindingVerify.setBindingPhoneVerifyStatus(BindingPhoneVerifyStatus.GENERATED);
        bindingVerify.setCreatedTime(System.currentTimeMillis());

        // 存储令牌和关联的数据到Redis
        String redisKey = UserLoginRedisKeys.getEmailBindingPhoneSecurityKey(bindingVerify.getEmail());
        String jsonString = JSONObject.toJSONString(bindingVerify);
        redisUtil.set(redisKey, jsonString, UserLoginRedisKeys.TOKEN_EXPIRE_MINUTES, TimeUnit.MINUTES);

        return bindingVerify.getPhone();
    }


    @Override
    public boolean verifyPhoneBinding(UserLoginEmailBindingVerify bindingVerify) {
        // 验证参数
        String email = bindingVerify.getEmail();
        // 获取令牌关联的数据
        String redisKey = UserLoginRedisKeys.getEmailBindingPhoneSecurityKey(email);
        String value = redisUtil.get(redisKey);
        // 不存在或已经被删除
        if (StringUtils.isEmpty(value)) {
            return false;
        }

        // 解析存储的数据
        UserLoginEmailBindingVerify storedVerify = JSONObject.parseObject(value, UserLoginEmailBindingVerify.class);

        // 检查令牌是否过期（例如，10分钟后过期）
        long currentTime = System.currentTimeMillis();
        if (currentTime - storedVerify.getCreatedTime() > UserLoginRedisKeys.TOKEN_EXPIRE_MINUTES * 60 * 1000) {
            // 删除过期令牌
            redisUtil.delete(redisKey);
            return false;
        }

        // 验证设备ID一致性
        //String deviceId = bindingVerify.getDeviceId();
        //if (StringUtils.isNotEmpty(deviceId) && !deviceId.equals(storedVerify.getDeviceId())) {
        //    return false;
        //}

        // 验证状态转换是否有效
        if (!isValidNextState(storedVerify.getBindingPhoneVerifyStatus(), bindingVerify.getBindingPhoneVerifyStatus())) {
            return false;
        }

        // 更新状态
        storedVerify.setBindingPhoneVerifyStatus(bindingVerify.getBindingPhoneVerifyStatus());
        if (StringUtils.isNotEmpty(bindingVerify.getPhone())) {
            storedVerify.setPhone(bindingVerify.getPhone());
        }

        // 存回 Redis
        //redisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(storedVerify),
        //                              UserLoginRedisKeys.TOKEN_EXPIRE_MINUTES, TimeUnit.MINUTES);

        return true;
    }



    @Override
    public Map<String, Object> phoneBindingStatusFlow(UserLoginEmailBindingVerify bindingVerify) {
        // 验证参数
        String email = bindingVerify.getEmail();

        // 获取令牌关联的数据
        String redisKey = UserLoginRedisKeys.getEmailBindingPhoneSecurityKey(email);
        String value = redisUtil.get(redisKey);

        // 不存在或已经被删除
        if (StringUtils.isEmpty(value)) {
            return new HashMap<>();
        }

        // 解析存储的数据
        UserLoginEmailBindingVerify newStoredVerify = JSONObject.parseObject(value, UserLoginEmailBindingVerify.class);

        // 检查令牌是否过期
        long currentTime = System.currentTimeMillis();
        if (currentTime - newStoredVerify.getCreatedTime() > UserLoginRedisKeys.TOKEN_EXPIRE_MINUTES * 60 * 1000) {
            // 删除过期令牌
            redisUtil.delete(redisKey);
            return new HashMap<>();
        }
        BindingPhoneVerifyStatus bindingPhoneVerifyStatus = bindingVerify.getBindingPhoneVerifyStatus();
        newStoredVerify.setBindingPhoneVerifyStatus(bindingPhoneVerifyStatus);
        String jsonString = JSONObject.toJSONString(newStoredVerify);

        // 获取当前的过期时间（秒）
        Long currentExpireSeconds = redisUtil.getExpire(redisKey);

        // 更新数据
        redisUtil.set(redisKey, jsonString);

        // 如果之前有过期时间，重新设置相同的过期时间
        if (currentExpireSeconds != null && currentExpireSeconds > 0) {
            redisUtil.expire(redisKey, currentExpireSeconds, TimeUnit.SECONDS);
        }else {
            redisUtil.expire(redisKey,UserLoginRedisKeys.TOKEN_EXPIRE_MINUTES, TimeUnit.MINUTES);
        }

        // 转换为 Map 返回，不包含敏感信息
        Map<String, Object> result = new HashMap<>();
        result.put("email", newStoredVerify.getEmail());
        result.put("phone", newStoredVerify.getPhone());
        result.put("deviceInfo", newStoredVerify.getAuthAccountDeviceDTO());
        result.put("platform", newStoredVerify.getPlatform());
        result.put("bindingPhoneVerifyStatus", bindingPhoneVerifyStatus);

        return result;
    }


    @Override
    public boolean verifyDeletePhoneBinding(String token) {
        if (StringUtils.isEmpty(token)) {
            return false;
        }

        // 获取令牌关联的数据
        String redisKey = UserLoginRedisKeys.EMAIL_BINDING_PHONE_SECURITY_KEY + token;
        String value = redisUtil.get(redisKey);

        // 不存在或已经被删除
        if (StringUtils.isEmpty(value)) {
            return false;
        }

        // 删除令牌
        redisUtil.delete(redisKey);
        return true;
    }

    @Override
    public UserContext parseTokenToUserContext(String token) {
        Claims claims = jwtTokenManager.validateToken(token);

        ObjectMapper objectMapper = new ObjectMapper();

        UserContext.AuthAccountDeviceApiDTO deviceInfo = null;
        Object rawDeviceInfo = claims.get("deviceInfo");
        if (rawDeviceInfo instanceof Map) {
            deviceInfo = objectMapper.convertValue(rawDeviceInfo, UserContext.AuthAccountDeviceApiDTO.class);
        }


        return new UserContext(
                claims.get("uid", Long.class),
                claims.get("businessId", Long.class),
                deviceInfo,
                claims.get("username", String.class),
                claims.get("device_id", String.class),
                claims.get("platform", String.class),
                (List<String>) claims.get("roles"),
                (List<String>) claims.get("permissions")

        );



    }

    @Override
    public UserLoginEmailBindingVerify getTokenVerifyInfo(UserLoginEmailBindingVerify bindingVerify) {
        // 验证参数
        String email = bindingVerify.getEmail();
        String phone = bindingVerify.getPhone();

        // 优先使用邮箱作为键
        String redisKey = null;
        if (StringUtils.isNotEmpty(email)) {
            redisKey = UserLoginRedisKeys.getEmailBindingPhoneSecurityKey(email);
        } else if (StringUtils.isNotEmpty(phone)) {
            redisKey = UserLoginRedisKeys.getEmailBindingPhoneSecurityKey(phone);
        } else {
            return null; // 没有提供有效的标识
        }

        // 处理deviceId为null的情况
        if (bindingVerify.getDeviceId() == null) {
            // 根据平台类型处理
            if (bindingVerify.getPlatform() != null) {
                String platform = bindingVerify.getPlatform().toString();
                if ("CUSTOMER".equalsIgnoreCase(platform)) {
                    // C端用户应该提供设备ID
                    bindingVerify.setDeviceId("customer-default");
                } else {
                    // 商家后台或管理后台使用平台类型作为标识
                    bindingVerify.setDeviceId(platform.toLowerCase() + "-default");
                }
            } else {
                // 如果没有平台信息，使用通用默认值
                bindingVerify.setDeviceId("unknown-default");
            }
        }

        // 获取令牌关联的数据
        String value = redisUtil.get(redisKey);

        // 不存在或已经被删除
        if (StringUtils.isEmpty(value)) {
            return null;
        }

        try {
            // 解析存储的数据
            UserLoginEmailBindingVerify storedVerify = JSONObject.parseObject(value, UserLoginEmailBindingVerify.class);

            // 检查令牌是否过期
            long currentTime = System.currentTimeMillis();
            if (currentTime - storedVerify.getCreatedTime() > UserLoginRedisKeys.TOKEN_EXPIRE_MINUTES * 60 * 1000) {
                // 删除过期令牌
                redisUtil.delete(redisKey);
                return null;
            }

            return storedVerify;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public UserLoginEmailBindingVerify getTokenVerifyInfo(String email) {
        // 创建一个绑定验证对象
        UserLoginEmailBindingVerify bindingVerify = new UserLoginEmailBindingVerify();
        bindingVerify.setEmail(email);
        return getTokenVerifyInfo(bindingVerify);
    }




    /**
     * 验证状态转换是否有效
     */
    private boolean isValidNextState(BindingPhoneVerifyStatus currentState, BindingPhoneVerifyStatus nextState) {
        // 定义有效的状态转换
        switch (currentState) {
            case INIT:
                return nextState == BindingPhoneVerifyStatus.R_EMAIL_VERIFIED ||
                        nextState == BindingPhoneVerifyStatus.R_THIRD_PARTY_AUTHORIZED;
            case GENERATED:
                return nextState == BindingPhoneVerifyStatus.R_EMAIL_VERIFIED ||
                        nextState == BindingPhoneVerifyStatus.R_THIRD_PARTY_AUTHORIZED;
            case BINDING_PHONE_VERIFIED:
                return nextState == BindingPhoneVerifyStatus.PHONE_EXISTS ||
                     nextState == BindingPhoneVerifyStatus.R_COMPLETED;


            default:
                return false;
        }
    }
}