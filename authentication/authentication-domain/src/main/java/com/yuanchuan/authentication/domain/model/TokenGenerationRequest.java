package com.yuanchuan.authentication.domain.model;

import com.yuanchuan.common.context.UserContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Token生成请求
 * 封装生成token所需的参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TokenGenerationRequest implements Serializable {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * b端账号id
     */
    private Long businessAccountId;
    /**
     * 用户名
     */
    private String username;

    /**
     * 平台
     */
    private String platform;

    private UserContext.AuthAccountDeviceApiDTO deviceInfo;

    /**
     * 设备信息
     */
    private String deviceId;

    /**
     * 角色列表
     */
    private List<String> roles;

    /**
     * 权限列表
     */
    private List<String> permissions;

    /**
     * 设备信息主键id
     */
    private Long accountDeviceId;
}
