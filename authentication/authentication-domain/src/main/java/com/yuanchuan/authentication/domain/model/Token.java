package com.yuanchuan.authentication.domain.model;

import com.yuanchuan.common.context.UserContext;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
public class Token implements Serializable {
    private static final long serialVersionUID = 1L;
    private String accessToken;
    private String refreshToken;
    private Long userId;
    private Long businessAccountId;
    private String username;
    private Long accountDeviceId;
    private String deviceId;
    private UserContext.AuthAccountDeviceApiDTO deviceInfo;
    private String platform;
    private List<String> roles;
    private List<String> permissions;
    private LocalDateTime issuedAt;
    private LocalDateTime expiresAt;
    private LocalDateTime refreshExpiresAt;

    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiresAt);
    }

    public boolean isRefreshExpired() {
        return LocalDateTime.now().isAfter(refreshExpiresAt);
    }

    public boolean isNearExpiration() {
        return LocalDateTime.now().plusMinutes(30).isAfter(expiresAt);
    }
}