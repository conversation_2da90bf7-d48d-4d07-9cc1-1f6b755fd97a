package com.yuanchuan.authentication.context.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 邮箱需要绑定手机号 临时令牌的状态枚举
 */
@Schema(description = "绑定手机号验证状态")
public enum BindingPhoneVerifyStatus {
    /**
     * 初始状态
     */
    INIT(0, "初始状态"),

    /**
     * 已生成
     */
    GENERATED(1, "已生成"),

    /**
     * 邮箱验证码已验证，等待绑定手机号
     */
    R_EMAIL_VERIFIED(2, "邮箱已验证未注册，等待绑定手机号"),

    /**
     * 第三方账号已授权，等待绑定手机号
     */
    R_THIRD_PARTY_AUTHORIZED(3, "第三方账号已授权未注册，等待绑定手机号"),

    /**
     * 手机号已经 验证
     */
    BINDING_PHONE_VERIFIED(4,  "手机号已验证"),
    /**
     * 手机号已存在账号，等待用户决策是否沿用
     */
    PHONE_EXISTS(5, "手机号已存在账号，等待决策是否沿用"),

    /**
     * 流程完成
     */
    R_COMPLETED(6, "流程完成");


    /**
     * 编码
     */
    private final int code;

    /**
     * 中文描述
     */
    private final String description;

    BindingPhoneVerifyStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取编码
     */
    @JsonValue
    public int getCode() {
        return code;
    }

    /**
     * 获取中文描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据编码获取枚举实例
     */
    @JsonCreator
    public static BindingPhoneVerifyStatus fromCode(int code) {
        for (BindingPhoneVerifyStatus status : BindingPhoneVerifyStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("無效的綁定手機號碼驗證狀態編碼: " + code);
    }
}
