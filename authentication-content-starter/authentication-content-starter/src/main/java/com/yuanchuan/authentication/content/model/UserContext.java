package com.yuanchuan.authentication.content.model;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * User context model that holds authenticated user information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, property = "@class") // 允许安全反序列化
public class UserContext implements Serializable {
    private Long userId;
    private String businessAccountId;
    private AuthAccountDeviceApiDTO deviceInfo;
    private String username;
    private String deviceId;
    private String platform;
    private List<String> roles;
    private List<String> permissions;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AuthAccountDeviceApiDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        private String deviceId;
        private String deviceName;
        private String deviceType;
        private String osVersion;
        private String appVersion;
        private Integer status;
        private Long accountId;
        private String ipAddress;
        private String userAgent;
        private LocalDateTime loginTime;
    }
}
