# File 電商微服務項目 - 文件管理服務
包路径：com.yuanchuan.file

### 服務基本信息

- **服務名稱**: file-service
- **服務類型**: Dubbo RPC 服務 + Spring Boot Web 服務
- **主要端口**:
  - Spring Boot Web 端口: `8092` (應用管理)
  - Dubbo RPC 端口: `20807` (服務間通信)

### 健康檢查配置

本服務是混合型服務，既提供Dubbo RPC接口，也提供Spring Boot Web接口，健康檢查可以採用以下方式：

#### 1. Dubbo QoS 健康檢查（推薦）
項目已啟用Dubbo QoS功能，可以使用更精確的健康檢查：

```yaml
# K8s健康檢查配置
livenessProbe:
  httpGet:
    path: /live
    port: 22222
  initialDelaySeconds: 90
  periodSeconds: 30
  timeoutSeconds: 5
  failureThreshold: 3

readinessProbe:
  httpGet:
    path: /ready  
    port: 22222
  initialDelaySeconds: 60
  periodSeconds: 20
  timeoutSeconds: 5
  failureThreshold: 3
```

#### 2. Spring Boot Actuator 健康檢查（備選方案）
```yaml
# K8s Deployment 中的健康檢查配置
livenessProbe:
  httpGet:
    path: /actuator/health
    port: 8092
  initialDelaySeconds: 60
  periodSeconds: 30
  timeoutSeconds: 5
  failureThreshold: 3

readinessProbe:
  httpGet:
    path: /actuator/health/readiness
    port: 8092
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 3
  failureThreshold: 3
```

#### 3. TCP 端口檢查（最簡方案）
```yaml
# K8s Deployment 中的健康檢查配置
livenessProbe:
  tcpSocket:
    port: 20807
  initialDelaySeconds: 60
  periodSeconds: 30
  timeoutSeconds: 5
  failureThreshold: 3

readinessProbe:
  tcpSocket:
    port: 20807
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 3
  failureThreshold: 3
```

### Service 配置

```yaml
apiVersion: v1
kind: Service
metadata:
  name: file-service
  namespace: default
  labels:
    app: file
spec:
  selector:
    app: file
  ports:
  - name: dubbo
    port: 20807
    targetPort: 20807
    protocol: TCP
  - name: qos
    port: 22222
    targetPort: 22222
    protocol: TCP
  - name: http
    port: 8092
    targetPort: 8092
    protocol: TCP
  type: ClusterIP
```

### Deployment 配置示例

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: file
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: file
  template:
    metadata:
      labels:
        app: file
    spec:
      containers:
      - name: file
        image: frchacrdev.azurecr.io/file:latest
        ports:
        - containerPort: 8092
          name: http
        - containerPort: 20807
          name: dubbo
        - containerPort: 22222
          name: qos
        # Dubbo QoS健康檢查（推薦）
        livenessProbe:
          httpGet:
            path: /live
            port: 22222
          initialDelaySeconds: 90
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 22222
          initialDelaySeconds: 60
          periodSeconds: 20
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1"
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "default"
        - name: SPRING_CONFIG_LOCATION
          value: "file:/app/config/application.yml"
        - name: JAVA_OPTS
          value: "-Xmx768m -Xms512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: config-volume
        configMap:
          name: file-config
```

### 服務發現和負載均衡

由於是混合型服務，支持多種訪問方式：

1. **Zookeeper註冊中心**: Dubbo服務自動註冊到Zookeeper
2. **服務消費**: 其他服務通過Dubbo客戶端調用RPC接口
3. **HTTP訪問**: 內部服務可通過Spring Boot Web接口訪問
4. **負載均衡**: Dubbo內置負載均衡策略 + K8s Service負載均衡

### 文件存儲配置

#### Azure Blob Storage 配置
```yaml
azure:
  storage:
    connection-string: DefaultEndpointsProtocol=https;AccountName=...
    container:
      public: dev    # 公開訪問的容器
      private: dev-private  # 私有訪問的容器
    cdn-host: https://your-cdn.azureedge.net  # CDN加速（可選）
```

#### 文件上傳限制
```yaml
file:
  upload:
    max-file-size: 100MB
    max-request-size: 100MB
    allowed-extensions: jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,txt
    temp-dir: /tmp/uploads
  storage:
    type: azure-blob  # azure-blob, local, oss
    base-url: https://qlqj2.blob.core.windows.net
```

### 監控和觀測

#### 1. Dubbo Admin 監控
```yaml
# 可以部署Dubbo Admin來監控服務狀態
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dubbo-admin
spec:
  # ... dubbo-admin配置
```

#### 2. Spring Boot Actuator 監控
```yaml
# Actuator端點配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
```

#### 3. 日誌收集
```yaml
# 通過sidecar或日誌收集器收集應用日誌
volumes:
- name: logs
  emptyDir: {}
volumeMounts:
- name: logs
  mountPath: /app/logs
```

### 注意事項

1. **混合服務**: 既提供Dubbo RPC接口，也提供Spring Boot Web接口
2. **健康檢查**: 已啟用Dubbo QoS，推薦使用QoS健康檢查，更精確可靠
3. **文件存儲**: 集成Azure Blob Storage，支持公開和私有容器
4. **安全檢查**: 集成Azure Content Safety API，自動檢測不當內容
5. **服務間通信**: 通過Dubbo RPC進行，HTTP接口主要用於內部管理
6. **監控**: 可通過Dubbo Admin、QoS端點、Actuator、日誌、JMX等方式監控
7. **擴縮容**: 基於CPU/內存使用率或自定義指標進行HPA配置
8. **QoS功能**: 已啟用，端口22222，僅允許本地訪問（安全考慮）

### QoS 運維命令

啟用QoS後，可以通過以下命令進行運維操作：

```bash
# 在Pod內執行以下命令

# 查看服務健康狀態
curl http://localhost:22222/live
curl http://localhost:22222/ready

# 查看所有服務
curl http://localhost:22222/ls

# 查看服務詳情
curl http://localhost:22222/ls -l

# 上線/下線服務
curl http://localhost:22222/online com.yuanchuan.file.api.service.FileService
curl http://localhost:22222/offline com.yuanchuan.file.api.service.FileService

# 查看配置信息
curl http://localhost:22222/getConfig
```

### 核心功能接口

#### 1. 文件上傳接口（RPC）
```java
@Service
public interface FileService {
    // 上傳文件
    FileUploadResponse uploadFile(FileUploadRequest request);
    
    // 獲取文件信息
    FileInfo getFileInfo(String fileId);
    
    // 刪除文件
    void deleteFile(String fileId);
    
    // 生成預簽名URL
    String generatePresignedUrl(String fileId, int expireSeconds);
}
```

#### 2. 文件管理接口（HTTP）
```bash
# 文件上傳
POST /api/files/upload

# 文件下載
GET /api/files/{fileId}/download

# 文件信息
GET /api/files/{fileId}

# 文件列表
GET /api/files?page=1&size=10

# 刪除文件
DELETE /api/files/{fileId}
```

### 相關服務依賴

- **Zookeeper**: 服務註冊中心 (需要在K8s中部署或使用外部服務)
- **MySQL**: 數據庫連接 (需要配置Service)
- **Redis**: 緩存服務 (需要配置Service)  
- **Azure Blob Storage**: 文件存儲服務 (外部服務)
- **Azure Service Bus**: 消息隊列 (外部服務)
- **Azure Content Safety**: 內容安全檢查 (外部服務)

### 環境變量配置

```yaml
env:
- name: SPRING_PROFILES_ACTIVE
  value: "prod"
- name: DUBBO_PORT
  value: "20807"
- name: DUBBO_QOS_PORT
  value: "22222"
- name: SERVER_PORT
  value: "8092"
- name: ZOOKEEPER_ADDRESS
  value: "zookeeper-service:2181"
- name: AZURE_STORAGE_CONNECTION_STRING
  valueFrom:
    secretKeyRef:
      name: azure-storage-secret
      key: connection-string
```

### 部署命令

```bash
# 一鍵部署
source deploy-config.sh && deploy_app

# 分步部署
source deploy-config.sh
check_tools && check_azure_login
mvn clean package -DskipTests
az acr build --registry $ACR_NAME --image $IMAGE_NAME:$IMAGE_TAG .
kubectl apply -f k8s/
kubectl rollout status deployment/file
```