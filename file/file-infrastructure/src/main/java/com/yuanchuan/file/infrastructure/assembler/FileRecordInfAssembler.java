package com.yuanchuan.file.infrastructure.assembler;

import com.yuanchuan.common.mapstruct.InfrastructureMapstruct;
import com.yuanchuan.file.infrastructure.po.FileRecordPO;
import com.yuanchuan.file.domain.model.FileRecord;
import org.mapstruct.Mapper;
import org.mapstruct.Named;

/**
* 文件记录Mapstruct
*
* @<NAME_EMAIL>
* @since 1.0.0 2025-05-15
*/
@Named("fileRecordInfAssembler")
@Mapper(componentModel = "spring")
public interface FileRecordInfAssembler extends InfrastructureMapstruct<FileRecord, FileRecordPO> {
}
