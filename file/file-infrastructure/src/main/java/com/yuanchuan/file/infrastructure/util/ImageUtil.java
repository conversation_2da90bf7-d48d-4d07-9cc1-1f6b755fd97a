package com.yuanchuan.file.infrastructure.util;

import com.yuanchuan.file.api.query.ReSizePictureQuery;
import net.coobird.thumbnailator.Thumbnails;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

/**
 * 图片工具类
 */
public class ImageUtil {

    private static final List<String> IMAGE_LIST = Arrays.asList("jpg", "jpeg", "png", "gif", "bmp", "svg");

    /**
     * 是否为图片
     */
    public static boolean checkImage(String fileType) {
        return IMAGE_LIST.contains(fileType);
    }

    public static InputStream compressImage(byte[] bytes, double scale, double quality) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        //缩放图片
        Thumbnails.of(new ByteArrayInputStream(bytes))
                //设置缩放比例
                .scale(scale)
                //设置图片质量
                .outputQuality(quality)
                .toOutputStream(outputStream);
        return new ByteArrayInputStream(outputStream.toByteArray());
    }

    /**
     * 缩放图片
     */
    public static InputStream resizeImage(ReSizePictureQuery query,
                                          InputStream inputStream) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        switch (query.getMode()) {
            // 单边固定缩略
            case 0:
                if (query.getWidth() != null) {
                    Thumbnails.of(inputStream)
                            .width(query.getWidth())
                            .keepAspectRatio(true)
                            .toOutputStream(outputStream);
                } else if (query.getHeight() != null) {
                    Thumbnails.of(inputStream)
                            .height(query.getHeight())
                            .keepAspectRatio(true)
                            .toOutputStream(outputStream);
                } else {
                    throw new IllegalArgumentException("Width or Height must be provided for single side scaling.");
                }
                break;
            // 指定宽高缩略
            case 1:
                if (query.getWidth() == null && query.getHeight() == null) {
                    throw new IllegalArgumentException("Both Width and Height must be provided for specified size scaling.");
                }
                Thumbnails.of(inputStream)
                        .size(query.getWidth(), query.getHeight())
                        .keepAspectRatio(query.getDirection() == 0)
                        .toOutputStream(outputStream);
                break;
            // 强制宽高缩略
            case 2:
                Thumbnails.of(inputStream)
                        .size(query.getWidth(), query.getHeight())
                        .keepAspectRatio(false)
                        .toOutputStream(outputStream);
                break;
            // 按比例缩放
            case 3:
                Thumbnails.of(inputStream)
                        .scale(query.getScale())
                        .outputQuality(query.getQuality() != null ? query.getQuality() : 1.0D)
                        .toOutputStream(outputStream);
                break;
            default:
                throw new IllegalArgumentException("Unsupported scaling mode.");
        }
        return new ByteArrayInputStream(outputStream.toByteArray());
    }

    /**
     * 判断该文件类型是否为pdf格式
     * @param fileType
     * @return
     */
    public static boolean isPdfFile(String fileType) {
        if (fileType == null || fileType.isEmpty()) {
            return false;
        }
        // 检查文件扩展名是否为 pdf（忽略大小写）
        return fileType.equalsIgnoreCase("pdf");
    }



    /**
     * 用来 pdf 合并的，用于文件上传时，pdf 转 jpg
     * @param images
     * @return
     */
    public static BufferedImage concatenateImages(List<BufferedImage> images) {
        int totalWidth = images.get(0).getWidth();
        int totalHeight = images.stream().mapToInt(BufferedImage::getHeight).sum();

        BufferedImage concatenatedImage = new BufferedImage(totalWidth, totalHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = concatenatedImage.createGraphics();

        int yOffset = 0;
        for (BufferedImage image : images) {
            g2d.drawImage(image, 0, yOffset, null);
            yOffset += image.getHeight();
        }
        g2d.dispose();

        return concatenatedImage;
    }

}
