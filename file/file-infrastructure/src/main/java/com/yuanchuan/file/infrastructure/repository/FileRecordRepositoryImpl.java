package com.yuanchuan.file.infrastructure.repository;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.azure.ai.contentsafety.ContentSafetyClient;
import com.azure.ai.contentsafety.models.*;
import com.azure.core.util.BinaryData;
import com.azure.storage.blob.BlobClient;
import com.azure.storage.blob.BlobContainerClient;
import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.blob.models.BlobHttpHeaders;
import com.azure.storage.blob.models.BlobProperties;
import com.azure.storage.blob.models.BlobStorageException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yuanchuan.common.exception.ServiceException;
import com.yuanchuan.file.api.dto.FileData;
import com.yuanchuan.file.api.dto.FileDownloadResult;
import com.yuanchuan.file.api.query.ReSizePictureQuery;
import com.yuanchuan.file.api.vo.FileRecordVO;
import com.yuanchuan.file.api.vo.SafetyCategoryDetailVO;
import com.yuanchuan.file.api.vo.SafetyResultVO;
import com.yuanchuan.file.domain.model.FileRecord;
import com.yuanchuan.file.domain.repository.FileRecordRepository;
import com.yuanchuan.file.infrastructure.assembler.FileRecordInfAssembler;
import com.yuanchuan.file.infrastructure.config.AzureContentSafetyConfig;
import com.yuanchuan.file.infrastructure.mapper.FileRecordMapper;
import com.yuanchuan.file.infrastructure.po.FileRecordPO;
import com.yuanchuan.file.infrastructure.util.AzureStorageUtil;
import com.yuanchuan.file.infrastructure.util.FileProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.rmi.ServerException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

import static com.yuanchuan.file.infrastructure.util.FileValidationUtil.validateFile;

/**
 * 文件记录存储实现
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-05-15
 */
@Slf4j
@Repository
public class FileRecordRepositoryImpl extends ServiceImpl<FileRecordMapper, FileRecordPO>
        implements FileRecordRepository {

    private final FileRecordMapper fileRecordMapper;
    private final FileRecordInfAssembler fileRecordAssembler;
    private final BlobServiceClient blobServiceClient;
//    private final ContentSafetyClient contentSafetyClient;
    private final FileProcessor fileProcessor;
    private final AzureStorageUtil azureStorageUtil;

    @Value("${azure.storage.container.public}")
    private String publicContainerName;

    @Value("${azure.storage.container.private}")
    private String privateContainerName;

    @Value("${azure.storage.cdn-host}")
    private String cdnHost;

    @Value("${file.max-pdf-size:20971520}") // 20MB default
    private long maxPdfSize;

    @Value("${file.max-pdf-pages:3}")
    private int maxPdfPages;

    @Autowired
    public FileRecordRepositoryImpl(FileRecordMapper fileRecordMapper,
                                    FileRecordInfAssembler fileRecordAssembler,
                                    BlobServiceClient blobServiceClient,
//                                    ContentSafetyClient contentSafetyClient,
                                    FileProcessor fileProcessor,
                                    AzureStorageUtil azureStorageUtil) {
        this.fileRecordMapper = fileRecordMapper;
        this.fileRecordAssembler = fileRecordAssembler;
        this.blobServiceClient = blobServiceClient;
//        this.contentSafetyClient = contentSafetyClient;
        this.fileProcessor = fileProcessor;
        this.azureStorageUtil = azureStorageUtil;
    }

    @Autowired
    private ContentSafetyClient contentSafetyClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileRecord upload(FileData file, Integer isOpen, String businessType) throws IOException {
        // 参数校验
        validateFile(file);

        String fileName = file.getOriginalFilename();
        String fileType = FileUtil.extName(fileName);
        String contentType = file.getContentType();

        // 处理PDF文件
        if (fileProcessor.isPdfFile(fileType)) {
            validatePdfFile(file);
            FileData processedFile = fileProcessor.processPdf(file, maxPdfPages);
            return uploadProcessedFile(processedFile, isOpen, businessType, true);
        }

        // 处理大图片文件
        if (fileProcessor.isImageFile(fileType) && file.getSize() > 1024 * 1024 * 2) {
            FileData compressedFile = fileProcessor.compressImage(file, 0.8D, 0.8D);
            return uploadProcessedFile(compressedFile, isOpen, businessType, false);
        }

        // 普通文件上传
        return uploadProcessedFile(file, isOpen, businessType, false);
    }

    private FileRecord uploadProcessedFile(FileData file, Integer isOpen, String businessType, boolean isPdf)
            throws IOException {

        String containerName = isOpen.equals(1) ? publicContainerName : privateContainerName;
        String fileType = FileUtil.extName(file.getOriginalFilename());
        String uuid = IdUtil.fastSimpleUUID();
        String fileUuidName = uuid + "." + fileType;
        String blobName = azureStorageUtil.buildBlobName(businessType, uuid, fileType);

        try {
            // 上传到Azure Blob Storage
            BlobClient blobClient = azureStorageUtil.uploadToBlobStorage(
                    blobServiceClient,
                    containerName,
                    blobName,
                    new ByteArrayInputStream(file.getFileBytes()),
                    file.getSize(),
                    file.getContentType()
            );

            // 保存文件记录
            FileRecordPO fileRecordPO = createFileRecordPO(
                    file.getOriginalFilename(),
                    fileUuidName,
                    fileType,
                    file.getSize(),
                    containerName,
                    blobName,
                    blobClient.getBlobUrl(),
                    isOpen,
                    businessType
            );

            fileRecordMapper.insert(fileRecordPO);
            return fileRecordAssembler.POToModel(fileRecordPO);

        } catch (BlobStorageException e) {
            log.error("Failed to upload file to Azure Blob Storage", e);
            throw new ServiceException("Failed to upload file");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateImg(FileData file, Integer isOpen, String fileUuidName, String businessType)
            throws IOException {

        validateFile(file);
        String containerName = isOpen.equals(1) ? publicContainerName : privateContainerName;
        String fileName = file.getOriginalFilename();
        String fileType = FileUtil.extName(fileName);
        String uuid = removeLastDotAndExtension(fileUuidName);
        String blobName = azureStorageUtil.buildBlobName(businessType, uuid, fileType);

        try {
            azureStorageUtil.uploadToBlobStorage(
                    blobServiceClient,
                    containerName,
                    blobName,
                    new ByteArrayInputStream(file.getFileBytes()),
                    file.getSize(),
                    file.getContentType()
            );

            log.info("File updated successfully, container: {}, blob: {}", containerName, blobName);
        } catch (BlobStorageException e) {
            log.error("Failed to update file", e);
            throw new ServiceException("Failed to update file");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByFileUuidName(String fileUuidName) {
        FileRecordPO fileRecordPO = getFileRecordByUuidName(fileUuidName);

        try {
            azureStorageUtil.deleteFromBlobStorage(
                    blobServiceClient,
                    fileRecordPO.getBucketName(),
                    fileRecordPO.getFileKey()
            );

            fileRecordMapper.delete(Wrappers.<FileRecordPO>lambdaQuery()
                    .eq(FileRecordPO::getFileUuidName, fileUuidName));

        } catch (BlobStorageException e) {
            log.error("Failed to delete file", e);
            throw new ServiceException("Failed to delete file");
        }
    }

    @Override
    public FileRecordVO getFileByFileUuidName(String fileUuidName) {
        FileRecordPO fileRecordPO = getFileRecordByUuidName(fileUuidName);
        // 转换PO为VO逻辑
        return null;
    }

    @Override
    public FileDownloadResult download(String fileUuidName) throws IOException {
        FileRecordPO fileRecordPO = getFileRecordByUuidName(fileUuidName);

        try {
            BlobClient blobClient = blobServiceClient.getBlobContainerClient(fileRecordPO.getBucketName())
                    .getBlobClient(fileRecordPO.getFileKey());

            if (!blobClient.exists()) {
                throw new ServiceException("File not found");
            }

            try (InputStream inputStream = blobClient.openInputStream()) {
                BlobProperties properties = blobClient.getProperties();

                return FileDownloadResult.builder()
                        .fileName(fileRecordPO.getFileName())
                        .fileContent(IOUtils.toByteArray(inputStream))
                        .contentType(properties.getContentType())
                        .build();
            }
        } catch (BlobStorageException e) {
            log.error("Failed to download file", e);
            throw new ServiceException("Failed to download file");
        }
    }

    @Override
    public FileDownloadResult resizePicture(ReSizePictureQuery reSizePictureQuery) throws IOException {
        FileRecordPO fileRecordPO = getFileRecordByUuidName(reSizePictureQuery.getFileUuidName());

        if (!fileProcessor.isImageFile(FileUtil.extName(fileRecordPO.getFileUuidName()))) {
            throw new ServiceException("Not an image type, unable to process");
        }

        try {
            BlobClient blobClient = blobServiceClient.getBlobContainerClient(fileRecordPO.getBucketName())
                    .getBlobClient(fileRecordPO.getFileKey());

            if (!blobClient.exists()) {
                throw new ServiceException("File not found");
            }

            try (InputStream inputStream = blobClient.openInputStream()) {
                InputStream resizedStream = fileProcessor.resizeImage(reSizePictureQuery, inputStream);

                return FileDownloadResult.builder()
                        .fileName(fileRecordPO.getFileName())
                        .fileContent(IOUtils.toByteArray(resizedStream))
                        .contentType(blobClient.getProperties().getContentType())
                        .build();
            }
        } catch (BlobStorageException e) {
            log.error("Failed to resize image", e);
            throw new ServiceException("Failed to resize image");
        }
    }

    private FileRecordPO createFileRecordPO(String fileName, String fileUuidName, String fileType,
                                            long fileSize, String bucketName, String fileKey,
                                            String azureBsUrl, Integer isOpen, String businessType) {
        FileRecordPO po = new FileRecordPO();
        po.setFileName(fileName);
        po.setFileUuidName(fileUuidName);
        po.setFileType(fileType);
        po.setFileSize(fileSize);
        po.setBucketName(bucketName);
        po.setFileKey(fileKey);
        po.setAzureBsUrl(azureBsUrl);
        po.setIsOpen(isOpen);
        po.setBusinessType(businessType);
        return po;
    }

    private void validatePdfFile(FileData file) {
        if (file.getSize() > maxPdfSize) {
            throw new ServiceException(
                    String.format("PDF file size exceeds limit (max %d MB)", maxPdfSize / (1024 * 1024)));
        }
    }

    private FileRecordPO getFileRecordByUuidName(String fileUuidName) {
        return Optional.ofNullable(fileRecordMapper.selectOne(Wrappers.<FileRecordPO>lambdaQuery()
                        .eq(FileRecordPO::getFileUuidName, fileUuidName)))
                .orElseThrow(() -> new ServiceException("File not found"));
    }

    private static String removeLastDotAndExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return fileName;
        }
        int lastDotIndex = fileName.lastIndexOf(".");
        return lastDotIndex == -1 ? fileName : fileName.substring(0, lastDotIndex);
    }


    @Override
    public SafetyResultVO checkImg(FileData file) {
        if (file == null || file.getFileBytes() == null) {
            throw new ServiceException("File data cannot be null");
        }

        try {
            int maxSeverity = 0;
            byte[] fileBytes = file.getFileBytes();
            ContentSafetyImageData imageData = new ContentSafetyImageData();
            imageData.setContent(BinaryData.fromBytes(fileBytes));

            AnalyzeImageOptions options = new AnalyzeImageOptions(imageData);
            AnalyzeImageResult response = contentSafetyClient.analyzeImage(options);

            SafetyCategoryDetailVO detail = new SafetyCategoryDetailVO();
            for (ImageCategoriesAnalysis result : response.getCategoriesAnalysis()) {
                String category = result.getCategory().toString();
                int severity = result.getSeverity();
                maxSeverity = Math.max(maxSeverity, severity);

                log.debug("Category: {}, Severity: {}", category, severity);

                switch (category.toLowerCase()) {
                    case "hate":
                        detail.setHateSeverity(severity);
                        break;
                    case "selfharm":
                        detail.setSelfHarmSeverity(severity);
                        break;
                    case "sexual":
                        detail.setSexualSeverity(severity);
                        break;
                    case "violence":
                        detail.setViolenceSeverity(severity);
                        break;
                    default:
                        log.warn("Unknown category: {}", category);
                        break;
                }
            }

            return SafetyResultVO.builder()
                    .safetyCategoryDetailVO(detail)
                    .maxSeverity(maxSeverity)
                    .isSafe(maxSeverity < 3)
                    .build();

        } catch (Exception e) {
            log.error("Failed to check image. File: {}", file.getOriginalFilename(), e);
            throw new ServiceException("Failed to check image: " + e.getMessage(), e);
        }
    }



    @Override
    public SafetyResultVO checkText(String text) {
        if (StrUtil.isEmpty(text)) {
            throw new ServiceException("text cannot be null");
        }

        try {
            int maxSeverity = 0;

            AnalyzeTextOptions options = new AnalyzeTextOptions(text);
            AnalyzeTextResult response = contentSafetyClient.analyzeText(options);

            SafetyCategoryDetailVO detail = new SafetyCategoryDetailVO();
            for (TextCategoriesAnalysis result : response.getCategoriesAnalysis()) {
                String category = result.getCategory().toString();
                int severity = result.getSeverity();
                maxSeverity = Math.max(maxSeverity, severity);

                log.debug("Category: {}, Severity: {}", category, severity);

                switch (category.toLowerCase()) {
                    case "hate":
                        detail.setHateSeverity(severity);
                        break;
                    case "selfharm":
                        detail.setSelfHarmSeverity(severity);
                        break;
                    case "sexual":
                        detail.setSexualSeverity(severity);
                        break;
                    case "violence":
                        detail.setViolenceSeverity(severity);
                        break;
                    default:
                        log.warn("Unknown category: {}", category);
                        break;
                }
            }

            return SafetyResultVO.builder()
                    .safetyCategoryDetailVO(detail)
                    .maxSeverity(maxSeverity)
                    .isSafe(maxSeverity < 2)
                    .build();

        } catch (Exception e) {
            log.error("Failed to check text. Text: {}", text, e);
            throw new ServiceException("Failed to check text: " + e.getMessage(), e);
        }
    }


    // 以下是基础的CRUD方法
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(FileRecord fileRecord) {
        FileRecordPO fileRecordPO = fileRecordAssembler.modelToPO(fileRecord);
        fileRecordMapper.insert(fileRecordPO);
        fileRecord.setId(fileRecordPO.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(FileRecord fileRecord) {
        fileRecordMapper.updateById(fileRecordAssembler.modelToPO(fileRecord));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        fileRecordMapper.deleteById(id);
    }

    @Override
    public FileRecord detail(Long id) {
        return fileRecordAssembler.POToModel(fileRecordMapper.selectById(id));
    }
}