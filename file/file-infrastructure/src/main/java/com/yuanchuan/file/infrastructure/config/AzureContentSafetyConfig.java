package com.yuanchuan.file.infrastructure.config;

import cn.hutool.core.util.StrUtil;
import com.azure.ai.contentsafety.ContentSafetyClient;
import com.azure.ai.contentsafety.ContentSafetyClientBuilder;
import com.azure.core.credential.AzureKeyCredential;
import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.blob.BlobServiceClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class AzureContentSafetyConfig {


    @Value("${azure.content.safety.endpoint}")
    private String endpoint;

    @Value("${azure.content.safety.key}")
    private String key;


    @Bean
    public ContentSafetyClient contentSafetyClient() {
        // 优先使用连接字符串
        if (StrUtil.isNotBlank(key) && StrUtil.isNotBlank(endpoint)) {
            return new ContentSafetyClientBuilder()
                    .endpoint(endpoint)
                    .credential(new AzureKeyCredential(key))
                    .buildClient();
        }
        throw new IllegalArgumentException("Azure Content Safety未配置，请检查配置文件");
    }


}

