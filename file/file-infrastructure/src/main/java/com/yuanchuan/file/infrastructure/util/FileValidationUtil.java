package com.yuanchuan.file.infrastructure.util;

import com.yuanchuan.common.exception.ServiceException;
import com.yuanchuan.file.api.dto.FileData;
import org.apache.commons.lang3.StringUtils;

/**
 * 文件验证工具类
 */
public class FileValidationUtil {

    /**
     * 验证文件基本属性
     */
    public static void validateFile(FileData file) {
        if (file == null) {
            throw new ServiceException("File data cannot be null");
        }

        if (file.getFileBytes() == null || file.getFileBytes().length == 0) {
            throw new ServiceException("File content cannot be empty");
        }

        if (StringUtils.isBlank(file.getOriginalFilename())) {
            throw new ServiceException("File name cannot be empty");
        }

        if (StringUtils.isBlank(file.getContentType())) {
            throw new ServiceException("Content type cannot be empty");
        }

        if (file.getSize() <= 0) {
            throw new ServiceException("File size must be positive");
        }
    }

    /**
     * 验证图片文件
     */
    public static void validateImageFile(String fileType) {
        String[] allowedTypes = {"jpg", "jpeg", "png", "gif"};
        for (String type : allowedTypes) {
            if (type.equalsIgnoreCase(fileType)) {
                return;
            }
        }
        throw new ServiceException("Unsupported image type: " + fileType);
    }

    /**
     * 验证PDF文件
     */
    public static void validatePdfFile(String fileType) {
        if (!"pdf".equalsIgnoreCase(fileType)) {
            throw new ServiceException("Unsupported file type, expected PDF");
        }
    }
}