package com.yuanchuan.file.infrastructure.util;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.azure.storage.blob.BlobClient;
import com.azure.storage.blob.BlobContainerClient;
import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.blob.models.BlobHttpHeaders;
import com.azure.storage.blob.models.BlobStorageException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;

/**
 * Azure Blob Storage 操作工具类
 */
@Slf4j
@Component
public class AzureStorageUtil {

    /**
     * 构建Blob存储路径
     */
    public String buildBlobName(String businessType, String uuid, String fileType) {
        String template = "{businessType}/{utcDate}/{fileType}/{uuid}.{fileType}";
        String date = DateUtil.formatDate(new DateTime(TimeZone.getTimeZone("UTC")));
        Map<String, Object> params = new HashMap<>();
        params.put("businessType", businessType);
        params.put("utcDate", date);
        params.put("uuid", uuid);
        params.put("fileType", fileType);

        return StrUtil.format(template, params);
    }

    /**
     * 上传文件到Blob Storage
     */
    public BlobClient uploadToBlobStorage(BlobServiceClient client,
                                          String containerName,
                                          String blobName,
                                          InputStream stream,
                                          long size,
                                          String contentType) throws BlobStorageException {
        BlobContainerClient containerClient = client.getBlobContainerClient(containerName);
        BlobClient blobClient = containerClient.getBlobClient(blobName);

        BlobHttpHeaders headers = new BlobHttpHeaders()
                .setContentType(contentType);

        blobClient.upload(stream, size, true);
        blobClient.setHttpHeaders(headers);

        log.info("File uploaded successfully, container: {}, blob: {}", containerName, blobName);
        return blobClient;
    }

    /**
     * 从Blob Storage删除文件
     */
    public void deleteFromBlobStorage(BlobServiceClient client,
                                      String containerName,
                                      String blobName) throws BlobStorageException {
        BlobContainerClient containerClient = client.getBlobContainerClient(containerName);
        BlobClient blobClient = containerClient.getBlobClient(blobName);

        if (blobClient.exists()) {
            blobClient.delete();
            log.info("File deleted successfully, container: {}, blob: {}", containerName, blobName);
        } else {
            log.warn("File not found, container: {}, blob: {}", containerName, blobName);
        }
    }
}