package com.yuanchuan.file.infrastructure.util;

import cn.hutool.core.io.FileUtil;
import com.yuanchuan.common.exception.ServiceException;
import com.yuanchuan.file.api.dto.FileData;
import com.yuanchuan.file.api.query.ReSizePictureQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.pdfbox.tools.imageio.ImageIOUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.rmi.ServerException;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件处理工具类
 */
@Slf4j
@Component
public class FileProcessor {

    /**
     * 检查是否为PDF文件
     */
    public boolean isPdfFile(String fileType) {
        return "pdf".equalsIgnoreCase(fileType);
    }

    /**
     * 检查是否为图片文件
     */
    public boolean isImageFile(String fileType) {
        String[] imageTypes = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};
        for (String type : imageTypes) {
            if (type.equalsIgnoreCase(fileType)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 处理PDF文件 - 转换为图片
     */
    public FileData processPdf(FileData file, int maxPages) throws IOException {
        try (InputStream pdfInputStream = new ByteArrayInputStream(file.getFileBytes());
             PDDocument document = Loader.loadPDF(pdfInputStream)) {

            int pageCount = document.getNumberOfPages();
            if (pageCount > maxPages) {
                throw new ServerException(
                        String.format("PDF page count exceeds limit (max %d pages)", maxPages));
            }

            PDFRenderer renderer = new PDFRenderer(document);
            List<BufferedImage> images = new ArrayList<>();

            for (int i = 0; i < pageCount; i++) {
                BufferedImage image = renderer.renderImageWithDPI(i, 150, ImageType.RGB);
                images.add(image);
            }

            BufferedImage concatenatedImage = concatenateImages(images);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIOUtil.writeImage(concatenatedImage, "jpg", outputStream, 150);

            return FileData.builder()
                    .fileBytes(outputStream.toByteArray()).contentType("image/jpeg")
                    .originalFilename(file.getOriginalFilename().replace(".pdf", ".jpg"))
                    .size(file.getSize()).build();

        } catch (Exception e) {
            log.error("PDF processing failed", e);
            throw new ServiceException("PDF processing failed");
        }
    }

    /**
     * 压缩图片
     */
    public FileData compressImage(FileData file, double quality, double scale) throws IOException {
        if (!isImageFile(FileUtil.extName(file.getOriginalFilename()))) {
            return file;
        }

        try (InputStream inputStream = new ByteArrayInputStream(file.getFileBytes())) {
            BufferedImage originalImage = ImageIO.read(inputStream);
            if (originalImage == null) {
                return file;
            }

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            compressAndWriteImage(originalImage, outputStream, quality, scale);

            return FileData.builder()
                    .fileBytes(outputStream.toByteArray())
                    .contentType("image/jpeg")
                    .originalFilename(file.getOriginalFilename().replace(".pdf", ".jpg"))
                    .size(file.getSize()).build();
        }
    }

    /**
     * 调整图片尺寸
     */
    public InputStream resizeImage(ReSizePictureQuery query, InputStream inputStream) throws IOException {
        BufferedImage originalImage = ImageIO.read(inputStream);
        if (originalImage == null) {
            throw new ServiceException("Invalid image file");
        }

        int targetWidth = query.getWidth() == null ? originalImage.getWidth() : query.getWidth();
        int targetHeight = query.getHeight() == null ? originalImage.getHeight() : query.getHeight();

        BufferedImage resizedImage = resize(originalImage, targetWidth, targetHeight);
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ImageIO.write(resizedImage, "jpg", os);
        return new ByteArrayInputStream(os.toByteArray());
    }


    /**
     * 压缩并写入图片（集成ImageUtil的compressImage方法）
     */
    public void compressAndWriteImage(BufferedImage image,
                                      ByteArrayOutputStream outputStream,
                                      double quality,
                                      double scale) throws IOException {
        ByteArrayOutputStream tempStream = new ByteArrayOutputStream();
        ImageIO.write(image, "jpg", tempStream);

        try (InputStream compressedStream = ImageUtil.compressImage(
                tempStream.toByteArray(), scale, quality)) {
            outputStream.write(IOUtils.toByteArray(compressedStream));
        }
    }

    /**
     * 调整图片尺寸（集成ImageUtil的resizeImage方法）
     */
    public BufferedImage resize(BufferedImage originalImage, int width, int height) {
        try {
            ByteArrayOutputStream tempStream = new ByteArrayOutputStream();
            ImageIO.write(originalImage, "jpg", tempStream);

            ReSizePictureQuery query = new ReSizePictureQuery();
            query.setWidth(width);
            query.setHeight(height);
            query.setMode(2); // 强制宽高模式

            try (InputStream resizedStream = ImageUtil.resizeImage(
                    query, new ByteArrayInputStream(tempStream.toByteArray()))) {
                return ImageIO.read(resizedStream);
            }
        } catch (IOException e) {
            log.error("Image resize failed", e);
            throw new ServiceException("Image resize failed");
        }
    }


    /**
     * 垂直拼接多张图片（用于PDF转图片）
     * @param images 要拼接的图片列表（至少包含一张图片）
     * @return 拼接后的完整图片
     * @throws ServiceException 当图片列表为空或拼接失败时抛出
     */
    private BufferedImage concatenateImages(List<BufferedImage> images) {
        // 参数校验
        if (CollectionUtils.isEmpty(images)) {
            throw new ServiceException("图片列表不能为空");
        }

        try {
            // 单张图片直接返回
            if (images.size() == 1) {
                return images.get(0);
            }

            // 计算总高度和最大宽度
            int totalHeight = 0;
            int maxWidth = 0;
            for (BufferedImage image : images) {
                totalHeight += image.getHeight();
                maxWidth = Math.max(maxWidth, image.getWidth());
            }

            // 创建目标图片
            BufferedImage concatenatedImage = new BufferedImage(
                    maxWidth,
                    totalHeight,
                    BufferedImage.TYPE_INT_RGB);

            Graphics2D g2d = concatenatedImage.createGraphics();

            // 设置背景为白色（针对PDF转换场景）
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, maxWidth, totalHeight);

            // 拼接图片
            int yOffset = 0;
            for (BufferedImage image : images) {
                g2d.drawImage(image, 0, yOffset, null);
                yOffset += image.getHeight();
            }

            g2d.dispose();
            return concatenatedImage;

        } catch (Exception e) {
            log.error("图片拼接失败", e);
            throw new ServiceException("图片拼接失败：" + e.getMessage());
        }
    }
}