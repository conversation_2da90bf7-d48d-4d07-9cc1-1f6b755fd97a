package com.yuanchuan.file.infrastructure.po;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.yuanchuan.common.domain.entity.BaseEntity;

/**
 * 文件记录 PO实体
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-05-15
 */
@EqualsAndHashCode(callSuper=false)
@Data
@TableName("file_record")
public class FileRecordPO extends BaseEntity {
	/**
	* 主键自增
	*/
	@TableId(type = IdType.AUTO)
	private Long id;

	/**
	* 文件名
	*/
	@TableField(value = "file_name")
	private String fileName;

	/**
	* 唯一标识文件名
	*/
	@TableField(value = "file_uuid_name")
	private String fileUuidName;

	/**
	* 文件类型如 jpg/png/pdf
	*/
	@TableField(value = "file_type")
	private String fileType;

	/**
	* 文件大小
	*/
	@TableField(value = "file_size")
	private Long fileSize;

	/**
	* 桶名称
	*/
	@TableField(value = "bucket_name")
	private String bucketName;

	/**
	* 对象存储key
	*/
	@TableField(value = "file_key")
	private String fileKey;

	/**
	* azure中Blob Storage的url地址
	*/
	@TableField(value = "azure_bs_url")
	private String azureBsUrl;

	/**
	* 是否公开。0-不公开，1-公开
	*/
	@TableField(value = "is_open")
	private Integer isOpen;

	/**
	* 业务类型
	*/
	@TableField(value = "business_type")
	private String businessType;






}