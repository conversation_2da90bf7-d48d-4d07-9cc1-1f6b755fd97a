package com.yuanchuan.file.infrastructure.config;

import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.blob.BlobServiceClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class AzureBlobStorageConfig {


    @Value("${azure.storage.connection-string:}")
    private String connectionString;


    @Bean
    public BlobServiceClient blobServiceClient() {
        // 优先使用连接字符串
        if (connectionString != null && !connectionString.isEmpty()) {
            return new BlobServiceClientBuilder()
                    .connectionString(connectionString)
                    .buildClient();
        }
        throw new IllegalArgumentException("Azure Blob Storage连接信息未配置，请检查配置文件");
    }

}
