<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuanchuan.file.infrastructure.mapper.FileRecordMapper">

    <resultMap type="com.yuanchuan.file.infrastructure.po.FileRecordPO" id="fileRecordMap">
        <result property="id" column="id"/>
        <result property="fileName" column="file_name"/>
        <result property="fileUuidName" column="file_uuid_name"/>
        <result property="fileType" column="file_type"/>
        <result property="fileSize" column="file_size"/>
        <result property="bucketName" column="bucket_name"/>
        <result property="fileKey" column="file_key"/>
        <result property="azureBsUrl" column="azure_bs_url"/>
        <result property="isOpen" column="is_open"/>
        <result property="businessType" column="business_type"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="createdBy" column="created_by"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="active" column="active"/>
    </resultMap>

</mapper>