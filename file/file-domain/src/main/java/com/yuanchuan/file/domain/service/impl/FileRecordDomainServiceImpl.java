package com.yuanchuan.file.domain.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import com.yuanchuan.file.domain.service.FileRecordDomainService;
import com.yuanchuan.file.domain.model.FileRecord;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import com.yuanchuan.file.domain.repository.FileRecordRepository;

import java.util.List;

/**
 * 文件记录
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-05-15
 */
@Service
public class FileRecordDomainServiceImpl implements FileRecordDomainService {

    @Autowired
    private FileRecordRepository fileRecordRepository;


    /**
    * 新增
    */
    @Override
    public void add(FileRecord fileRecord) {
        fileRecordRepository.add(fileRecord);
    }

    /**
    * 修改
    */
    @Override
    public void update(FileRecord fileRecord) {
        fileRecordRepository.update(fileRecord);
    }

    /**
    * 删除
    */
    @Override
    public void delete(Long id) {
        fileRecordRepository.delete(id);
    }

    /**
    * 详情
    */
    @Override
    public FileRecord detail(Long id) {
        return fileRecordRepository.detail(id);
    }



}