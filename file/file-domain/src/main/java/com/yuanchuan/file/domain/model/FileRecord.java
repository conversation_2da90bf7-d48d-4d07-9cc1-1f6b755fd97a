package com.yuanchuan.file.domain.model;

import lombok.Data;
import java.util.Date;
import com.yuanchuan.common.domain.entity.BaseEntity;

/**
 * 文件记录 Model
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-05-15
 */
@Data
public class FileRecord  extends BaseEntity {
	/**
	* 主键自增
	*/
	private Long id;

	/**
	* 文件名
	*/
	private String fileName;

	/**
	* 唯一标识文件名
	*/
	private String fileUuidName;

	/**
	* 文件类型如 jpg/png/pdf
	*/
	private String fileType;

	/**
	* 文件大小
	*/
	private Integer fileSize;

	/**
	* 桶名称
	*/
	private String bucketName;

	/**
	* 对象存储key
	*/
	private String fileKey;

	/**
	* azure中Blob Storage的url地址
	*/
	private String azureBsUrl;

	/**
	* 是否公开。0-不公开，1-公开
	*/
	private Integer isOpen;

	/**
	* 业务类型
	*/
	private String businessType;






}