package com.yuanchuan.file.domain.service;

import com.yuanchuan.common.response.PageResult;
import com.yuanchuan.file.domain.model.FileRecord;

import java.util.List;

/**
 * 文件记录
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-05-15
 */
public interface FileRecordDomainService{

    /**
    * 新增
    */
    void add(FileRecord fileRecord);

    /**
    * 修改
    */
    void update(FileRecord fileRecord);

    /**
    * 删除
    */
    void delete(Long id);

    /**
    * 详情
    */
    FileRecord detail(Long id);



}