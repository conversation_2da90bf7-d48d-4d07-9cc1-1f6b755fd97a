package com.yuanchuan.file.domain.repository;

import com.yuanchuan.file.api.dto.FileData;
import com.yuanchuan.file.api.dto.FileDownloadResult;
import com.yuanchuan.file.api.query.ReSizePictureQuery;
import com.yuanchuan.file.api.vo.FileRecordVO;
import com.yuanchuan.file.api.vo.SafetyResultVO;
import com.yuanchuan.file.domain.model.FileRecord;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;


/**
 * 文件记录 Repository仓储接口
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-05-15
 */
public interface FileRecordRepository{

    /**
     * 文件上传
     */
    FileRecord upload(FileData file, Integer isOpen, String businessType) throws IOException;

    void updateImg(FileData file, Integer isOpen,String fileUuidName,String businessType ) throws IOException;

    /**
     * 文件删除
     */
    void deleteByFileUuidName(String fileUuidName);

    /**
     * 根据key获取文件
     */
    FileRecordVO getFileByFileUuidName(String fileUuidName);

    /**
     * 下载文件
     */
    FileDownloadResult download(String fileUuidName) throws IOException;

    /**
     * 缩放图片
     */
    FileDownloadResult resizePicture(ReSizePictureQuery reSizePictureQuery) throws IOException;


    SafetyResultVO checkImg(FileData file);

    SafetyResultVO checkText(String text);

    /**
    * 新增
    */
    void add(FileRecord fileRecord);

    /**
    * 修改
    */
    void update(FileRecord fileRecord);

    /**
    * 删除
    */
    void delete(Long id);

    /**
    * 详情
    */
    FileRecord detail(Long id);



}