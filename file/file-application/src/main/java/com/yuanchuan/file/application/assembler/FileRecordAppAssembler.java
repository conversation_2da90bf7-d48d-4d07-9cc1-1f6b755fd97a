package com.yuanchuan.file.application.assembler;

import com.yuanchuan.common.mapstruct.ApplicationMapstruct;
import com.yuanchuan.file.api.dto.FileRecordDTO;
import com.yuanchuan.file.api.vo.FileRecordVO;
import com.yuanchuan.file.domain.model.FileRecord;


import org.mapstruct.Mapper;
import org.mapstruct.Named;

/**
* 文件记录Mapstruct
*
* @<NAME_EMAIL>
* @since 1.0.0 2025-05-15
*/
@Named("fileRecordAppAssembler")
@Mapper(componentModel = "spring")
public interface FileRecordAppAssembler extends ApplicationMapstruct<FileRecordDTO,FileRecord,FileRecordVO> {
}
