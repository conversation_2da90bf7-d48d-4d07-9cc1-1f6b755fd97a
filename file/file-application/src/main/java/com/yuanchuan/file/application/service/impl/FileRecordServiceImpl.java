package com.yuanchuan.file.application.service.impl;


import com.yuanchuan.common.response.PageResult;
import com.yuanchuan.file.api.dto.FileData;
import com.yuanchuan.file.api.dto.FileDownloadResult;
import com.yuanchuan.file.api.query.FileRecordQuery;
import com.yuanchuan.file.api.query.ReSizePictureQuery;
import com.yuanchuan.file.api.service.FileRecordService;
import com.yuanchuan.file.api.vo.FileRecordVO;

import com.yuanchuan.file.api.vo.SafetyResultVO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.List;
import com.yuanchuan.file.api.dto.FileRecordDTO;
import com.yuanchuan.file.application.assembler.FileRecordAppAssembler;

import com.yuanchuan.file.domain.model.FileRecord;
import com.yuanchuan.file.domain.repository.FileRecordRepository;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件记录
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-05-15
 */
@Service
//TODO 修改Dubbo group
@DubboService(version = "1.0", group = "${dubbo.consumer.group.file}", delay = -1, retries = 0, timeout = 10000)
public class FileRecordServiceImpl implements FileRecordService {

    @Autowired
    private FileRecordRepository fileRecordRepository;

    @Resource
    private FileRecordAppAssembler fileRecordAssembler;


    @Override
    public FileRecordVO upload(FileData file, Integer isOpen, String businessType) throws IOException {
        return fileRecordAssembler.modelToVO(fileRecordRepository.upload(file, isOpen, businessType));
    }

    @Override
    public void updateImg(FileData file, Integer isOpen, String fileUuidName, String businessType) throws IOException {
        fileRecordRepository.updateImg(file, isOpen, fileUuidName, businessType);
    }

    @Override
    public void deleteByFileUuidName(String fileUuidName) {
        fileRecordRepository.deleteByFileUuidName(fileUuidName);
    }

    @Override
    public FileRecordVO getFileByFileUuidName(String fileUuidName) {
        return fileRecordRepository.getFileByFileUuidName(fileUuidName);
    }

    @Override
    public FileDownloadResult download(String fileUuidName) throws IOException {
        return fileRecordRepository.download(fileUuidName);
    }

    @Override
    public FileDownloadResult resizePicture(ReSizePictureQuery reSizePictureQuery) throws IOException {
        return fileRecordRepository.resizePicture(reSizePictureQuery);
    }


    @Override
    public SafetyResultVO checkImg(FileData file) {
        return fileRecordRepository.checkImg(file);
    }

    @Override
    public SafetyResultVO checkText(String text) {
        return fileRecordRepository.checkText(text);
    }


    /**
    * 新增
    */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void add(FileRecordDTO dto) {
        FileRecord fileRecord = fileRecordAssembler.dtoToModel(dto);
        //TODO 唯一性校验等
        fileRecordRepository.add(fileRecord);
    }

    /**
    * 修改
    */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void update(FileRecordDTO dto) {
        FileRecord fileRecord = fileRecordAssembler.dtoToModel(dto);
        //TODO 唯一性校验等
        fileRecordRepository.update(fileRecord);
    }

    /**
    * 删除
    */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void delete(Long id) {
        FileRecord fileRecord = fileRecordRepository.detail(id);
        //TODO 关联信息删除
        fileRecordRepository.delete(id);
    }

    /**
    * 详情
    */
    @Override
    public FileRecordVO detail(Long id) {
        FileRecord detail = fileRecordRepository.detail(id);
        return fileRecordAssembler.modelToVO(detail);
    }


    /**
    * 复杂分页
    */
    @Override
    public PageResult<FileRecordVO> voPage(FileRecordQuery query) {
        return null;
    }


}