# File 服务手動部署操作指南

## 步驟1：載入配置變數
```bash
source deploy-config.sh
show_config
```

## 步驟2：檢查必要工具
```bash
check_tools
```

## 步驟3：檢查Azure登錄狀態
```bash
check_azure_login
```

## 步驟4：檢查K8s集群現有服務
```bash
# 檢查現有deployment 管理Pod的控制器
kubectl get deployment file
# 刪除deployment（會自動刪除pods）
kubectl delete deployment file --ignore-not-found=true

# 檢查現有service 為Pod提供穩定網絡訪問的抽象層
kubectl get svc file
# 刪除service
kubectl delete svc file --ignore-not-found=true

# 檢查現有configmap
kubectl get configmap file-config
# 刪除configmap
kubectl delete configmap file-config --ignore-not-found=true

# 檢查現有pods Kubernetes中最小的部署單元
kubectl get pods -l app=file
# 删除
kubectl delete pods -l app=file
kubectl delete pods -l app=file --force --grace-period=0

# 確認清理完成
kubectl get all -l app=file
```

## 步驟5：構建Java應用
```bash
# 清理並構建
mvn clean package -DskipTests

# 檢查構建結果
ls -la file-bootstrap/target/*.jar
```

## 步驟6：構建Docker映像
```bash
# 使用ACR遠程構建
az acr build --registry $ACR_NAME --image $IMAGE_NAME:$IMAGE_TAG .

# 查看ACR中所有的倉庫(查看，非必须)
az acr repository list --name $ACR_NAME --output table

# 檢查映像是否成功推送
az acr repository show-tags --name $ACR_NAME --repository $IMAGE_NAME --output table
```

## 步驟7：連接K8s集群
```bash
# kubectl cluster-info 有信息后，后续可跳过
kubectl cluster-info

# 獲取集群憑證
az aks get-credentials --resource-group $RESOURCE_GROUP --name $AKS_CLUSTER --overwrite-existing

# 檢查集群連接
kubectl cluster-info

# 檢查節點狀態
kubectl get nodes
```

## 步驟8：應用ConfigMap（已有配置文件）
```bash
# 直接應用已有的ConfigMap配置
kubectl apply -f k8s/configmap.yaml

# 查看ConfigMap內容
kubectl get configmap file-config -o yaml
```

## 步驟9：應用Service（已有配置文件）
```bash
# 直接應用已有的Service配置
kubectl apply -f k8s/service.yaml

# 檢查Service狀態
kubectl get svc file
```

## 步驟10：更新並應用Deployment
```bash
# 更新deployment.yaml中的鏡像標籤
sed -i.bak "s|image: frchacrdev.azurecr.io/file:.*|image: $ACR_LOGIN_SERVER/$IMAGE_NAME:$IMAGE_TAG|" k8s/deployment.yaml

# 應用deployment配置
kubectl apply -f k8s/deployment.yaml

# 檢查deployment狀態
kubectl get deployment file
```

## 步驟11：檢查部署狀態
```bash
# 檢查所有資源
kubectl get all -l app=file

# 檢查deployment狀態
kubectl get deployment file

# 檢查service狀態（顯示所有端口）
kubectl get svc file -o wide

# 檢查configmap
kubectl get configmap file-config
```

## 步驟12：檢查Pod狀態
```bash
# 檢查pod狀態
kubectl get pods -l app=file -o wide

# 檢查pod詳細信息
kubectl describe pods -l app=file

# 檢查pod事件
kubectl get events --field-selector involvedObject.kind=Pod --sort-by='.lastTimestamp'
```

## 步驟13：檢查應用日誌
```bash
# 查看所有pod日誌
kubectl logs -l app=file --tail=50

# 實時查看日誌
kubectl logs -l app=file -f

# 查看特定pod日誌（如果需要）
kubectl logs deployment/file
```

## 步驟14：檢查服務健康狀態
```bash
# 等待pod ready
kubectl wait --for=condition=ready pod -l app=file --timeout=300s

# 檢查端點
kubectl get endpoints file

# 測試Dubbo QoS健康檢查
kubectl port-forward service/file 22222:22222 &
sleep 5
echo "檢查Dubbo QoS狀態："
curl -s http://localhost:22222/status
echo -e "\n檢查Dubbo就緒狀態："
curl -s http://localhost:22222/ready
echo -e "\n檢查Dubbo健康詳情："
curl -s http://localhost:22222/health
pkill -f "kubectl port-forward"
```

## 步驟15：基本連通性驗證
```bash
# 簡單驗證服務DNS和基本連通性
kubectl run test-file --image=busybox --rm -it --restart=Never -- nslookup file

# 直接檢查服務端點狀態
kubectl get endpoints file

# 快速健康檢查
FILE_POD=$(kubectl get pods -l app=file -o jsonpath='{.items[0].metadata.name}')
if [ ! -z "$FILE_POD" ]; then
  kubectl exec $FILE_POD -- curl -s http://localhost:22222/status
fi
```

## 步驟16：檢查Dubbo註冊狀態
```bash
# 檢查是否能訪問QoS端點
kubectl exec deployment/file -- curl -s http://localhost:22222/ls

# 檢查服務提供者狀態
kubectl exec deployment/file -- curl -s http://localhost:22222/ps

# 檢查消費者狀態
kubectl exec deployment/file -- curl -s http://localhost:22222/cd

# **确认健康检查状态
kubectl describe pods -l app=file | grep -A3 "Conditions:"


```

## 步驟17：最終驗證
```bash
# 檢查所有資源狀態
echo "=== Deployment Status ==="
kubectl get deployment file

echo "=== Pod Status ==="
kubectl get pods -l app=file

echo "=== Service Status (所有端口) ==="
kubectl get svc file -o wide

echo "=== ConfigMap Status ==="
kubectl get configmap file-config

echo "=== 端口信息 ==="
echo "- Dubbo RPC: 20807 (主要服務端口)"
echo "- Dubbo QoS: 22222 (健康檢查端口)"
echo "- Spring Boot: 8092 (管理端口)"

echo "=== Recent Events ==="
kubectl get events --sort-by='.lastTimestamp' | tail -10

echo "=== 服務健康檢查 ==="
FILE_POD=$(kubectl get pods -l app=file -o jsonpath='{.items[0].metadata.name}')
if [ ! -z "$FILE_POD" ]; then
  echo "檢查Pod: $FILE_POD"
  kubectl exec $FILE_POD -- curl -s http://localhost:22222/status || echo "QoS檢查失敗"
fi
```