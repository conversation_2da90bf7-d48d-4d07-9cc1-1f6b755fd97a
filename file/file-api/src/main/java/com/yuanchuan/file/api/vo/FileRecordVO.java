package com.yuanchuan.file.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

import java.util.Date;

/**
* 文件记录
*
* @<NAME_EMAIL>
* @since 1.0.0 2025-05-15
*/

@Data
@Schema(name = "FileRecordVO", description = "VO实体类")
public class FileRecordVO implements Serializable {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	* 主键自增
	*/
	@Schema(description = "主键自增")
	private Long id;

	/**
	* 文件名
	*/
	@Schema(description = "文件名")
	private String fileName;

	/**
	* 唯一标识文件名
	*/
	@Schema(description = "唯一标识文件名")
	private String fileUuidName;

	/**
	* 文件类型如 jpg/png/pdf
	*/
	@Schema(description = "文件类型如 jpg/png/pdf")
	private String fileType;

	/**
	* 文件大小
	*/
	@Schema(description = "文件大小")
	private Integer fileSize;

	/**
	* 桶名称
	*/
	@Schema(description = "桶名称")
	private String bucketName;

	/**
	* 对象存储key
	*/
	@Schema(description = "对象存储key")
	private String fileKey;

	/**
	* azure中Blob Storage的url地址
	*/
	@Schema(description = "azure中Blob Storage的url地址")
	private String azureBsUrl;

	/**
	* 是否公开。0-不公开，1-公开
	*/
	@Schema(description = "是否公开。0-不公开，1-公开")
	private Integer isOpen;

	/**
	* 业务类型
	*/
	@Schema(description = "业务类型")
	private String businessType;

	/**
	* 创建时间
	*/
	@Schema(description = "创建时间")
		/** @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)*/
	private Date createdAt;

	/**
	* 更新时间
	*/
	@Schema(description = "更新时间")
		/** @JsonFormat(pattern = DateUtils.DATE_TIME_PATTERN)*/
	private Date updatedAt;

	/**
	* 创建人
	*/
	@Schema(description = "创建人")
	private Long createdBy;

	/**
	* 更新人
	*/
	@Schema(description = "更新人")
	private Long updatedBy;

	/**
	* 是否启用 0禁用，1正常
	*/
	@Schema(description = "是否启用 0禁用，1正常")
	private Integer active;


}