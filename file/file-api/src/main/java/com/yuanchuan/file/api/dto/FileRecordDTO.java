package com.yuanchuan.file.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;

import java.util.Date;


/**
 * 文件记录
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-05-15
 */
@EqualsAndHashCode(callSuper=false)
@Data
@Schema(name = "FileRecordDTO", description = "DTO实体类")
public class FileRecordDTO implements Serializable {
	/**
	* 主键自增
	*/
	@Schema(description = "主键自增")
	private Long id;

	/**
	* 文件名
	*/
	@Schema(description = "文件名")
	private String fileName;

	/**
	* 唯一标识文件名
	*/
	@Schema(description = "唯一标识文件名")
	private String fileUuidName;

	/**
	* 文件类型如 jpg/png/pdf
	*/
	@Schema(description = "文件类型如 jpg/png/pdf")
	private String fileType;

	/**
	* 文件大小
	*/
	@Schema(description = "文件大小")
	private Integer fileSize;

	/**
	* 桶名称
	*/
	@Schema(description = "桶名称")
	private String bucketName;

	/**
	* 对象存储key
	*/
	@Schema(description = "对象存储key")
	private String fileKey;

	/**
	* azure中Blob Storage的url地址
	*/
	@Schema(description = "azure中Blob Storage的url地址")
	private String azureBsUrl;

	/**
	* 是否公开。0-不公开，1-公开
	*/
	@Schema(description = "是否公开。0-不公开，1-公开")
	private Integer isOpen;

	/**
	* 业务类型
	*/
	@Schema(description = "业务类型")
	private String businessType;






}