package com.yuanchuan.file.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(description = "内容安全各类别审核详情")
public class SafetyCategoryDetailVO implements Serializable {

    @Schema(description = "仇恨内容的严重等级，范围通常为 0-4，0 表示安全")
    private int hateSeverity;

    @Schema(description = "自残内容的严重等级，范围通常为 0-4，0 表示安全")
    private int selfHarmSeverity;

    @Schema(description = "色情内容的严重等级，范围通常为 0-4，0 表示安全")
    private int sexualSeverity;

    @Schema(description = "暴力内容的严重等级，范围通常为 0-4，0 表示安全")
    private int violenceSeverity;

}