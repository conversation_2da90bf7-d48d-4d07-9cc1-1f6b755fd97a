package com.yuanchuan.file.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
@Schema(name = "SafetyResultVO", description = "ai检查结果VO实体类")
public class SafetyResultVO implements Serializable {

    @Schema(description = "是否安全 false：不安全。true：安全")
    private Boolean isSafe;

    @Schema(description = "最大严重等级")
    private int maxSeverity;

    @Schema(description = "检测结果详情")
    private SafetyCategoryDetailVO safetyCategoryDetailVO;
}
