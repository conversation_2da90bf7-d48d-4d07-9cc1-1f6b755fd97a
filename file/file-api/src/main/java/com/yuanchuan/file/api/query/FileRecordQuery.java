package com.yuanchuan.file.api.query;


import com.yuanchuan.common.domain.query.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;

import java.util.Date;

/**
* 文件记录查询
*
* @<NAME_EMAIL>
* @since 1.0.0 2025-05-15
*/
@Data
@Schema(name = "file_record", description = "Query实体类")
public class FileRecordQuery extends PageQuery implements Serializable {

}