package com.yuanchuan.file.api.query;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 缩放图片查询实体类
 */
@Data
@Schema(name = "ReSizePictureQuery", description = "缩放图片查询实体类")
public class ReSizePictureQuery implements Serializable {

    private static final long serialVersionUID = 6136593365368522668L;

    /**
     * 唯一标识文件名
     */
    @Schema(description = "唯一标识文件名" , example = "123456.jpg")
    private String fileUuidName;

    /**
     * 缩放模式。0-单边固定缩略，1-指定宽高缩略，2-强制宽高缩略，3-按比例缩放
     */
    @Schema(description = "缩放模式。0-单边固定缩略，1-指定宽高缩略，2-强制宽高缩略，3-按比例缩放" , example = "0" ,required = true)
    private Integer mode;

    /**
     * 缩放宽度
     */
    @Schema(description = "缩放宽度", example = "100")
    private Integer width;

    /**
     * 缩放高度
     */
    @Schema(description = "缩放高度", example = "100")
    private Integer height;

    /**
     * 缩放方向。0-长边，1-短边
     */
    @Schema(description = "缩放方向", example = "0")
    private Integer direction;

    /**
     * 缩放比例
     */
    @Schema(description = "缩放比例", example = "0.5")
    private Double scale;

    /**
     * 质量
     */
    @Schema(description = "质量", example = "0.5")
    private Double quality = 1.0D;

}
