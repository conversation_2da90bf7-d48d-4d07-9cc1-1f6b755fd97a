package com.yuanchuan.file.api.service;

import com.yuanchuan.common.response.PageResult;
import com.yuanchuan.file.api.dto.FileData;
import com.yuanchuan.file.api.dto.FileDownloadResult;
import com.yuanchuan.file.api.dto.FileRecordDTO;
import com.yuanchuan.file.api.query.ReSizePictureQuery;
import com.yuanchuan.file.api.vo.FileRecordVO;
import com.yuanchuan.file.api.query.FileRecordQuery;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yuanchuan.file.api.vo.SafetyResultVO;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 文件记录
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-05-15
 */
public interface FileRecordService{


    /**
     * 文件上传
     */
    FileRecordVO upload(FileData file, Integer isOpen, String businessType) throws IOException;

    void updateImg(FileData file, Integer isOpen,String fileUuidName,String businessType ) throws IOException;

    /**
     * 文件删除
     */
    void deleteByFileUuidName(String fileUuidName);

    /**
     * 根据key获取文件
     */
    FileRecordVO getFileByFileUuidName(String fileUuidName);

    /**
     * 下载文件
     */
    FileDownloadResult download(String fileUuidName) throws IOException;

    /**
     * 缩放图片
     */
    FileDownloadResult resizePicture(ReSizePictureQuery reSizePictureQuerye) throws IOException;


    SafetyResultVO checkImg(FileData file);

    SafetyResultVO checkText(String text);
    /**
    * 新增
    */
    void add(FileRecordDTO dto);

    /**
    * 修改
    */
    void update(FileRecordDTO dto);

    /**
    * 删除
    */
    void delete(Long id);

    /**
    * 详情
    */
    FileRecordVO detail(Long id);

    /**
    * 复杂分页
    */
    PageResult<FileRecordVO> voPage(FileRecordQuery query);


}