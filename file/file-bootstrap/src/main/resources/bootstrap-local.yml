server:
  port: 8092

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: *******************************************************************************************************************************
#    username: dev
#    password: 5xVUKUpkJc^
    url: jdbc:mysql://**************:13306/yc_file?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true
    username: admin
    password: '!@#4qweR'
  data:
    redis:
      host: *************
      port: 16379
      password: O*mZz1rrA!S5q
      database: 0

zookeeper:
  address: 127.0.0.1:2181
  name: com.yuanchuan.file

dubbo:
  application:
    name: com.yuanchuan.file
  protocol:
    name: dubbo
    port: 20807
    host: 0.0.0.0
  scan:
    base-packages: com.yuanchuan.file
  registry:
    address: zookeeper://127.0.0.1:2181
    register: true
    timeout: 60000
    parameters:
      blockUntilConnectedWait: 60000
      retryIntervalMillis: 5000
      retryTimes: 5
      sessionTimeoutMs: 180000
      connectionTimeoutMs: 30000
    client: curator
  group: file-local
  provider:
    payload: ********
  consumer:
    timeout: 600000
    check: false


mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.yuanchuan.file.dto
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl 

logging:
  level:
    org.apache.dubbo: DEBUG
    org.apache.zookeeper: DEBUG


azure:
  storage:
    connection-string: DefaultEndpointsProtocol=https;AccountName=qlqj2;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net  # 建议改用环境变量
    container:
      public: dev    # 公开访问的容器（需单独创建并设置访问级别为 Blob 或 Container）
      private: dev-private  # 私有访问的容器
    cdn-host: https://your-cdn.azureedge.net  # 如果使用 CDN 加速
  servicebus:
    connection-string: Endpoint=sb://yuanchuan-asb.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=t54BvRmzN8etGGmk4STCIHXS/ztiFEchd+ASbOWQtPo=
    enabled: true
    retry:
      max-attempts: 3
      delay: 1000
  content:
    safety:
      endpoint: https://frch-aics-dev.cognitiveservices.azure.com
      key: AacgJiTst1KACqZOwV07dj39CODVWEdzwgU1upLc3Kfmr2zT6gWTJQQJ99BEACi0881XJ3w3AAAHACOG77c5
      max:
        retry: 3
      retry:
        interval: 1000

# dubbo 消费者group
dubbo.consumer.group.user: user-local
dubbo.consumer.group.file: file-local
dubbo.consumer.group.shop: shop-local
dubbo.consumer.group.order: order-local
dubbo.consumer.group.reservation: reservation-local
dubbo.consumer.group.review: review-local
dubbo.consumer.group.marketing: marketing-local