apiVersion: v1
kind: ConfigMap
metadata:
  name: file-config
  namespace: default
data:
  application.yml: |
    server:
      port: 8092

    spring:
      datasource:
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: ***************************************************************************************************************
        username: frchuser
        password: Frch2025@Dev
      data:
        redis:
          host: frch-redis-dev.redis.cache.windows.net
          port: 6380
          # 方案1：带用户名配置（推荐）
          username: default
          password: fX9W6BYENhvxUfaqRIV4rAlyvHVkKhvvPAzCaMMMpWk=
          # 方案2：如果上面不行，注释掉username行，只用密码
          # password: fX9W6BYENhvxUfaqRIV4rAlyvHVkKhvvPAzCaMMMpWk=
          database: 0
          ssl:
            enabled: true
          timeout: 10000
          lettuce:
            pool:
              max-active: 8
              max-wait: -1
              max-idle: 8
              min-idle: 0
            shutdown-timeout: 100ms

    zookeeper:
      address: zookeeper-0.zookeeper.default.svc.cluster.local:2181,zookeeper-1.zookeeper.default.svc.cluster.local:2181
      name: com.yuanchuan.file

    dubbo:
      application:
        name: com.yuanchuan.file
        # 启用QoS功能
        qos-enable: true
        qos-port: 22222
        qos-accept-foreign-ip: false
      protocol:
        name: dubbo
        port: 20807
        host: 0.0.0.0
      scan:
        base-packages: com.yuanchuan.file
      registry:
        address: zookeeper://zookeeper-0.zookeeper.default.svc.cluster.local:2181,zookeeper-1.zookeeper.default.svc.cluster.local:2181
        register: true
        timeout: 60000
        parameters:
          blockUntilConnectedWait: 60000
          retryIntervalMillis: 5000
          retryTimes: 5
          sessionTimeoutMs: 180000
          connectionTimeoutMs: 30000
        client: curator
      group: file-dev
      provider:
        payload: ********
      consumer:
        timeout: 600000
        check: false


    mybatis-plus:
      mapper-locations: classpath*:mapper/**/*.xml
      type-aliases-package: com.yuanchuan.file.dto
      configuration:
        map-underscore-to-camel-case: true
        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl 

    logging:
      level:
        org.apache.dubbo: DEBUG
        org.apache.zookeeper: DEBUG


    azure:
      storage:
        connection-string: DefaultEndpointsProtocol=https;AccountName=qlqj2;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net  # 建议改用环境变量
        container:
          public: dev    # 公开访问的容器（需单独创建并设置访问级别为 Blob 或 Container）
          private: dev-private  # 私有访问的容器
        cdn-host: https://your-cdn.azureedge.net  # 如果使用 CDN 加速
      servicebus:
        connection-string: Endpoint=sb://yuanchuan-asb.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=t54BvRmzN8etGGmk4STCIHXS/ztiFEchd+ASbOWQtPo=
        enabled: true
        retry:
          max-attempts: 3
          delay: 1000
      content:
        safety:
          endpoint: https://frch-aics-dev.cognitiveservices.azure.com
          key: AacgJiTst1KACqZOwV07dj39CODVWEdzwgU1upLc3Kfmr2zT6gWTJQQJ99BEACi0881XJ3w3AAAHACOG77c5
          max:
            retry: 3
          retry:
            interval: 1000

    # dubbo 消费者group
    dubbo.consumer.group.user: user-dev
    dubbo.consumer.group.file: file-dev
    dubbo.consumer.group.merchant: merchant-dev
    dubbo.consumer.group.order: order-dev
    dubbo.consumer.group.reservation: reservation-dev
    dubbo.consumer.group.review: review-dev
    dubbo.consumer.group.marketing: marketing-dev