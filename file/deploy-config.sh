#!/bin/bash

# =============================================
# File Server 部署配置
# =============================================

# 項目基本信息
PROJECT_NAME="file"
VERSION="1.0.0"

# Azure 資源配置
ACR_NAME="frchacrdev.azurecr.io"
ACR_SHORT_NAME="frchacrdev"
RESOURCE_GROUP="frch-rg-dev"
AKS_NAME="frch-aks-dev"
SUBSCRIPTION_NAME="Azure subscription 1"

# Docker 映像配置
IMAGE_NAME="file"
TAG=$(date +%Y%m%d%H%M%S)
IMAGE_TAG=$TAG  # 為了與手動部署指南保持一致

# Kubernetes 配置
NAMESPACE="default"
APP_PORT="8092"  # Spring Boot 端口
DUBBO_PORT="20807"  # 主要服务端口
QOS_PORT="22222"  # Dubbo QoS 健康检查端口
REPLICAS="1"

# Git 配置 (用於 Config Server)
GIT_URI="http://gitlab.meichai.in/yuanchuan/config-repo.git"
GIT_BRANCH="develop"

# 資源限制配置
CPU_REQUEST="500m"
CPU_LIMIT="1"
MEMORY_REQUEST="512Mi"
MEMORY_LIMIT="1Gi"

# 服务类型：内部 RPC 服务
SERVICE_TYPE="internal-rpc"

# 新增ACR相關變數
ACR_LOGIN_SERVER=$ACR_NAME
AKS_CLUSTER=$AKS_NAME

# Java 運行時配置
JAVA_OPTS="-Xmx768m -Xms512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"

# =============================================
# 函數定義
# =============================================

# 顯示當前配置
show_config() {
    echo "=========================================="
    echo "當前部署配置 - 内部 RPC 服务"
    echo "=========================================="
    echo "項目名稱: $PROJECT_NAME"
    echo "版本: $VERSION"
    echo "部署標籤: $TAG"
    echo "服务类型: $SERVICE_TYPE"
    echo ""
    echo "Azure 資源:"
    echo "  - ACR: $ACR_NAME"
    echo "  - AKS: $AKS_NAME"
    echo "  - 資源組: $RESOURCE_GROUP"
    echo "  - 訂閱: $SUBSCRIPTION_NAME"
    echo ""
    echo "應用配置:"
    echo "  - 映像: $ACR_NAME/$IMAGE_NAME:$TAG"
    echo "  - 命名空間: $NAMESPACE"
    echo "  - 副本數: $REPLICAS"
    echo "  - Spring Boot 端口: $APP_PORT"
    echo "  - Dubbo RPC 端口: $DUBBO_PORT (主要服务)"
    echo "  - Dubbo QoS 端口: $QOS_PORT (健康检查)"
    echo ""
    echo "資源限制:"
    echo "  - CPU: $CPU_REQUEST ~ $CPU_LIMIT"
    echo "  - 內存: $MEMORY_REQUEST ~ $MEMORY_LIMIT"
    echo "  - Java 選項: $JAVA_OPTS"
    echo ""
    echo "網路配置:"
    echo "  - 服务类型: ClusterIP (集群内部访问)"
    echo "  - 访问方式: 通过 Dubbo RPC 调用"
    echo "  - 健康检查: Dubbo QoS (端口 $QOS_PORT)"
    echo "=========================================="
}

# 驗證必要工具
check_tools() {
    echo "檢查必要工具..."
    
    local commands=("az" "kubectl" "docker" "mvn")
    local missing_tools=()
    
    for cmd in "${commands[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing_tools+=("$cmd")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        echo "❌ 缺少以下工具: ${missing_tools[*]}"
        echo "請先安裝這些工具後再繼續"
        return 1
    else
        echo "✅ 所有必要工具都已安裝"
        echo "  - Azure CLI: $(az version --query '"azure-cli"' -o tsv 2>/dev/null || echo '已安裝')"
        echo "  - kubectl: $(kubectl version --client --short 2>/dev/null | head -1 || echo '已安裝')"
        echo "  - Docker: $(docker --version 2>/dev/null || echo '已安裝')"
        echo "  - Maven: $(mvn --version 2>/dev/null | head -1 || echo '已安裝')"
        return 0
    fi
}

# 檢查 Azure 登錄狀態
check_azure_login() {
    echo "檢查 Azure 登錄狀態..."
    
    if az account show &> /dev/null; then
        current_sub=$(az account show --query name -o tsv)
        echo "✅ 已登錄 Azure，當前訂閱: $current_sub"
        
        if [ "$current_sub" != "$SUBSCRIPTION_NAME" ]; then
            echo "⚠️  當前訂閱不匹配，將切換到: $SUBSCRIPTION_NAME"
            az account set --subscription "$SUBSCRIPTION_NAME"
        fi
        return 0
    else
        echo "❌ 未登錄 Azure，請執行: az login"
        return 1
    fi
}

# 檢查 ConfigMap 配置
check_configmap() {
    echo "檢查 ConfigMap 配置..."
    
    if kubectl get configmap file-config -n $NAMESPACE &> /dev/null; then
        echo "✅ ConfigMap file-config 已存在"
        return 0
    else
        echo "❌ ConfigMap file-config 不存在，請先應用 configmap.yaml"
        echo "執行: kubectl apply -f k8s/configmap.yaml"
        return 1
    fi
}

# 部署應用
deploy_app() {
    echo "開始部署文件服務 (内部 RPC 服务)..."
    
    # 檢查前置條件
    if ! check_tools || ! check_azure_login; then
        echo "❌ 前置條件檢查失敗"
        return 1
    fi
    
    # 應用 Kubernetes 配置
    echo "應用 Kubernetes 配置..."
    kubectl apply -f k8s/configmap.yaml
    kubectl apply -f k8s/deployment.yaml
    kubectl apply -f k8s/service.yaml
    
    echo "✅ 部署完成，等待 Pod 就緒..."
    kubectl rollout status deployment/file -n $NAMESPACE
    
    echo "當前 Pod 狀態:"
    kubectl get pods -l app=file -n $NAMESPACE
    
    echo ""
    echo "服务信息:"
    kubectl get service file -n $NAMESPACE
    
    echo ""
    echo "📌 注意: 这是一个内部 RPC 服务"
    echo "   - Dubbo RPC 端口: $DUBBO_PORT (供其他服务调用)"
    echo "   - Dubbo QoS 端口: $QOS_PORT (健康检查和监控)"
    echo "   - Spring Boot 端口: $APP_PORT (应用管理)"
    echo "   - 访问方式: 集群内通过 Dubbo 协议调用"
}

# 記錄部署信息
log_deployment() {
    local log_file="deployment-log.txt"
    echo "記錄部署信息到: $log_file"
    
    {
        echo "========================================"
        echo "部署記錄"
        echo "========================================"
        echo "時間: $(date)"
        echo "項目: $PROJECT_NAME"
        echo "版本: $VERSION"
        echo "標籤: $TAG"
        echo "映像: $ACR_NAME/$IMAGE_NAME:$TAG"
        echo "AKS: $AKS_NAME"
        echo "命名空間: $NAMESPACE"
        echo "HTTP 端口: $APP_PORT"
        echo "Dubbo 端口: $DUBBO_PORT"
        echo "操作者: $(whoami)"
        echo "========================================"
        echo ""
    } >> $log_file
}

# 顯示使用說明
show_usage() {
    echo "使用方法:"
    echo "  source deploy-config.sh          # 載入配置"
    echo "  show_config                      # 顯示當前配置"
    echo "  check_tools                      # 檢查必要工具"
    echo "  check_azure_login               # 檢查 Azure 登錄"
    echo "  check_configmap                 # 檢查 ConfigMap"
    echo "  deploy_app                      # 部署應用"
    echo "  log_deployment                  # 記錄部署信息"
    echo ""
    echo "配置變數已載入，可以直接在命令中使用:"
    echo "  docker build -t \$ACR_NAME/\$IMAGE_NAME:\$TAG ."
    echo "  kubectl get pods -l app=file -n \$NAMESPACE"
    echo "  kubectl logs -f deployment/file -n \$NAMESPACE"
}

# =============================================
# 初始化
# =============================================

echo "載入 $PROJECT_NAME 部署配置..."
echo "標籤: $TAG"
echo "HTTP 端口: $APP_PORT"
echo "Dubbo 端口: $DUBBO_PORT"
echo ""
echo "輸入 'show_usage' 查看使用說明"
echo "輸入 'show_config' 查看完整配置" 