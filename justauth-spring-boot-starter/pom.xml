<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2019-2029, xkcoding & Yangkai.Shen & 沈扬凯 (<EMAIL> & xkcoding.com).
  ~ <p>
  ~ Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~ <p>
  ~ http://www.gnu.org/licenses/lgpl.html
  ~ <p>
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.xkcoding.justauth</groupId>
  <artifactId>justauth-spring-boot-starter</artifactId>
  <version>1.4.1</version>

  <name>justauth-spring-boot-starter</name>
  <url>https://github.com/xkcoding/justauth-spring-boot-starter</url>
  <description>
    Spring Boot 集成 JustAuth 的最佳实践~
    JustAuth 脚手架
  </description>

  <developers>
    <developer>
      <id>xkcoding</id>
      <name>Yangkai.Shen</name>
      <email><EMAIL></email>
      <roles>
        <role>master</role>
        <role>developer</role>
      </roles>
      <timezone>Asia/Shanghai</timezone>
    </developer>
    <developer>
      <name>Yadong.Zhang</name>
      <email><EMAIL></email>
      <url>https://www.zhyd.me</url>
    </developer>
  </developers>

  <licenses>
    <license>
      <name>GNU Lesser General Public License v3.0</name>
      <url>https://www.gnu.org/licenses/lgpl-3.0.html</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <scm>
    <connection>https://github.com/xkcoding/justauth-spring-boot-starter.git</connection>
    <developerConnection>https://github.com/xkcoding/justauth-spring-boot-starter.git</developerConnection>
    <url>https://github.com/xkcoding/justauth-spring-boot-starter</url>
  </scm>

  <properties>
    <!--maven配置信息-->
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <!--SpringBoot版本-->
    <spring-boot.version>2.1.8.RELEASE</spring-boot.version>
    <!--JustAuth版本-->
    <justauth.version>1.16.7</justauth.version>
    <hutool.version>5.8.35</hutool.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-core</artifactId>
      <version>${hutool.version}</version>
    </dependency>
    <dependency>
      <groupId>me.zhyd.oauth</groupId>
      <artifactId>JustAuth</artifactId>
      <version>${justauth.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-redis</artifactId>
      <optional>true</optional>
    </dependency>
    <!-- 对象池，使用redis时必须引入 -->
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-pool2</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-autoconfigure</artifactId>
      <optional>true</optional>
    </dependency>
    <!--https://docs.spring.io/spring-boot/docs/2.1.6.RELEASE/reference/html/configuration-metadata.html#configuration-metadata-annotation-processor-->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>
  </dependencies>

  <dependencyManagement>
    <dependencies>
      <!-- spring boot 版本控制 -->
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring-boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <finalName>${project.artifactId}</finalName>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.0</version>
        <configuration>
          <target>${maven.compiler.target}</target>
          <source>${maven.compiler.source}</source>
          <encoding>UTF-8</encoding>
          <skip>true</skip>
        </configuration>
      </plugin>
      <!-- Source -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>2.2.1</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>jar-no-fork</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <!-- Javadoc -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.9.1</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
            <configuration>
              <!-- add this to disable checking -->
              <additionalparam>-Xdoclint:none</additionalparam>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-maven-plugin</artifactId>
          <version>${spring-boot.version}</version>
          <executions>
            <execution>
              <goals>
                <goal>repackage</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <repositories>
    <!--阿里云私服-->
    <repository>
      <id>aliyun</id>
      <name>aliyun</name>
      <url>http://maven.aliyun.com/nexus/content/groups/public</url>
    </repository>
    <!--xkcoding 私服-->
    <repository>
      <id>xkcoding-nexus</id>
      <name>xkcoding nexus</name>
      <url>https://nexus.xkcoding.com/repository/maven-public/</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </repository>
  </repositories>

  <profiles>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <!-- GPG -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>1.5</version>
            <executions>
              <execution>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <distributionManagement>
        <snapshotRepository>
          <id>oss</id>
          <name>Oss Release Repository</name>
          <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
        </snapshotRepository>
        <repository>
          <id>oss</id>
          <name>Oss Snapshot Repository</name>
          <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
      </distributionManagement>
    </profile>
    <profile>
      <id>nexus</id>
      <build>
        <plugins>
          <!-- GPG -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>1.5</version>
            <executions>
              <execution>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <distributionManagement>
        <!-- 发布版私服仓库 -->
        <repository>
          <id>xkcoding-nexus</id>
          <name>xkcoding Nexus Release Repository</name>
          <url>https://nexus.xkcoding.com/repository/maven-releases/</url>
        </repository>
        <!-- 快照版私服仓库 -->
        <snapshotRepository>
          <id>xkcoding-nexus</id>
          <name>xkcoding Nexus Snapshot Repository</name>
          <url>https://nexus.xkcoding.com/repository/maven-snapshots/</url>
        </snapshotRepository>
      </distributionManagement>
    </profile>
    <profile>
      <id>github</id>
      <build>
        <plugins>
          <!-- GPG -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>1.5</version>
            <executions>
              <execution>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <distributionManagement>
        <!-- 发布到GitHub仓库 -->
        <repository>
          <id>github-xkcoding</id>
          <name>xkcoding</name>
          <url>https://maven.pkg.github.com/xkcoding/justauth-spring-boot-starter</url>
        </repository>
      </distributionManagement>
    </profile>
  </profiles>

</project>
