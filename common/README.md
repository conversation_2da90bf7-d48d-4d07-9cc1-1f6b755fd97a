# common

## 模組概述
公共模組(common)提供系統基礎功能和工具類，包括安全配置、JWT工具、異常處理、事件發佈、分散式鎖等核心組件。

## 主要功能
- **安全配置**：提供Spring Security基礎配置和JWT認證過濾器
- **JWT工具**：提供JWT令牌生成、解析等工具方法
- **異常處理**：提供基礎異常類和業務異常類
- **響應封裝**：統一API響應格式封裝
- **事件發佈**：支援Azure Service Bus和Kafka事件發佈
- **分散式鎖**：基於Redisson的分散式鎖實現
- **工具類**：提供JSON處理、HTTP請求、上下文工具等常用工具方法

## 專案類型說明

**重要**: 本專案是公共工具庫(JAR包)，不是獨立的微服務應用，因此：

- ❌ **不需要獨立部署**：作為依賴庫被其他服務引用
- ❌ **不需要K8s配置**：無需Deployment、Service、Ingress等配置
- ❌ **不需要健康檢查**：不是運行時服務
- ❌ **不需要服務發現**：通過Maven依賴管理

## 依賴服務
- Spring Boot 3.2.3
- Spring Security
- JJWT (JWT處理庫)
- Azure Service Bus (事件發佈)
- Redisson (分散式鎖)
- MyBatis Plus
- MapStruct

## 使用說明

### 1. 添加Maven依賴
在需要使用此公共庫的服務專案中添加依賴：

```xml
<dependency>
    <groupId>com.yuanchuan.common</groupId>
    <artifactId>common</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2. 配置說明

#### JWT配置 (可選)
```properties
# JWT密鑰配置，預設使用"yuanchuan"
jwt.secret=your-secret-key
jwt.expiration=86400000
```

#### Azure Service Bus配置 (如使用事件發佈功能)
```yaml
spring:
  cloud:
    azure:
      servicebus:
        connection-string: ${AZURE_SERVICEBUS_CONNECTION_STRING}
```

#### Redisson配置 (如使用分散式鎖功能)
```yaml
spring:
  redis:
    redisson:
      config: |
        singleServerConfig:
          address: "redis://localhost:6379"
```

### 3. 使用範例

#### JWT工具使用
```java
// 生成JWT令牌
String token = JwtUtils.generateToken(userDetails);

// 解析JWT令牌
Claims claims = JwtUtils.parseToken(token);
```

#### 事件發佈使用
```java
@Autowired
private DomainEventPublisher eventPublisher;

// 發佈事件
CommonEvent event = new CommonEvent();
event.setEventId(UUID.randomUUID().toString());
event.setAggregateId("merchant-123");
eventPublisher.publish(event);
```

#### 分散式鎖使用
```java
@Autowired
private DistributedLock distributedLock;

// 嘗試獲取鎖
boolean locked = distributedLock.tryLock("lock-key", 5000, 30000);
if (locked) {
    try {
        // 執行業務邏輯
    } finally {
        distributedLock.unlock("lock-key");
    }
}
```

#### 統一響應格式
```java
// 成功響應
return Result.success(data);

// 分頁響應
PageResult<User> pageResult = PageResult.of(userList, total, page, size);
return Result.success(pageResult);

// 錯誤響應
return Result.error("操作失敗");
```

## 開發和構建

### 本地開發
```bash
# 編譯專案
mvn clean compile

# 運行測試
mvn test

# 打包
mvn clean package

# 安裝到本地倉庫
mvn clean install
```

### 發佈到私有倉庫
```bash
# 發佈到私有Maven倉庫
mvn clean deploy -Drepository.id=your-repo-id \
                 -Drepository.name=your-repo-name \
                 -Drepository.url=your-repo-url
```

## 版本管理

### 版本號規範
- **主版本號**: 不相容的API修改
- **次版本號**: 向下相容的功能性新增
- **修訂號**: 向下相容的問題修正

### 發佈流程
1. 更新版本號 (pom.xml)
2. 更新CHANGELOG.md
3. 建立Git標籤
4. 發佈到Maven倉庫
5. 通知相關團隊更新依賴

## 依賴此庫的服務

使用此公共庫的服務在K8s部署時需要注意：

### 服務配置要求
```yaml
# 使用此庫的服務需要的基礎配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: your-service
spec:
  template:
    spec:
      containers:
      - name: your-service
        env:
        # JWT配置
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        # Azure Service Bus配置 (如使用事件功能)
        - name: AZURE_SERVICEBUS_CONNECTION_STRING
          valueFrom:
            secretKeyRef:
              name: azure-servicebus-secret
              key: connection-string
        # Redis配置 (如使用分散式鎖功能)
        - name: REDIS_HOST
          value: "redis-service"
        - name: REDIS_PORT
          value: "6379"
```

### 依賴服務清單
使用此公共庫的服務可能需要以下外部依賴：

1. **Redis** (分散式鎖功能)
   - Service: `redis-service:6379`
   - 用途: 分散式鎖存儲

2. **Azure Service Bus** (事件發佈功能)
   - 連接字串通過Secret配置
   - 用途: 事件訊息發佈

3. **資料庫** (如使用MyBatis Plus功能)
   - MySQL/PostgreSQL等
   - 用途: 資料持久化

## 模組結構
```
config/           # 配置類
constant/         # 常數定義
context/          # 上下文相關
domain/           # 領域物件
enums/           # 列舉類
event/           # 事件相關
  ├── azure/     # Azure Service Bus事件
  └── kafka/     # Kafka事件 (如有)
exception/       # 異常處理
mapstruct/       # 物件映射
response/        # 響應封裝
spi/             # SPI介面
  └── lock/      # 分散式鎖
utils/           # 工具類
```

## 注意事項

1. **版本相容性**: 升級此庫版本時，需要測試所有依賴服務的相容性
2. **配置管理**: 各服務需要根據使用的功能模組配置相應的環境變數
3. **依賴衝突**: 注意與服務自身依賴的版本衝突問題
4. **功能模組化**: 可以選擇性使用庫中的功能，不必全部啟用
5. **安全考慮**: JWT密鑰等敏感配置應通過K8s Secret管理

## 聯絡方式
如有問題或建議，請聯絡開發團隊或提交Issue。