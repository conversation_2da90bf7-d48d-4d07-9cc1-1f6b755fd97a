package com.yuanchuan.common.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 通用分页结果包装类
 * @param <T> 列表数据类型
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PageResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;  // 添加序列化ID

    
    private List<T> records;      // 数据列表
    private long total;           // 总记录数
    private int pageSize;         // 每页大小
    private int pageNum;          // 当前页码

    public PageResult(int i, List<T> collect) {
        this.records = collect;
        this.total = i;
        this.pageSize = collect.size();
        this.pageNum = 1;
    }

    public List<T> getRecords() {
        return records;
    }

    public void setRecords(List<T> records) {
        this.records = records;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    /**
     * 获取总页数
     */
    public long getPages() {
        if (pageSize == 0) {
            return 0L;
        }
        return (total + pageSize - 1) / pageSize;
    }

    /**
     * 创建分页响应
     *
     * @param list 数据列表
     * @param total 总数
     * @param page 页码
     * @param size 每页大小
     * @param <T> 数据类型
     * @return 分页响应
     */
    public static <T> PageResult<T> of(List<T> list, long total, int page, int size) {
        return PageResult.<T>builder()
                .records(list)
                .total(total)
                .pageNum(page)
                .pageSize(size)
                .build();
    }
}