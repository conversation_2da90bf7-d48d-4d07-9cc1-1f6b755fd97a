package com.yuanchuan.common.response;
public enum ResponseCode {
    INVALID_REQUEST(400, "Invalid request"),
    UNAUTHORIZED(401, "Unauthorized"),
    FORBIDDEN(403, "Forbidden"),
    NOT_FOUND(404, "Not found"),
    INTERNAL_SERVER_ERROR(500, "Internal server error"),


    ERROR_2(2001, "error 2"),
    ERROR_3(3001, "error 3"),


    /**
     * 参数异常相关
     */
    /**
     * 参数不能为空
     */
    PARAM_NOT_NULL(10001, "參數不能為空"),

    /**
     * 参数格式不正确
     */
    PARAM_FORMAT_ERROR(10002, "參數格式不正確"),

    /**
     * 参数校验失败
     */
    PARAM_VALIDATE_FAILED(10003, "參數驗證失敗"),

    /**
     * 缺少必要参数
     */
    PARAM_MISSING(10004, "缺少必要參數"),


    ;

    private final int code;
    private final String message;

    ResponseCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
