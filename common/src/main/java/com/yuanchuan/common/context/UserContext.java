package com.yuanchuan.common.context;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserContext implements Serializable {

    private Long userId;
    private Long businessAccountId;
    private AuthAccountDeviceApiDTO deviceInfo;
    private String username;
    private String deviceId;
    private String platform;
    private List<String> roles;
    private List<String> permissions;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AuthAccountDeviceApiDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        private Long userId;
        private Long businessAccountId;
        private AuthAccountDeviceApiDTO deviceInfo;
        private String username;
        private String deviceId;
        private String platform;
        private List<String> roles;
        private List<String> permissions;
    }

}
