package com.yuanchuan.common.domain.entity;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yuanchuan.common.enums.ACTIVE;
import com.yuanchuan.common.utils.ContextUtils;
import lombok.Data;
import java.time.LocalDateTime;

import java.io.Serializable;
import java.util.Date;
import java.util.TimeZone;

import static com.baomidou.mybatisplus.annotation.FieldFill.INSERT;
import static com.baomidou.mybatisplus.annotation.FieldFill.INSERT_UPDATE;

/**
 * Entity基类
 */
@Data
public class BaseEntity implements Serializable {

    private static final long serialVersionUID = 7840215182457065832L;
    
    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = INSERT)
    private LocalDateTime createdAt;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = INSERT)
    private Long createdBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_at", fill = INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 修改人
     */
    @TableField(value = "updated_by", fill = INSERT_UPDATE)
    private Long updatedBy;



    /**
     * 可用标识。（数据库字段：active，1启用，0禁用）
     */
    @TableField("active")
    private Integer active;



    public void initCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdAt = now;
        this.createdBy = ContextUtils.getCurrentUserId();
        this.updatedAt = now;
        this.updatedBy = ContextUtils.getCurrentUserId();
        active = ACTIVE.Y.getValue();
    }

    public void setCreatedInfo(Long userId) {
        LocalDateTime now = LocalDateTime.now();
        this.createdAt = now;
        this.createdBy = userId;
    }

    public void setUpdatedInfo(Long userId) {
        LocalDateTime now = LocalDateTime.now();
        this.updatedAt = now;
        this.updatedBy = userId;
    }
}
