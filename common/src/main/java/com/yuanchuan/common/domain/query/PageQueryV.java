package com.yuanchuan.common.domain.query;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-05-28 15:53:15:53
 */

@Data
@ToString
@EqualsAndHashCode
@Schema(
        description = "分页查询参数"
)
public class PageQueryV implements Serializable {

    /**
     * 默认页大小
     */

    public static final int DEFAULT_PAGE_SIZE = 10;

    /**
     * 最大页大小
     */
    public static final int MAX_PAGE_SIZE = 2000;

    /**
     * 分页开关
     */
    @Schema(
            description = "分页开关"
    )
    @NotNull(message = "分頁開關不能為空")
    private Boolean usePaging = true;

    @Schema(
            description = "页码"
    )
    @NotNull(message = "頁碼不能為空")
    private Integer pageNo;

    @Schema(
            description = "分页大小"
    )
    @NotNull(message = "分頁大小不能為空")
    private Integer pageSize;

    private String sortColumns;

    public Integer getPageNo() {
        if (this.pageNo == null) {
            this.pageNo = 1;
        } else if (this.pageNo < 1) {
            this.pageNo = 1;
        }
        return pageNo;
    }

    public Integer getPageSize() {
        if (this.pageSize == null) {
            this.pageSize = DEFAULT_PAGE_SIZE;
        } else if (this.pageSize < 1) {
            this.pageSize = DEFAULT_PAGE_SIZE;
        } else if (this.pageSize > MAX_PAGE_SIZE) {
            this.pageSize = MAX_PAGE_SIZE;
        }
        return pageSize;
    }

    public int getOffset() {
        return (this.getPageNo() - 1) * this.getPageSize();
    }

}

