package com.yuanchuan.common.domain.query;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 分页查询参数基类，分页查询入参和返回基类
 *
 * @param <T> Mapper的返回值分页的泛型类型
 */
@Data
public class PageQuery<T> implements IPage<T>, Serializable {

    private static final long serialVersionUID = 2101820826443930585L;

    /**
     * 当前页码
     */
    @NotNull(message = "pageNum为空")
    @Schema(description = "当前页码", example = "1", required = true)
    private Integer pageNum;

    /**
     * 每页大小
     */
    @NotNull(message = "pageSize为空")
    @Schema(description = "每页大小", example = "10", required = true)
    private Integer pageSize;

    /**
     * 排序字段列表
     */
    @Schema(description = "排序字段列表")
    private List<OrderItem> orders;

    /**
     * 分页数据
     */
    @Schema(description = "分页数据", hidden = true)
    private List<T> records;

    /**
     * 记录总数
     */
    @Schema(description = "记录总数", hidden = true)
    private long total;

    /**
     * 升序字段列表
     */
    @Schema(description = "升序字段列表")
    private List<String> ascs;

    /**
     * 降序字段列表
     */
    @Schema(description = "降序字段列表")
    private List<String> descs;

    @Schema(hidden = true)
    private Long size;

    @Schema(hidden = true)
    private Long current;

    @Schema(hidden = true)
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private Long pages;

    public PageQuery() {
        this.records = Collections.emptyList();
        this.total = 0L;
        this.pageSize = 10;
        this.pageNum = 1;
        this.orders = new ArrayList<>();
    }

    public PageQuery(int pageNum, int pageSize) {
        this(pageNum, pageSize, 0);
    }

    public PageQuery(int pageNum, int pageSize, int total) {
        this.records = Collections.emptyList();
        this.pageNum = 1;
        this.orders = new ArrayList<>();
        if (pageNum > 1) {
            this.pageNum = pageNum;
        }

        this.pageSize = pageSize;
        this.total = total;
    }

    @Override
    public long getSize() {
        return this.pageSize;
    }

    @Override
    public IPage<T> setSize(long size) {
        this.pageSize = (int)size;
        return this;
    }

    @Override
    public long getCurrent() {
        return pageNum;
    }

    @Override
    public IPage<T> setCurrent(long current) {
        this.pageSize = (int)current;
        return this;
    }

    @Override
    public List<T> getRecords() {
        return this.records;
    }

    @Override
    public PageQuery<T> setRecords(List<T> records) {
        this.records = records;
        return this;
    }

    @Override
    public long getTotal() {
        return this.total;
    }

    @Override
    public PageQuery<T> setTotal(long total) {
        this.total = total;
        return this;
    }

    @Override
    public List<OrderItem> orders() {
        return this.orders;
    }

}
