package com.yuanchuan.common.enums.users.login;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 登录类型枚举
 */
@Schema(description = "登录类型")
public enum LoginType {
    /**
     * 短信验证码登录
     */
    SMS_CODE(0, "SMS_CODE","短信验证码登录"),

    /**
     * 邮箱验证码登录
     */
    EMAIL_CODE(1, "EMAIL_CODE","邮箱验证码登录"),

    /**
     * 账号密码登录
     */
    PASSWORD(2, "PASSWORD","账号密码登录"),

    /**
     * 第三方登录
     */
    THIRD_PARTY(3, "THIRD_PARTY","第三方登录"),

    /**
     * 统一登录流程
     */
    UNIFIED(4, "UNIFIED","统一登录流程");

    /**
     * 编码
     */
    private final int code;

    /**
     * 英文名称
     */
    private final String name;

    /**
     * 中文描述
     */
    private final String description;

    LoginType(int code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 获取编码
     */
    @JsonValue
    public int getCode() {
        return code;
    }
    /**
     * 获取英文名称
     */
    public String getName() {
        return name;
    }


    /**
     * 获取中文描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据编码获取枚举实例
     */
    @JsonCreator
    public static LoginType fromCode(int code) {
        for (LoginType type : LoginType.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的登录类型编码: " + code);
    }
}
