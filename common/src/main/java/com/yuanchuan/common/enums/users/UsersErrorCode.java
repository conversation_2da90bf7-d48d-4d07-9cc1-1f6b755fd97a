package com.yuanchuan.common.enums.users;

import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @date 2025-05-14 10:54:10:54
 */
@Schema(description = "异常场景提示code")
public enum UsersErrorCode {

    /**
     * 用户通用code码
     * 注意：msg字段为占位符，使用时需要提供具体的错误消息
     * 示例：new BusinessException(UsersErrorCode.USER_PUBLIC_CODE.getCode(), "具体的错误消息")
     */
    USER_PUBLIC_CODE(100000, "{0}"),
    /**
     * 用户不存在
     */
    USER_NOT_EXIST(100001, "用戶不存在"),

    /**
     * 用户存在未处理的资产
     */
    HAVE_PROPERTY(100002, "用戶存在未處理的資產"),

    /**
     * 用户账号不存在
     */
    USER_ACCOUNT_NOT_EXIST(100003, "用戶賬號不存在"),

    /**
     * 用户设备信息不存在
     */
    USER_DEVICE_NOT_EXIST(100004, "用戶設備信息不存在"),

    /**
     * 登录授权失败
     */
    USER_AUTHORIZATION_ERROR(100005, "登錄授權失敗"),

    /**
     * 登出失败
     */
    USER_LOGOUT_ERROR(100006, "登出失敗"),

    /**
     * 注销失败
     */
    USER_CANCEL_ERROR(100007, "註銷失敗"),

    /**
     * 敏感词校验失败
     */
    WORD_SENSITIVE_ERROR(100008, "敏感詞校驗失敗"),

    /**
     * 昵称更新失败
     */
    NICKNAME_UPDATE_ERROR(100009, "昵稱更新失敗"),

    /**
     * 头像更新失败
     */
    AVATAR_UPDATE_ERROR(100010, "頭像更新失敗"),

    /**
     * 性别校验失败
     */
    GENDER_CHECK_ERROR(100011, "性別值不正確，只能是男 、女 或 未知"),

    /**
     * 性别更新失败
     */
    GENDER_UPDATE_ERROR(100012, "性別更新失敗"),


    /**
     * 生日更新失败
     */
    BIRTHDAY_UPDATE_ERROR(100013, "生日更新失敗"),

    /**
     * 常居地更新失败
     */
    ADDRESS_UPDATE_ERROR(100014, "常居地更新失敗"),

    /**
     * 获取用户信息失败
     */
     USER_INFO_ERROR(100015, "獲取用戶信息失敗"),


    /**
     * 短信验证码校验失败
     */
    SMS_CODE_CHECK_ERROR(100016, "短信驗證碼校驗失敗"),


    /**
     * 邮箱验证码校验失败
     */
    EMAIL_CODE_CHECK_ERROR(100017, "郵箱驗證碼校驗失敗"),


    /**
     * 手机号不匹配
     */
    PHONE_NOT_MATCH(100018, "手機號不匹配"),


    /**
     * 邮箱不匹配
     */
    EMAIL_NOT_MATCH(100019, "郵箱賬號與綁定不一致"),


    /**
     * 手机号已被注册
     */
    PHONE_ALREADY_REGISTERED(100020, "該手機號已關聯其他賬號"),


    /**
     * 邮箱已被注册
     */
    EMAIL_ALREADY_REGISTERED(100021, "郵箱已被註冊"),


    /**
     * 设备限制超限
     */

    DEVICE_LIMIT_EXCEEDED(100023, "每天每個設備最多換綁3次手機號"),



    /**
     * 更新手机号失败
     */
    PHONE_UPDATE_ERROR(100025, "換綁手機號失敗"),

    /**
     * 更新邮箱失败
     */
    EMAIL_UPDATE_ERROR(100026, "綁定郵箱失敗"),

    /**
     * 密码不符合规则
     */
    PASSWORD_POLICY_ERROR(100027, "密碼不符合規則"),

    /**
     * 密码更新失败
     */
    PASSWORD_UPDATE_ERROR(100028, "密碼更新失敗"),

    /**
     * 密码校验失败
     */
    PASSWORD_VERIFY_ERROR(100029, "密碼校驗失敗"),

    /**
     * 获取三方绑定平台失败
     */
    THIRD_PARTY_BIND_ERROR(100030, "獲取三方綁定平台失敗"),

    /**
     * 绑定信息不存在
     */
    BINDING_NOT_EXIST(100031, "綁定信息不存在"),


    /**
     * 绑定信息不属于该用户
     */
    BINDING_NOT_BELONG(100032, "綁定信息不屬於該用戶"),

    /**
     * 第三方账号已被绑定
     */
    THIRD_PARTY_ALREADY_BOUND(100033, "第三方賬號已被綁定"),

    /**
     * 解绑失败
     */
    UNBIND_FAILED(100034, "解綁失敗"),

    /**
     * 绑定失败
     */
    BIND_FAILED(100035, "綁定失敗"),

    /**
     * 第三方授权失败
     */
    AUTHORIZATION_FAILED(100036, "第三方授權失敗"),

    /**
     * 角色不存在
     */
    ROLE_NOT_EXIST(100037, "角色不存在"),

    /**
     * 角色分配失败
     */
    ROLE_ASSIGN_FAILED(100038, "角色分配失敗"),

    /**
     * 未找到匹配的地址信息
     */
    ADDRESS_NOT_FOUND(100039, "未找到匹配的地址信息"),

    /**
     * 地址更新失败
     */
    ADDRESS_UPDATE_FAILED(100040, "地址更新失敗"),

    /**
     * 创建商户账户失败
     */
    MERCHANT_ACCOUNT_CREATE_ERROR(100041, "創建商戶賬戶失敗: 賬戶信息異常"),

    /**
     * 不支持的登录类型或状态
     */
    UNSUPPORTED_LOGIN_TYPE_OR_STATUS(100042, "不支持的登錄類型或狀態"),

    /**
     * 登录失败
     */
    LOGIN_FAILED(100043, "登錄失敗"),

    /**
     * 创建账号失败
     */
    ACCOUNT_CREATE_FAILED(100044, "創建賬號失敗"),

    /**
     * 账号格式不正确
     */
    ACCOUNT_FORMAT_ERROR(100045, "賬號格式不正確，請輸入正確的手機號或郵箱"),

    /**
     * 用户信息异常
     */
    USER_INFO_EXCEPTION(100046, "登錄失敗: 用戶信息異常"),

    /**
     * 账号或密码错误
     */
    ACCOUNT_OR_PASSWORD_ERROR(100047, "賬號或密碼錯誤"),

    /**
     * 参数验证错误
     */
    PARAMETER_VALIDATION_ERROR(100048, "參數驗證錯誤"),

    /**
     * 用户基本信息不存在
     */
    USER_BASIC_INFO_NOT_EXIST(100049, "用戶基本信息不存在"),

    /**
     * 手机号格式错误
     */
    PHONE_FORMAT_ERROR(100050, "手機號格式不正確，請輸入有效的台灣手機號"),

    /**
     * 邮箱格式错误
     */
    EMAIL_FORMAT_ERROR(100051, "郵箱格式不正確，請輸入有效的郵箱"),

    /**
     * 验证码发送频率限制
     */
    VERIFICATION_CODE_RATE_LIMIT(100052, "請勿頻繁發送驗證碼"),

    /**
     * 账号不能为空
     */
    ACCOUNT_EMPTY(100053, "賬號不能為空"),

    /**
     * 密码不能为空
     */
    PASSWORD_EMPTY(100054, "密碼不能為空"),

    /**
     * 手机号不能为空
     */
    PHONE_EMPTY(100055, "手機號不能為空"),

    /**
     * 验证码不能为空
     */
    VERIFICATION_CODE_EMPTY(100056, "驗證碼不能為空"),

    /**
     * 邮箱不能为空
     */
    EMAIL_EMPTY(100057, "郵箱不能為空"),

    /**
     * 临时令牌无效或已过期
     */
    TEMPORARY_TOKEN_INVALID(100058, "臨時令牌無效或已過期"),

    /**
     * 账号使用标志不能为空
     */
    ACCOUNT_USE_FLAG_EMPTY(100059, "賬號使用標誌不能為空"),

    /**
     * 邮箱不能为空（验证邮箱时）
     */
    EMAIL_EMPTY_VERIFY(100060, "驗證郵箱不能為空"),

    /**
     * 性别不能为空
     */
    GENDER_EMPTY(100061, "性別不能為空"),

    /**
     * 生日不能为空
     */
    BIRTHDAY_EMPTY(100062, "生日不能為空"),

    /**
     * 生日不能是未来日期
     */
    BIRTHDAY_FUTURE(100063, "生日不能是未來日期"),

    /**
     * 新手机号不能为空
     */
    NEW_PHONE_EMPTY(100064, "新手機號不能為空"),

    /**
     * 密码不能为空（设置密码时）
     */
    PASSWORD_EMPTY_SET(100065, "設置密碼不能為空"),

    /**
     * 密码不能为空（验证密码时）
     */
    PASSWORD_EMPTY_VERIFY(100066, "驗證密碼不能為空"),

    /**
     * 手机号、邮箱或账号不能同时为空
     */
    ACCOUNT_ALL_EMPTY(100067, "手機號、郵箱或賬號不能同時為空"),

    /**
     * 设备信息不能为空
     */
    DEVICE_INFO_EMPTY(100068, "設備信息不能為空"),

    /**
     * 用户ID不能为空
     */
    USER_ID_EMPTY(100069, "用戶ID不能為空"),

    /**
     * 设备ID不能为空
     */
    DEVICE_ID_EMPTY(100070, "設備ID不能為空"),

    /**
     * 设备不存在
     */
    DEVICE_NOT_EXIST(100071, "設備不存在"),

    /**
     * 设备不属于该用户
     */
    DEVICE_NOT_BELONG_TO_USER(100072, "設備不屬於該用戶"),

    /**
     * 新设备名称不能为空
     */
    NEW_DEVICE_NAME_EMPTY(100073, "新設備名稱不能為空"),

    /**
     * 操作参数不能为空
     */
    OPERATION_PARAM_EMPTY(100074, "操作參數不能為空"),

    /**
     * Token请求不能为空
     */
    TOKEN_REQUEST_EMPTY(100075, "Token請求不能為空"),

    /**
     * 平台类型不能为空
     */
    PLATFORM_TYPE_EMPTY(100076, "平台類型不能為空"),

    /**
     * 用户名不能为空
     */
    USERNAME_EMPTY(100077, "用戶名不能為空"),

    /**
     * Token不能为空
     */
    TOKEN_EMPTY(100078, "Token不能為空"),

    /**
     * Token无效或已过期
     */
    TOKEN_INVALID_OR_EXPIRED(100079, "Token無效或已過期"),

    /**
     * 刷新Token已过期
     */
    REFRESH_TOKEN_EXPIRED(100080, "刷新Token已過期"),

    /**
     * 非法的刷新Token
     */
    INVALID_REFRESH_TOKEN(100081, "非法的刷新Token"),

    /**
     * 刷新Token无效或已过期
     */
    REFRESH_TOKEN_INVALID_OR_EXPIRED(100082, "刷新Token無效或已過期"),

    /**
     * 刷新Token验证失败
     */
    REFRESH_TOKEN_VALIDATION_FAILED(100083, "刷新Token驗證失敗"),

    /**
     * 邮箱未绑定手机号
     */
    EMAIL_NOT_BINDING_PHONE(100084, "郵箱未綁定手機號"),

    /**
     * 二次验证邮箱不匹配
     */
    SECONDARY_VERIFICATION_EMAIL_MISMATCH(100085, "二次驗證郵箱不匹配，請重新登錄"),

    /**
     * 二次验证手机号不匹配
     */
    SECONDARY_VERIFICATION_PHONE_MISMATCH(100086, "二次驗證手機號不匹配，請重新登錄"),

    /**
     * 二次验证用户信息异常
     */
    SECONDARY_VERIFICATION_USER_INFO_ERROR(100087, "二次驗證用戶信息異常，請重新開始登錄"),

    /**
     * 二次验证状态已过期
     */
    SECONDARY_VERIFICATION_STATE_EXPIRED(100088, "二次驗證狀態已過期，請重新開始登錄"),

    /**
     * 二次验证过程中请勿修改账户信息
     */
    SECONDARY_VERIFICATION_ACCOUNT_CHANGE_FORBIDDEN(100089, "二次驗證過程中，請勿修改賬戶信息"),

    /**
     * 用户手机号不存在，无法进行二次验证
     */
    SECONDARY_VERIFICATION_PHONE_NOT_EXIST(100090, "用戶手機號不存在，無法進行二次驗證"),

    /**
     * 二次验证邮箱不匹配或已被更换
     */
    SECONDARY_VERIFICATION_EMAIL_CHANGED(100091, "二次驗證郵箱不匹配或已被更換，請重新開始登錄"),

    /**
     * 设备ID不能为空
     */
    DEVICE_ID_REQUIRED(100092, "設備ID不能為空"),

    /**
     * 二次验证令牌无效
     */
    SECONDARY_VERIFICATION_TOKEN_INVALID(100093, "二次驗證令牌無效或已過期"),

    /**
     * 操作正在进行中
     */
    OPERATION_IN_PROGRESS(100094, "操作正在進行中，請稍後重試"),

    /**
     * 系统错误
     */
    SYSTEM_ERROR(100095, "系統錯誤，請稍後重試"),

    /**
     * 设备绑定失败
     */
    DEVICE_BINDING_FAILED(100096, "設備繫定失敗，將稍後重試"),

    /**
     * 短信登录失败
     */
    SMS_LOGIN_FAILED(100097, "短信登錄失敗，請稍後重試"),

    /**
     * 邮箱登录失败
     */
    EMAIL_LOGIN_FAILED(100098, "郵箱登錄失敗，請稍後重試"),

    /**
     * 密码登录失败
     */
    PASSWORD_LOGIN_FAILED(100099, "密碼登錄失敗，請稍後重試"),

    /**
     * 邮箱格式错误
     */
    EMAIL_FORMAT_INVALID(100100, "郵箱格式不正確"),

    /**
     * IP地址变化风险警告
     */
    IP_ADDRESS_CHANGED_WARNING(100101, "IP地址發生變化，請注意賬戶安全"),

    /**
     * 第三方登录失败
     */
    THIRD_PARTY_LOGIN_FAILED(100102, "第三方登錄失敗，請稍後重試"),

    /**
     * 角色名称已存在
     */
    ROLE_NAME_EXIST(100085, "角色名稱已經存在"),

    /**
     * 权限名称已存在
     */
    PERMISSION_NAME_EXIST(100086, "權限名稱已經存在"),

    /**
     * 权限编码已存在
     */
    PERMISSION_CODE_EXIST(100087, "權限編碼已經存在"),

    /**
     * 权限不存在
     */
    PERMISSION_NOT_EXIST(100088, "權限不存在"),

    /**
     * 用户关联此角色不允许删除
     */
    USER_RELATED_ROLE_NOT_ALLOWED_TO_DELETE(100089, "用戶關聯此角色不允許刪除"),


    /**
     * 角色关联权限不允许删除
     */
    ROLE_RELATED_PERMISSION_NOT_ALLOWED_TO_DELETE(100091, "角色關聯權限不允許刪除"),

    /**
     * 角色关联用户不允许删除
     */
    ROLE_RELATED_USER_NOT_ALLOWED_TO_DELETE(100092, "角色關聯用戶不允許刪除"),

    /**
     * 角色code已经存在
     */
    ROLE_CODE_EXIST(100093, "角色code已經存在"),


    /**
     * 只能修改运营账号
     */
    ONLY_MODIFY_ADMIN_ACCOUNT(100094, "只能修改运营賬號"),


    /**
     * 创建账号失败
     */
    CREATE_ACCOUNT_FAILED(100095, "創建賬號失敗"),


    /**
     * 修改账号失败
     */
    MODIFY_ACCOUNT_FAILED(100096, "修改賬號失敗"),

    /**
     * 姓名不能為空且不能超過20個字元
     */
    NAME_EMPTY_OR_TOO_LONG(100097, "姓名不能為空且不能超過20個字元"),

    /**
     * 手机号格式不正确
     */
    PHONE_FORMAT_INVALID(100098, "手機號格式不正確"),

    /**
     * 员工编码不能为空
     */
    EMPLOYEE_CODE_EMPTY(100099, "員工編碼不能為空"),

    /**
     * 角色不能为空
     */
    ROLE_EMPTY(100100, "角色不能為空"),

    /**
     * 员工编码已经存在
     */
    EMPLOYEE_CODE_EXIST(100101, "員工編碼已經存在"),


    /**
     * 角色已禁用
     */
    ROLE_DISABLED(100102, "角色已禁用"),


    ;

    /**
     * 编码
     */
    private final int code;

    /**
     * 英文名称
     */
    private final String msg;


    UsersErrorCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * 获取编码
     */
    @JsonValue
    public int getCode() {
        return code;
    }

    /**
     * 获取英文名称
     */
    public String getMsg() {
        return msg;
    }

}
