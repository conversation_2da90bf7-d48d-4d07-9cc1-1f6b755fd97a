package com.yuanchuan.common.enums;

/**
 * <AUTHOR>
 * 卡有效类型: 1-永久 2-购买时 3-首次使用时
 */
public enum ACTIVE {

    /**
     * 有效
     */
    Y(1, "有效"),
    /**
     * 无效
     */
    N(0, "无效"),
    ;

    /**
     * 枚举的值
     */
    private final Integer value;
    /**
     * 枚举的含义
     */
    private final String name;

    ACTIVE(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static ACTIVE getByValue(Integer value) {
        for (ACTIVE itemType : values()) {
            if (itemType.value.equals(value)) {
                return itemType;
            }
        }
        return null;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
