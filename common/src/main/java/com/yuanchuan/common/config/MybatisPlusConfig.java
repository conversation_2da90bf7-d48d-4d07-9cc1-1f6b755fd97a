package com.yuanchuan.common.config;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.yuanchuan.common.enums.ACTIVE;
import com.yuanchuan.common.utils.ContextUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.util.TimeZone;

@Configuration
public class MybatisPlusConfig {


    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL)); // 根据你的数据库类型
        return interceptor;
    }

    /**
     * 自动填充部分数据（createAt、updateAt）
     */
    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new MetaObjectHandler() {
            @Override
            public void insertFill(MetaObject metaObject) {
                // 插入的时候设置添加和修改时间的值
                this.setFieldValByName("createdAt", LocalDateTime.now(), metaObject);
                this.setFieldValByName("updatedAt", LocalDateTime.now(), metaObject);
//                this.setFieldValByName("createdBy", ContextUtils.getCurrentUserId(), metaObject);
//                this.setFieldValByName("updatedBy", ContextUtils.getCurrentUserId(), metaObject);
                this.setFieldValByName("active", ACTIVE.Y.getValue(), metaObject);
            }

            @Override
            public void updateFill(MetaObject metaObject) {
                // 更新的时候设置修改时间的值
                this.setFieldValByName("updatedAt", LocalDateTime.now(), metaObject);
//                this.setFieldValByName("updateBy", SecurityUtils.getUserIdOrNull(), metaObject);
            }
        };
    }

}
