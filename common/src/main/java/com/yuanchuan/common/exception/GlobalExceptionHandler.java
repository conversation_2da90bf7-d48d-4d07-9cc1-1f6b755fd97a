package com.yuanchuan.common.exception;


import com.yuanchuan.common.constant.HttpStatus;
import com.yuanchuan.common.response.R;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDeniedException;

import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;


import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.yuanchuan.common.response.R.fail;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    /**
     * 权限校验异常
     */
    @ExceptionHandler(AccessDeniedException.class)
    public R handleAccessDeniedException(AccessDeniedException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        log.warn("请求地址'{}',权限校验失败'{}'", requestUri, e.getMessage());
        return fail(HttpStatus.FORBIDDEN, "没有权限，请联系管理员授权");
    }

    /**
     * 请求方式不支持
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public R handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException e,
                                                 HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        log.error("请求地址'{}',不支持'{}'请求", requestUri, e.getMethod());
        return fail(e.getMessage());
    }



    /**
     * 拦截未知的运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public R handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        log.error("请求地址'{}',发生未知异常.", requestUri, e);
        return fail("系统发生异常");
    }

    /**
     * 捕获文件太大异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public R<Void> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException exception,
                                                        HttpServletRequest request) {
        log.error("上传文件大小超出限制, 请求uri: {}", request.getRequestURI(), exception);
        return fail("上传文件大小超出限制: " + exception.getMaxUploadSize());
    }

    /**
     * 系统异常
     */
    @ExceptionHandler(Exception.class)
    public R handleException(Exception e, HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        log.error("请求地址'{}',发生系统异常.", requestUri, e);
        return fail("系统发生异常");
    }


    /**
     * 处理@RequestBody参数校验异常
     * 当使用@Valid注解校验请求体参数失败时触发
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R<Map<String, String>> handleValidationExceptions(MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        log.warn("请求体参数校验失败: {}", errors);

        R<Map<String, String>> result = R.fail(HttpStatus.BAD_REQUEST, "请求参数不合法");
        result.setData(errors);
        return result;
    }

    /**
     * 处理@RequestParam/@PathVariable参数校验异常
     * 当使用@Validated在类级别校验URL参数失败时触发
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public R<Map<String, String>> handleConstraintViolationException(ConstraintViolationException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getConstraintViolations().forEach(violation -> {
            String fieldPath = violation.getPropertyPath().toString();
            String fieldName = fieldPath.substring(fieldPath.lastIndexOf('.') + 1);
            errors.put(fieldName, violation.getMessage());
        });
        log.warn("URL参数校验失败: {}", errors);

        R<Map<String, String>> result = R.fail(HttpStatus.BAD_REQUEST, "URL参数不合法");
        result.setData(errors);
        return result;
    }

    /**
     * 处理绑定异常（如类型转换失败）
     * 当请求参数类型与控制器方法参数类型不匹配时触发
     */
    @ExceptionHandler(BindException.class)
    public R<Map<String, String>> handleBindException(BindException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            errors.put(fieldName, error.getDefaultMessage());
        });
        log.warn("参数绑定失败: {}", errors);

        R<Map<String, String>> result = R.fail(HttpStatus.BAD_REQUEST, "参数类型不匹配");
        result.setData(errors);
        return result;
    }


}

