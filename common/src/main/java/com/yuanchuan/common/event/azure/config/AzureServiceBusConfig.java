package com.yuanchuan.common.event.azure.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import com.azure.messaging.servicebus.ServiceBusClientBuilder;
import com.yuanchuan.common.event.azure.publisher.AzureServiceBusDomainEventPublisher;

/**
 * Azure Service Bus配置类
 */
@Configuration
public class AzureServiceBusConfig {

    @Value("${azure.servicebus.connection-string}")
    private String connectionString;

    /**
     * 创建Azure Service Bus客户端构建器
     *
     * @return ServiceBusClientBuilder实例
     */
    @Bean
    public ServiceBusClientBuilder serviceBusClientBuilder() {
        return new ServiceBusClientBuilder()
                .connectionString(connectionString);
    }

    /**
     * 创建Azure Service Bus的DomainEventPublisher实现
     * 标记为Primary，优先使用此实现而非Kafka实现
     *
     * @return AzureServiceBusDomainEventPublisher实例
     */
    @Bean
    @Primary
    public AzureServiceBusDomainEventPublisher azureServiceBusDomainEventPublisher() {
        return new AzureServiceBusDomainEventPublisher();
    }
}