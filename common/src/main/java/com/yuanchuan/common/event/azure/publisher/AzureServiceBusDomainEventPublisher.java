package com.yuanchuan.common.event.azure.publisher;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.azure.messaging.servicebus.ServiceBusProcessorClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.azure.messaging.servicebus.ServiceBusClientBuilder;
import com.azure.messaging.servicebus.ServiceBusMessage;
import com.azure.messaging.servicebus.ServiceBusSenderClient;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yuanchuan.common.event.CommonEvent;
import com.yuanchuan.common.event.DomainEventPublisher;
import com.yuanchuan.common.event.EventTypeResolver;

import lombok.extern.slf4j.Slf4j;

/**
 * Azure Service Bus事件发布器
 * 使用事件类型解析器自动确定事件类型和对应的topic
 */
@Component
@Slf4j
public class AzureServiceBusDomainEventPublisher implements DomainEventPublisher {

    @Autowired
    private List<EventTypeResolver<CommonEvent>> eventTypeResolvers;

    @Autowired
    private ServiceBusClientBuilder serviceBusClientBuilder;

    @Autowired
    private ObjectMapper objectMapper;

    // 缓存消费处理器（Key: Topic+Subscription组合）
    private final Map<String, ServiceBusProcessorClient> processorCache = new ConcurrentHashMap<>();


    /**
     * 发布领域事件
     *
     * @param event 领域事件
     */
    @Override
    public void publish(CommonEvent event) {
        if (event == null) {
            return;
        }

        publishJsonEvent(event);
    }

    /**
     * 发布JSON事件
     *
     * @param event 事件对象
     */
    public <T extends CommonEvent> void publishJsonEvent(T event) {
        if (event == null) {
            return;
        }
        Class<?> eventClass = event.getClass();

        // 查找支持该事件类型的解析器
        for (EventTypeResolver<CommonEvent> resolver : eventTypeResolvers) {
            if (resolver.supports(eventClass)) {
                String topic = resolver.resolveTopic((CommonEvent) event);
                String key = resolver.resolveKey((CommonEvent) event);

                log.info("发布事件到Azure Service Bus: type={}, topic={}, key={}", eventClass.getSimpleName(), topic, key);

                try {
                    // 创建Service Bus发送客户端
                    ServiceBusSenderClient sender = serviceBusClientBuilder
                            .sender()
                            .topicName(topic)
                            .buildClient();

                    // 将对象序列化为JSON字节数组
                    String eventData = serializeToJson(event);

                    // 创建Service Bus消息
                    ServiceBusMessage message = new ServiceBusMessage(eventData);
                    message.setSessionId(event.getEventId());
                    message.setContentType("application/json");
                    message.setMessageId(key);

                    // 发送消息
                    sender.sendMessage(message);
                    sender.close();
                } catch (Exception e) {
                    log.error("发送事件到Azure Service Bus失败: {}", e.getMessage(), e);
                }
                return;
            }
        }

        log.warn("未找到支持事件类型{}的解析器", eventClass.getName());
    }

    /**
     * 序列化对象为JSON字节数组
     *
     * @param object 要序列化的对象
     * @return 序列化后的字节数组
     */
    private String serializeToJson(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            log.error("序列化对象为JSON失败: {}", e.getMessage(), e);
            throw new RuntimeException("序列化对象为JSON失败", e);
        }
    }



    /**
     * 发送消息到指定主题（每次创建新的发送者客户端）
     * @param topicName 主题名称
     * @param message 消息内容
     */
    public void sendToTopic(String topicName, String message) {
        try (ServiceBusSenderClient sender = serviceBusClientBuilder
                .sender()
                .topicName(topicName)
                .buildClient()) {

            sender.sendMessage(new ServiceBusMessage(message));
            System.out.println("Successfully sent message to topic: " + topicName);

        } catch (Exception e) {
            System.err.println("Error sending message to topic " + topicName + ": " + e.getMessage());
            throw new RuntimeException("Failed to send message to Azure Service Bus", e);
        }
    }



}