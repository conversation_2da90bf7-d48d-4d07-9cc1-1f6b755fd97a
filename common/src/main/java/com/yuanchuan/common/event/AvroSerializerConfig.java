// package com.yuanchuan.common.event;
//
// import java.util.HashMap;
// import java.util.Map;
//
// import org.apache.kafka.clients.consumer.ConsumerConfig;
// import org.apache.kafka.clients.producer.ProducerConfig;
// import org.apache.kafka.common.serialization.StringDeserializer;
// import org.apache.kafka.common.serialization.StringSerializer;
// import org.springframework.beans.factory.annotation.Value;
// import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Configuration;
// import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
// import org.springframework.kafka.core.ConsumerFactory;
// import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
// import org.springframework.kafka.core.DefaultKafkaProducerFactory;
// import org.springframework.kafka.core.KafkaTemplate;
// import org.springframework.kafka.core.ProducerFactory;
//
// import io.confluent.kafka.serializers.KafkaAvroDeserializer;
// import io.confluent.kafka.serializers.KafkaAvroSerializer;
//
// /**
//  * Avro序列化器配置类
//  */
// @Configuration
// public class AvroSerializerConfig {
//
//     @Value("${spring.kafka.bootstrap-servers}")
//     private String bootstrapServers;
//
//     // @Value("${spring.kafka.schema-registry-url}")
//     // private String schemaRegistryUrl;
//
//     /**
//      * 配置生产者工厂
//      */
//     @Bean
//     public ProducerFactory<String, Object> producerFactory() {
//         Map<String, Object> configProps = new HashMap<>();
//         configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
//         configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
//         configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, KafkaAvroSerializer.class);
//         //configProps.put("schema.registry.url", schemaRegistryUrl);
//         return new DefaultKafkaProducerFactory<>(configProps);
//     }
//
//     /**
//      * 配置KafkaTemplate
//      */
//     @Bean
//     public KafkaTemplate<String, Object> kafkaTemplate() {
//         return new KafkaTemplate<>(producerFactory());
//     }
//
//     /**
//      * 配置消费者工厂
//      */
//     @Bean
//     public ConsumerFactory<String, Object> consumerFactory() {
//         Map<String, Object> configProps = new HashMap<>();
//         configProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
//         configProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
//         configProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, KafkaAvroDeserializer.class);
//         //configProps.put("schema.registry.url", schemaRegistryUrl);
//         configProps.put("specific.avro.reader", true);
//         return new DefaultKafkaConsumerFactory<>(configProps);
//     }
//
//     /**
//      * 配置Kafka监听器容器工厂
//      */
//     @Bean
//     public ConcurrentKafkaListenerContainerFactory<String, Object> kafkaListenerContainerFactory() {
//         ConcurrentKafkaListenerContainerFactory<String, Object> factory = new ConcurrentKafkaListenerContainerFactory<>();
//         factory.setConsumerFactory(consumerFactory());
//         return factory;
//     }
// }