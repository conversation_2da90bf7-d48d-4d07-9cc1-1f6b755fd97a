// package com.yuanchuan.common.event;
//
// import org.apache.avro.specific.SpecificRecordBase;
//
// import lombok.extern.slf4j.Slf4j;
//
// /**
//  * 抽象Avro事件处理器
//  * 提供通用的事件处理逻辑，减少重复代码
//  *
//  * @param <A> Avro事件类型，必须是SpecificRecordBase的子类
//  * @param <J> Java事件类型
//  */
// @Slf4j
// public abstract class AbstractAvroEventHandler<A extends SpecificRecordBase, J> {
//
//     /**
//      * 处理Avro格式的事件
//      *
//      * @param avroEvent Avro格式的事件
//      */
//     protected void handleEvent(A avroEvent) {
//         String eventType = avroEvent.getClass().getSimpleName();
//         log.info("收到Avro格式的{}事件: {}", eventType, avroEvent);
//
//         try {
//             // 将Avro消息转换为Java对象
//             J javaEvent = convertToJavaEvent(avroEvent);
//
//             // 记录处理信息
//             logEventInfo(javaEvent);
//
//             // 处理事件的业务逻辑
//             processEvent(javaEvent);
//
//         } catch (Exception e) {
//             log.error("处理{}事件失败", eventType, e);
//         }
//     }
//
//     /**
//      * 将Avro事件转换为Java事件
//      *
//      * @param avroEvent Avro格式的事件
//      * @return Java格式的事件
//      */
//     protected J convertToJavaEvent(A avroEvent) {
//         return (J) avroEvent;
//     };
//
//     /**
//      * 记录事件信息
//      *
//      * @param javaEvent Java格式的事件
//      */
//     protected abstract void logEventInfo(J javaEvent);
//
//     /**
//      * 处理事件的业务逻辑
//      *
//      * @param javaEvent Java格式的事件
//      */
//     protected abstract void processEvent(J javaEvent);
// }