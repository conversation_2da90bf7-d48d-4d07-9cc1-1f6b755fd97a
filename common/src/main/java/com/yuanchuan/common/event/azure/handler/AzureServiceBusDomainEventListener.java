package com.yuanchuan.common.event.azure.handler;

import com.azure.messaging.servicebus.ServiceBusClientBuilder;
import com.azure.messaging.servicebus.ServiceBusProcessorClient;
import com.azure.messaging.servicebus.ServiceBusReceivedMessageContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.SmartLifecycle;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Azure Service Bus 事件监听器（企业级优化版）
 * 功能：
 * 1. 动态订阅管理
 * 2. 消息处理生命周期控制
 * 3. 生产级错误处理和监控
 */
@Component
@Slf4j
public class AzureServiceBusDomainEventListener implements SmartLifecycle {

    @Autowired
    private ServiceBusClientBuilder serviceBusClientBuilder;

    // 处理器缓存（线程安全）
    private final Map<String, ServiceBusProcessorClient> processorCache = new ConcurrentHashMap<>();
    private volatile boolean running = false;

    /**
     * 动态添加订阅（企业级实现）
     * @param topicName 主题名称
     * @param subscriptionName 订阅名称
     * @param messageHandler 消息处理器
     * @param maxRetries 最大重试次数（默认3次）
            */
    public void addSubscription(String topicName,
                                String subscriptionName,
                                MessageHandler messageHandler,
                                int maxRetries) {
        String cacheKey = buildCacheKey(topicName, subscriptionName);

        processorCache.computeIfAbsent(cacheKey, k -> {
            log.info("Initializing listener for {}/{}", topicName, subscriptionName);
            return buildProcessorClient(topicName, subscriptionName, messageHandler, maxRetries);
        });
    }

    // 简化版方法（默认重试3次）
    public void addSubscription(String topicName,
                                String subscriptionName,
                                MessageHandler messageHandler) {
        addSubscription(topicName, subscriptionName, messageHandler, 3);
    }

    private ServiceBusProcessorClient buildProcessorClient(String topicName,
                                                           String subscriptionName,
                                                           MessageHandler messageHandler,
                                                           int maxRetries) {
        return serviceBusClientBuilder
                .processor()
                .topicName(topicName)
                .subscriptionName(subscriptionName)
                .prefetchCount(50) // 优化吞吐量
                .maxConcurrentCalls(5) // 并发处理数
                .maxAutoLockRenewDuration(Duration.ofMinutes(5)) // 延长锁定时长
                .processMessage(context -> handleMessage(context, messageHandler, maxRetries))
                .processError(context -> {
                    log.error("[{}/{}] 监听失败 - 错误来源: {}",
                            topicName, subscriptionName, context.getErrorSource(),
                            context.getException());
                })
                .buildProcessorClient();
    }

    private void handleMessage(ServiceBusReceivedMessageContext context,
                               MessageHandler messageHandler,
                               int remainingRetries) {
        try {
            String messageBody = context.getMessage().getBody().toString();
            log.debug("Processing message: {}", messageBody);

            messageHandler.handle(messageBody);
            context.complete();
            log.trace("Message completed successfully");
        } catch (BusinessException e) {
            log.warn("Business validation failed: {}", e.getMessage());
            context.deadLetter(); // 业务异常转入死信
        } catch (Exception e) {
            if (remainingRetries > 0) {
                log.warn("Processing failed ({} retries left), abandoning...", remainingRetries);
                context.abandon(); // 触发重试
            } else {
                log.error("Max retries exceeded, dead-lettering message");
                context.deadLetter(); // 超过重试次数转入死信
            }
        }
    }

    // ========== Spring 生命周期管理 ==========
    @Override
    public void start() {
        log.info("Starting {} listeners", processorCache.size());
        processorCache.values().forEach(ServiceBusProcessorClient::start);
        running = true;
    }

    @Override
    public void stop() {
        log.info("Stopping {} listeners", processorCache.size());
        processorCache.values().forEach(client -> {
            try {
                client.close();
            } catch (Exception e) {
                log.error("Error closing listener", e);
            }
        });
        running = false;
    }

    @Override
    public boolean isRunning() {
        return running;
    }

    @Override
    public int getPhase() {
        return Integer.MAX_VALUE; // 最后启动最先关闭
    }

    // ========== 工具方法 ==========
    private String buildCacheKey(String topic, String subscription) {
        return topic + "|" + subscription;
    }

    /**
    * 消息处理器接口（业务方实现）
    */
    @FunctionalInterface
    public interface MessageHandler {
        void handle(String messageBody) throws Exception;
    }

    /**
    * 业务异常（应转入死信队列）
    */
    public static class BusinessException extends Exception {
        public BusinessException(String message) {
            super(message);
        }
    }
}