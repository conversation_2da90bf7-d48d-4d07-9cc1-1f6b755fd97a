package com.yuanchuan.common.constant.user;

/**
 * 用户登录相关的Redis键常量
 * 集中管理所有与用户登录、注册、二次验证相关的Redis键前缀和格式
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/19 10:44
 */
public class UserLoginRedisKeys {

    // ==================== 基础登录相关 ====================

    /**
     * 用户token信息key前缀
     * 用途：存储用户的认证token信息
     * 完整格式：auth:token:[tokenId]
     * 值：token详细信息JSON
     * 过期时间：根据token类型设定
     */
    public static final String TOKEN_KEY_PREFIX = "auth:token:";

    /**
     * 用户设备Token映射key前缀
     * 用途：建立用户ID、设备ID与token的映射关系
     * 完整格式：auth:user:token:[userId]:[deviceId]
     * 值：tokenId
     * 过期时间：与token同步
     */
    public static final String USER_TOKEN_KEY_PREFIX = "auth:user:token:";

    // ==================== 邮箱绑定手机号相关 ====================

    /**
     * 邮箱绑定手机号安全临时key前缀
     * 用途：邮箱注册时需要绑定手机号的临时状态存储
     * 完整格式：user:email:binding:security:[email]
     * 值：绑定验证信息JSON
     * 过期时间：30分钟
     */
    public static final String EMAIL_BINDING_PHONE_SECURITY_KEY = "user:email:binding:security:";

    // ==================== 二次验证相关 ====================

    /**
     * 新设备验证状态key前缀
     * 用途：存储新设备登录时的二次验证状态
     * 完整格式：user:login:new_device_verification:[userId]:[deviceId]
     * 值：NewDeviceVerificationState JSON
     * 过期时间：15分钟
     */
    public static final String NEW_DEVICE_VERIFICATION_PREFIX = "user:login:new_device_verification:";

    /**
     * 设备登录缓存key前缀
     * 用途：缓存用户设备的登录状态，用于快速判断是否为新设备
     * 完整格式：user:login:device_cache:[accountId]:[deviceId]
     * 值：1（存在即表示已知设备）
     * 过期时间：7天
     */
    public static final String DEVICE_LOGIN_CACHE_PREFIX = "user:login:device_cache:";

    /**
     * 安全通知发送记录key前缀
     * 用途：记录安全通知的发送状态，避免重复发送
     * 完整格式：user:login:security_notification:[userId]:[deviceId]
     * 值：发送时间戳
     * 过期时间：24小时
     */
    public static final String SECURITY_NOTIFICATION_PREFIX = "user:login:security_notification:";

    // ==================== 时间常量 ====================

    /**
     * 令牌过期时间（分钟）
     */
    public static final int TOKEN_EXPIRE_MINUTES = 30;

    /**
     * 新设备验证状态过期时间（分钟）
     */
    public static final int NEW_DEVICE_VERIFICATION_EXPIRE_MINUTES = 15;

    /**
     * 设备登录缓存过期时间（天）
     */
    public static final int DEVICE_LOGIN_CACHE_EXPIRE_DAYS = 7;

    /**
     * 安全通知记录过期时间（小时）
     */
    public static final int SECURITY_NOTIFICATION_EXPIRE_HOURS = 24;

    // ==================== 工具方法 ====================

    /**
     * 生成新设备验证状态的完整键
     *
     * @param userId   用户ID
     * @param deviceId 设备ID
     * @return 完整的Redis键
     */
    public static String getNewDeviceVerificationKey(Long userId, String deviceId) {
        return NEW_DEVICE_VERIFICATION_PREFIX + userId + ":" + deviceId;
    }

    /**
     * 生成设备登录缓存的完整键
     *
     * @param accountId 账户ID
     * @param deviceId  设备ID
     * @return 完整的Redis键
     */
    public static String getDeviceLoginCacheKey(Long accountId, String deviceId) {
        return DEVICE_LOGIN_CACHE_PREFIX + accountId + ":" + deviceId;
    }

    /**
     * 生成用户设备Token映射的完整键
     *
     * @param userId   用户ID
     * @param deviceId 设备ID
     * @return 完整的Redis键
     */
    public static String getUserTokenKey(Long userId, String deviceId) {
        return USER_TOKEN_KEY_PREFIX + userId + ":" + deviceId;
    }

    /**
     * 生成安全通知记录的完整键
     *
     * @param userId   用户ID
     * @param deviceId 设备ID
     * @return 完整的Redis键
     */
    public static String getSecurityNotificationKey(Long userId, String deviceId) {
        return SECURITY_NOTIFICATION_PREFIX + userId + ":" + deviceId;
    }

    /**
     * 生成邮箱绑定手机号安全临时key的完整键
     *
     * @param email 邮箱地址
     * @return 完整的Redis键
     */
    public static String getEmailBindingPhoneSecurityKey(String email) {
        return EMAIL_BINDING_PHONE_SECURITY_KEY + email;
    }

    /**
     * 生成token信息的完整键
     *
     * @param tokenId token ID
     * @return 完整的Redis键
     */
    public static String getTokenKey(String tokenId) {
        return TOKEN_KEY_PREFIX + tokenId;
    }
}
