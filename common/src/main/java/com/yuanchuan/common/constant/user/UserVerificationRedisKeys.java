package com.yuanchuan.common.constant.user;

/**
 * 用户验证码相关的Redis键常量
 * 集中管理所有与用户验证码相关的Redis键前缀和格式
 */
public class UserVerificationRedisKeys {

    /**
     * 短信验证码存储前缀
     * 用途：存储发送给用户手机的验证码
     * 完整格式：user:sms:code:[业务类型]:[phone]
     * 值：6位数字验证码
     * 过期时间：5分钟
     */
    public static final String SMS_CODE_PREFIX = "user:sms:code:";

    /**
     * 邮箱验证码存储前缀
     * 用途：存储发送给用户邮箱的验证码
     * 完整格式：user:email:code:[业务类型]:[email]
     * 值：6位数字验证码
     * 过期时间：5分钟
     */
    public static final String EMAIL_CODE_PREFIX = "user:email:code:";

    /**
     * 短信发送频率限制前缀
     * 用途：限制同一手机号短时间内重复发送验证码
     * 完整格式：user:sms:limit:[phone]
     * 值：1（存在即表示在限制期内）
     * 过期时间：60秒
     */
    public static final String SMS_LIMIT_PREFIX = "user:sms:limit:";

    /**
     * 邮箱发送频率限制前缀
     * 用途：限制同一邮箱短时间内重复发送验证码
     * 完整格式：user:email:limit:[email]
     * 值：1（存在即表示在限制期内）
     * 过期时间：60秒
     */
    public static final String EMAIL_LIMIT_PREFIX = "user:email:limit:";

    /**
     * IP访问频率限制前缀
     * 用途：限制同一IP地址短时间内的访问频率
     * 完整格式：user:ip:limit:[ip]:[minute]
     * 值：访问计数
     * 过期时间：60秒
     */
    public static final String IP_LIMIT_PREFIX = "user:ip:limit:";

    /**
     * 每日计数器前缀
     * 用途：记录用户每天发送验证码的次数
     * 完整格式：user:daily:counter:[type]:[phone/email]
     * 值：发送计数
     * 过期时间：当天结束
     */
    public static final String DAILY_COUNTER_PREFIX = "user:daily:counter:";

    /**
     * 生成短信验证码的完整键
     * @param businessType 业务类型
     * @param phone 手机号
     * @return 完整的Redis键
     */
    public static String getSmsCodeKey(String businessType, String phone) {
        return SMS_CODE_PREFIX + businessType + ":" + phone;
    }

    /**
     * 生成邮箱验证码的完整键
     * @param businessType 业务类型
     * @param email 邮箱
     * @return 完整的Redis键
     */
    public static String getEmailCodeKey(String businessType, String email) {
        return EMAIL_CODE_PREFIX + businessType + ":" + email;
    }

    /**
     * 生成短信频率限制的完整键
     * @param phone 手机号
     * @return 完整的Redis键
     */
    public static String getSmsLimitKey(String phone) {
        return SMS_LIMIT_PREFIX + phone;
    }

    /**
     * 生成邮箱频率限制的完整键
     * @param email 邮箱
     * @return 完整的Redis键
     */
    public static String getEmailLimitKey(String email) {
        return EMAIL_LIMIT_PREFIX + email;
    }

    /**
     * 生成IP频率限制的完整键
     * @param ip IP地址
     * @param minute 分钟时间戳
     * @return 完整的Redis键
     */
    public static String getIpLimitKey(String ip, long minute) {
        return IP_LIMIT_PREFIX + ip + ":" + minute;
    }

    /**
     * 生成短信每日计数器的完整键
     * @param phone 手机号
     * @return 完整的Redis键
     */
    public static String getSmsDailyCounterKey(String phone) {
        return DAILY_COUNTER_PREFIX + "sms:" + phone;
    }

    /**
     * 生成邮箱每日计数器的完整键
     * @param email 邮箱
     * @return 完整的Redis键
     */
    public static String getEmailDailyCounterKey(String email) {
        return DAILY_COUNTER_PREFIX + "email:" + email;
    }
}
