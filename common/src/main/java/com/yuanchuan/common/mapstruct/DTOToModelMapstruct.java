package com.yuanchuan.common.mapstruct;

import cn.hutool.core.collection.CollUtil;
import org.mapstruct.InheritConfiguration;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


public interface DTOToModelMapstruct<D, M> {

    /**
     * 将DTO转换成Model
     */
    @InheritConfiguration
    M dtoToModel(D dto);

    /**
     * 将DTO列表转换为Model列表
     */
    default List<M> dtoToModel(List<D> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return Collections.emptyList();
        }
        return dtoList.stream()
                .map(this::dtoToModel)
                .collect(Collectors.toList());
    }

}