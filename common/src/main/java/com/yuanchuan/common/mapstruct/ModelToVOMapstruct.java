package com.yuanchuan.common.mapstruct;

import cn.hutool.core.collection.CollUtil;
import org.mapstruct.InheritConfiguration;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


public interface ModelToVOMapstruct<M, V> {

    /**
     * 将Entity转换成VO
     */
    @InheritConfiguration
    V modelToVO(M model);

    /**
     * 将Entity列表转换成VO列表
     */
    default List<V> modelToVO(List<M> entityList) {
        if (CollUtil.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        return entityList.stream()
                .map(this::modelToVO)
                .collect(Collectors.toList());
    }

}