package com.yuanchuan.common.mapstruct;

import cn.hutool.core.collection.CollUtil;
import org.mapstruct.InheritConfiguration;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 将Model转换成PO的通用接口
 *
 * @param <M> Model实体类泛型
 * @param <P> PO实体类泛型（数据库实体类）
 */
public interface POToModelMapstruct<P, M> {

    /**
     * 将Model转换成PO
     */
    @InheritConfiguration
    M POToModel(P po);

    /**
     * 将DTO列表转换为Entity列表
     */
    default List<M> POToModel(List<P> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return Collections.emptyList();
        }
        return dtoList.stream()
                .map(this::POToModel)
                .collect(Collectors.toList());
    }

}