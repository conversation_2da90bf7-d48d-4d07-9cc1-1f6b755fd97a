package com.yuanchuan.common.utils;

import java.text.Normalizer;
import java.util.regex.Pattern;

public class TextSanitizer {

    // 匹配 emoji 表情（Unicode 代理区）
    private static final Pattern EMOJI_PATTERN = Pattern.compile("[\uD83C-\uDBFF\uDC00-\uDFFF]+");

    // 去除 HTML 标签
    private static final Pattern HTML_TAG_PATTERN = Pattern.compile("<[^>]+>");

    // 保留中英文、数字、常见标点，其它替换为空
    private static final Pattern SPECIAL_CHAR_PATTERN = Pattern.compile("[^\\p{L}\\p{N}\\p{P}\\p{Z}]");

    // 去除所有空白字符（空格、制表符、换行等）
    private static final Pattern WHITESPACE_PATTERN = Pattern.compile("\\s+");

    // 去除标点符号
    private static final Pattern PUNCTUATION_PATTERN = Pattern.compile("[\\p{P}]");

    public static String sanitize(String input) {
        if (input == null) {
            return null;
        }

        String result = input;

        // 去除 HTML 标签
        result = HTML_TAG_PATTERN.matcher(result).replaceAll("");

        // 去除 Emoji
        result = EMOJI_PATTERN.matcher(result).replaceAll("");

        // 规范化 Unicode（去除合成字符、附加符号）
        result = Normalizer.normalize(result, Normalizer.Form.NFKC);

        // 去除特殊字符（非中英文、数字、常见标点）
        result = SPECIAL_CHAR_PATTERN.matcher(result).replaceAll("");

        // 去除标点符号
        result = PUNCTUATION_PATTERN.matcher(result).replaceAll("");

        // 去除多余空白字符
        result = WHITESPACE_PATTERN.matcher(result).replaceAll(" ").trim();

        return result;
    }

    public static void main(String[] args) {
        String test = "Hello 😊\t <b>世界</b>　!!! \n👨‍👩‍👧‍👦 \uD83D\uDC4D";
        System.out.println("原文: " + test);
        System.out.println("清洗后: " + sanitize(test));
    }
}
