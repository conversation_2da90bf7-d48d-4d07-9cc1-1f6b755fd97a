package com.yuanchuan.common.utils;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 地标别名映射工具类（支持热更新、配置文件、可插拔）
 */
public class LandmarkAliasExpander {

    // volatile 保证线程安全可见性
    private static volatile Map<String, String> aliasMap = new ConcurrentHashMap<>();

    // 默认词典路径，可通过配置替换
    private static final String DEFAULT_DICT_PATH = "landmark_aliases.json";

    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        loadFromClasspath(DEFAULT_DICT_PATH);
    }

    /**
     * 从类路径中加载词典（JSON 格式）
     */
    public static void loadFromClasspath(String path) {
        try {
            String json = ResourceUtil.readUtf8Str(path);
            Map<String, String> parsed = objectMapper.readValue(json,
                    new TypeReference<Map<String, String>>() {});
            aliasMap = new ConcurrentHashMap<>(parsed);
        } catch (Exception e) {
            System.err.println("【警告】加载地标别名词典失败：" + e.getMessage());
        }
    }

    /**
     * 从外部 Map 重新加载词典（例如 Redis、数据库）
     */
    public static void reloadFromExternal(Map<String, String> externalMap) {
        if (externalMap != null && !externalMap.isEmpty()) {
            aliasMap = new ConcurrentHashMap<>(externalMap);
        }
    }

    /**
     * 获取当前所有别名映射（只读）
     */
    public static Map<String, String> getCurrentAliasMap() {
        return Collections.unmodifiableMap(aliasMap);
    }

    /**
     * 获取标准地标名称，如果没有命中则返回原词
     */
    public static String expand(String input) {
        if (StrUtil.isBlank(input)) return input;
        return aliasMap.getOrDefault(input.trim(), input.trim());
    }

    /**
     * 判断是否是已知别名
     */
    public static boolean isAlias(String input) {
        if (StrUtil.isBlank(input)) return false;
        return aliasMap.containsKey(input.trim());
    }

    public static void main(String[] args) {
        String rawInput = "北車";
        String expanded = LandmarkAliasExpander.expand(rawInput);
        System.out.println(expanded);
    }
}

