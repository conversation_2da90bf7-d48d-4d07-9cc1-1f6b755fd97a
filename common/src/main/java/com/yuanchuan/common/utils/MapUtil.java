package com.yuanchuan.common.utils;

public class MapUtil {

    private static final double EARTH_RADIUS = 6371; // 地球半径（km）

    public static int haversineDistance(double lat1, double lon1, double lat2, double lon2) {
        // 计算原始距离（单位：km）
        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        double distanceKm = EARTH_RADIUS * c;

        // 转换为米，并确保最小10米（四舍五入到整数）
        double distanceM = distanceKm * 1000;
        return Math.toIntExact(Math.max(10, Math.round(distanceM))); // 确保最小10米
    }

    public static void main(String[] args) {
        // 示例：北京天安门 (39.9087, 116.3975) 到北京西站 (39.8949, 116.3224)
        double distance = haversineDistance(39.9087, 116.3975, 39.8949, 116.3224);
        System.out.println("距离: " + distance + " m"); // 约 7.5 km
    }

}
