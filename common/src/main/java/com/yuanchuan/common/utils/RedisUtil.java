package com.yuanchuan.common.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Redis工具类，封装StringRedisTemplate
 * 使用StringRedisTemplate避免序列化问题
 */
@Component
public class RedisUtil {

    private final StringRedisTemplate stringRedisTemplate;

    @Autowired
    public RedisUtil(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    // ============================== String类型操作 ==============================

    /**
     * 设置指定key的值
     *
     * @param key   键
     * @param value 值
     */
    public void set(String key, String value) {
        stringRedisTemplate.opsForValue().set(key, value);
    }

    /**
     * 设置指定key的值，并设置过期时间
     *
     * @param key   键
     * @param value 值
     * @param time  过期时间
     * @param unit  时间单位
     */
    public void set(String key, String value, long time, TimeUnit unit) {
        stringRedisTemplate.opsForValue().set(key, value, time, unit);
    }

    /**
     * 获取指定key的值
     *
     * @param key 键
     * @return 值
     */
    public String get(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }

    /**
     * 删除指定key
     *
     * @param key 键
     * @return 是否成功
     */
    public Boolean delete(String key) {
        return stringRedisTemplate.delete(key);
    }

    /**
     * 批量删除key
     *
     * @param keys 键集合
     * @return 删除的数量
     */
    public Long delete(Collection<String> keys) {
        return stringRedisTemplate.delete(keys);
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return 是否存在
     */
    public Boolean hasKey(String key) {
        return stringRedisTemplate.hasKey(key);
    }

    /**
     * 设置过期时间
     *
     * @param key  键
     * @param time 时间
     * @param unit 时间单位
     * @return 是否成功
     */
    public Boolean expire(String key, long time, TimeUnit unit) {
        return stringRedisTemplate.expire(key, time, unit);
    }

    /**
     * 获取过期时间
     *
     * @param key 键
     * @return 过期时间（秒）
     */
    public Long getExpire(String key) {
        return stringRedisTemplate.getExpire(key);
    }

    /**
     * 获取指定key的过期时间（指定时间单位）
     *
     * @param key  键
     * @param unit 时间单位
     * @return 过期时间
     */
    public Long getExpire(String key, TimeUnit unit) {
        return stringRedisTemplate.getExpire(key, unit);
    }

    /**
     * 递增操作
     *
     * @param key 键
     * @return 递增后的值
     */
    public Long increment(String key) {
        return stringRedisTemplate.opsForValue().increment(key);
    }

    /**
     * 递增指定数量
     *
     * @param key   键
     * @param delta 递增量
     * @return 递增后的值
     */
    public Long increment(String key, long delta) {
        return stringRedisTemplate.opsForValue().increment(key, delta);
    }

    /**
     * 计算当天剩余秒数
     *
     * @return 当天剩余的秒数
     */
    public long getRemainSecondsInDay() {
        long currentTimeMillis = System.currentTimeMillis();
        long todayEndMillis = currentTimeMillis - (currentTimeMillis % (24 * 60 * 60 * 1000)) + (24 * 60 * 60 * 1000);
        return (todayEndMillis - currentTimeMillis) / 1000;
    }

    // ============================== Hash类型操作 ==============================

    /**
     * 向Hash表中放入数据
     *
     * @param key     键
     * @param hashKey 项
     * @param value   值
     */
    public void hSet(String key, String hashKey, String value) {
        stringRedisTemplate.opsForHash().put(key, hashKey, value);
    }

    /**
     * 获取Hash表中的数据
     *
     * @param key     键
     * @param hashKey 项
     * @return 值
     */
    public Object hGet(String key, String hashKey) {
        return stringRedisTemplate.opsForHash().get(key, hashKey);
    }

    /**
     * 获取Hash表中的所有数据
     *
     * @param key 键
     * @return 对应的多个键值
     */
    public Map<Object, Object> hGetAll(String key) {
        return stringRedisTemplate.opsForHash().entries(key);
    }

    /**
     * 批量设置Hash的值
     *
     * @param key 键
     * @param map 对应多个键值
     */
    public void hSetAll(String key, Map<String, String> map) {
        stringRedisTemplate.opsForHash().putAll(key, map);
    }

    /**
     * 删除Hash表中的值
     *
     * @param key      键
     * @param hashKeys 项集合
     * @return 删除的数量
     */
    public Long hDelete(String key, Object... hashKeys) {
        return stringRedisTemplate.opsForHash().delete(key, hashKeys);
    }

    /**
     * 判断Hash表中是否有该项的值
     *
     * @param key     键
     * @param hashKey 项
     * @return 是否存在
     */
    public Boolean hHasKey(String key, String hashKey) {
        return stringRedisTemplate.opsForHash().hasKey(key, hashKey);
    }

    // ============================== List类型操作 ==============================

    /**
     * 将数据放入List缓存
     *
     * @param key   键
     * @param value 值
     * @return 插入后的长度
     */
    public Long lPush(String key, String value) {
        return stringRedisTemplate.opsForList().rightPush(key, value);
    }

    /**
     * 将多个数据放入List缓存
     *
     * @param key    键
     * @param values 值
     * @return 插入后的长度
     */
    public Long lPushAll(String key, Collection<String> values) {
        return stringRedisTemplate.opsForList().rightPushAll(key, values);
    }

    /**
     * 获取List缓存的内容
     *
     * @param key   键
     * @param start 开始
     * @param end   结束（-1代表所有值）
     * @return 列表
     */
    public List<String> lRange(String key, long start, long end) {
        return stringRedisTemplate.opsForList().range(key, start, end);
    }

    /**
     * 获取List缓存的长度
     *
     * @param key 键
     * @return 长度
     */
    public Long lSize(String key) {
        return stringRedisTemplate.opsForList().size(key);
    }

    /**
     * 通过索引获取List中的值
     *
     * @param key   键
     * @param index 索引（index>=0时，0表头，1第二个元素，依次类推；index<0时，-1表尾，-2倒数第二个元素，依次类推）
     * @return 值
     */
    public String lIndex(String key, long index) {
        return stringRedisTemplate.opsForList().index(key, index);
    }

    /**
     * 移除List中的元素
     *
     * @param key   键
     * @param count 移除多少个（count > 0：从头部开始移除count个；count < 0：从尾部开始移除count个；count = 0：移除所有）
     * @param value 值
     * @return 移除的个数
     */
    public Long lRemove(String key, long count, String value) {
        return stringRedisTemplate.opsForList().remove(key, count, value);
    }

    // ============================== Set类型操作 ==============================

    /**
     * 将数据放入Set缓存
     *
     * @param key    键
     * @param values 值
     * @return 成功个数
     */
    public Long sAdd(String key, String... values) {
        return stringRedisTemplate.opsForSet().add(key, values);
    }

    /**
     * 获取Set缓存的所有值
     *
     * @param key 键
     * @return 值集合
     */
    public Set<String> sMembers(String key) {
        return stringRedisTemplate.opsForSet().members(key);
    }

    /**
     * 判断Set是否包含value
     *
     * @param key   键
     * @param value 值
     * @return 是否存在
     */
    public Boolean sIsMember(String key, String value) {
        return stringRedisTemplate.opsForSet().isMember(key, value);
    }

    /**
     * 获取Set的长度
     *
     * @param key 键
     * @return 长度
     */
    public Long sSize(String key) {
        return stringRedisTemplate.opsForSet().size(key);
    }

    /**
     * 移除Set中的值
     *
     * @param key    键
     * @param values 值
     * @return 移除的个数
     */
    public Long sRemove(String key, Object... values) {
        return stringRedisTemplate.opsForSet().remove(key, values);
    }

    // ============================== ZSet类型操作 ==============================

    /**
     * 将数据放入ZSet缓存
     *
     * @param key   键
     * @param value 值
     * @param score 分数
     * @return 是否成功
     */
    public Boolean zAdd(String key, String value, double score) {
        return stringRedisTemplate.opsForZSet().add(key, value, score);
    }

    /**
     * 获取ZSet指定范围内的值
     *
     * @param key   键
     * @param start 开始
     * @param end   结束
     * @return 值集合
     */
    public Set<String> zRange(String key, long start, long end) {
        return stringRedisTemplate.opsForZSet().range(key, start, end);
    }

    /**
     * 获取ZSet中value的score值
     *
     * @param key   键
     * @param value 值
     * @return score值
     */
    public Double zScore(String key, String value) {
        return stringRedisTemplate.opsForZSet().score(key, value);
    }

    /**
     * 移除ZSet中的值
     *
     * @param key    键
     * @param values 值
     * @return 移除的个数
     */
    public Long zRemove(String key, Object... values) {
        return stringRedisTemplate.opsForZSet().remove(key, values);
    }

    // ============================== 其他操作 ==============================

    /**
     * 获取所有符合pattern的key
     *
     * @param pattern 模式
     * @return key集合
     */
    public Set<String> keys(String pattern) {
        return stringRedisTemplate.keys(pattern);
    }


    /**
     * 执行 Lua 脚本
     *
     * @param script 脚本内容
     * @param keys   键列表
     * @param args   参数列表
     * @return 脚本执行结果
     */
    public Long executeLua(String script, List<String> keys, String... args) {
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>(script, Long.class);
        return stringRedisTemplate.execute(redisScript, keys, args);
    }


    /**
     * 获取有序集合中指定范围的成员（按分数降序）
     *
     * @param key   Redis key
     * @param start 起始索引（0 表示第一个）
     * @param end   结束索引（-1 表示最后一个）
     * @return 成员列表（按分数从高到低排序）
    */
    public Set<String> zRevRange(String key, long start, long end) {
        Set<String> result = stringRedisTemplate.opsForZSet().reverseRange(key, start, end);
        return result != null ? result : Collections.emptySet();
    }

    /**
     * 获取有序集合中指定范围的成员（带分数）
     *
     * @param key   Redis key
     * @param start 起始索引
     * @param end   结束索引
     * @return 成员及分数（按分数降序）
    */
    public Set<ZSetOperations.TypedTuple<String>> zRevRangeWithScores(String key, long start, long end) {
        Set<ZSetOperations.TypedTuple<String>> result = stringRedisTemplate.opsForZSet()
                .reverseRangeWithScores(key, start, end);
        return result != null ? result : Collections.emptySet();
    }
}
