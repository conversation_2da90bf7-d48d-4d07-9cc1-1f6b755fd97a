package com.yuanchuan.common.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuanchuan.common.response.PageResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分页转换工具类
 * <p>
 * 提供分页对象之间的转换功能，包含以下方法：
 * 1. 将 MyBatis-Plus 的 Page 转换为自定义的 PageResult
 * 2. 分页对象之间的类型转换
 * </p>
*/
public final class PageUtils {
    private static final Logger logger = LoggerFactory.getLogger(PageUtils.class);

    private PageUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * 将 MyBatis-Plus 的 Page 转换为自定义的 PageResult
     *
     * @param page      源分页对象，不能为 null
     * @param converter 元素转换函数，不能为 null
     * @param <E>       源元素类型
     * @param <V>       目标元素类型
     * @return 转换后的 PageResult 对象
     * @throws IllegalArgumentException 如果 page 或 converter 为 null
     * @throws ArithmeticException      如果分页参数超出 int 范围
     */
    public static <E, V> PageResult<V> convertPageToPageResult(Page<E> page, Function<E, V> converter) {
        Objects.requireNonNull(page, "Page must not be null");
        Objects.requireNonNull(converter, "Converter must not be null");

        List<E> records = page.getRecords();
        List<V> convertedRecords = records == null ? Collections.emptyList() :
                records.stream()
                        .map(converter)
                        .collect(Collectors.toList());

        try {
            return PageResult.<V>builder()
                    .records(convertedRecords)
                    .total(page.getTotal())
                    .pageNum(Math.toIntExact(page.getCurrent()))
                    .pageSize(Math.toIntExact(page.getSize()))
                    .build();
        } catch (ArithmeticException e) {
            logger.error("Page parameter overflow: current={}, size={}", page.getCurrent(), page.getSize(), e);
            throw new IllegalArgumentException("Page parameter exceeds integer range", e);
        }
    }

    /**
     * 通用分页对象转换
     *
     * @param sourcePage 源分页对象，不能为 null
     * @param converter  列表转换函数，不能为 null
     * @param <T>        源元素类型
     * @param <R>        目标元素类型
     * @return 转换后的分页对象
     * @throws IllegalArgumentException 如果 sourcePage 或 converter 为 null
    */
    public static <T, R> Page<R> convertPage(
            IPage<T> sourcePage,
            Function<List<T>, List<R>> converter
    ) {
        Objects.requireNonNull(sourcePage, "Source page must not be null");
        Objects.requireNonNull(converter, "Converter must not be null");

        Page<R> targetPage = new Page<>();
        targetPage.setCurrent(sourcePage.getCurrent());
        targetPage.setSize(sourcePage.getSize());
        targetPage.setTotal(sourcePage.getTotal());

        List<T> records = sourcePage.getRecords();
        targetPage.setRecords(records == null ? Collections.emptyList() : converter.apply(records));

        return targetPage;
    }
}