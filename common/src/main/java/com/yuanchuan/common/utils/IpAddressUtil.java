package com.yuanchuan.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.regex.Pattern;

/**
 * IP地址工具类
 * 提供IP地址验证、比较和分析功能
 *
 * <AUTHOR>
 * @date 2025/1/20
 * @version 1.0
 */
@Slf4j
public class IpAddressUtil {

    /**
     * IPv4地址正则表达式
     */
    private static final String IPV4_REGEX = 
        "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
    
    /**
     * IPv6地址正则表达式（简化版）
     */
    private static final String IPV6_REGEX = 
        "^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$";
    
    /**
     * 编译后的IPv4正则表达式模式
     */
    private static final Pattern IPV4_PATTERN = Pattern.compile(IPV4_REGEX);
    
    /**
     * 编译后的IPv6正则表达式模式
     */
    private static final Pattern IPV6_PATTERN = Pattern.compile(IPV6_REGEX);

    /**
     * 私有网络IP地址范围
     */
    private static final String[] PRIVATE_IP_RANGES = {
        "10.0.0.0/8",       // 10.0.0.0 - **************
        "**********/12",    // ********** - **************
        "***********/16",   // *********** - ***************
        "*********/8"       // ********* - *************** (localhost)
    };

    /**
     * 验证IP地址格式是否正确
     *
     * @param ipAddress IP地址字符串
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidIpAddress(String ipAddress) {
        if (StringUtils.isBlank(ipAddress)) {
            return false;
        }
        
        // 检查IPv4格式
        if (IPV4_PATTERN.matcher(ipAddress).matches()) {
            return true;
        }
        
        // 检查IPv6格式
        if (IPV6_PATTERN.matcher(ipAddress).matches()) {
            return true;
        }
        
        // 使用Java内置方法进行更严格的验证
        try {
            InetAddress.getByName(ipAddress);
            return true;
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 检查两个IP地址是否相同
     *
     * @param ip1 第一个IP地址
     * @param ip2 第二个IP地址
     * @return true表示相同，false表示不同
     */
    public static boolean isSameIpAddress(String ip1, String ip2) {
        if (StringUtils.isBlank(ip1) || StringUtils.isBlank(ip2)) {
            return false;
        }
        
        // 直接字符串比较
        if (ip1.equals(ip2)) {
            return true;
        }
        
        // 标准化后比较（处理IPv6的不同表示形式）
        try {
            InetAddress addr1 = InetAddress.getByName(ip1);
            InetAddress addr2 = InetAddress.getByName(ip2);
            return addr1.equals(addr2);
        } catch (UnknownHostException e) {
            log.warn("IP地址解析失败: ip1={}, ip2={}", ip1, ip2, e);
            return false;
        }
    }

    /**
     * 检查两个IP地址是否在同一网段
     *
     * @param ip1 第一个IP地址
     * @param ip2 第二个IP地址
     * @param subnetMask 子网掩码（如24表示/24）
     * @return true表示在同一网段，false表示不在同一网段
     */
    public static boolean isSameSubnet(String ip1, String ip2, int subnetMask) {
        if (!isValidIpAddress(ip1) || !isValidIpAddress(ip2)) {
            return false;
        }
        
        try {
            InetAddress addr1 = InetAddress.getByName(ip1);
            InetAddress addr2 = InetAddress.getByName(ip2);
            
            // 只处理IPv4地址
            if (addr1.getAddress().length == 4 && addr2.getAddress().length == 4) {
                return isSameIPv4Subnet(addr1.getAddress(), addr2.getAddress(), subnetMask);
            }
            
            return false;
        } catch (UnknownHostException e) {
            log.warn("IP地址解析失败: ip1={}, ip2={}", ip1, ip2, e);
            return false;
        }
    }

    /**
     * 检查IP地址是否为私有网络地址
     *
     * @param ipAddress IP地址
     * @return true表示是私有网络地址，false表示是公网地址
     */
    public static boolean isPrivateIpAddress(String ipAddress) {
        if (!isValidIpAddress(ipAddress)) {
            return false;
        }
        
        try {
            InetAddress addr = InetAddress.getByName(ipAddress);
            return addr.isSiteLocalAddress() || addr.isLoopbackAddress();
        } catch (UnknownHostException e) {
            log.warn("IP地址解析失败: ipAddress={}", ipAddress, e);
            return false;
        }
    }

    /**
     * 获取IP地址的地理位置信息（简化版，实际项目中可以集成第三方服务）
     *
     * @param ipAddress IP地址
     * @return 地理位置信息
     */
    public static String getIpLocation(String ipAddress) {
        if (!isValidIpAddress(ipAddress)) {
            return "未知";
        }
        
        // 检查是否为私有网络地址
        if (isPrivateIpAddress(ipAddress)) {
            return "内网";
        }
        
        // 这里可以集成第三方IP地理位置服务
        // 如：高德地图、百度地图、IP2Location等
        // 目前返回简化信息
        return "外网";
    }

    /**
     * 计算IP地址变化的风险等级
     *
     * @param originalIp 原始IP地址
     * @param currentIp 当前IP地址
     * @return 风险等级：LOW(低)、MEDIUM(中)、HIGH(高)
     */
    public static IpChangeRiskLevel calculateIpChangeRisk(String originalIp, String currentIp) {
        // 如果IP地址相同，无风险
        if (isSameIpAddress(originalIp, currentIp)) {
            return IpChangeRiskLevel.NONE;
        }
        
        // 如果都是私有网络地址，风险较低
        if (isPrivateIpAddress(originalIp) && isPrivateIpAddress(currentIp)) {
            return IpChangeRiskLevel.LOW;
        }
        
        // 如果在同一网段（/24），风险中等
        if (isSameSubnet(originalIp, currentIp, 24)) {
            return IpChangeRiskLevel.MEDIUM;
        }
        
        // 如果在同一网段（/16），风险中等
        if (isSameSubnet(originalIp, currentIp, 16)) {
            return IpChangeRiskLevel.MEDIUM;
        }
        
        // 其他情况风险较高
        return IpChangeRiskLevel.HIGH;
    }

    /**
     * 脱敏IP地址（用于日志记录）
     *
     * @param ipAddress IP地址
     * @return 脱敏后的IP地址
     */
    public static String maskIpAddress(String ipAddress) {
        if (StringUtils.isBlank(ipAddress)) {
            return ipAddress;
        }
        
        // IPv4地址脱敏：************* -> 192.168.*.***
        if (IPV4_PATTERN.matcher(ipAddress).matches()) {
            String[] parts = ipAddress.split("\\.");
            if (parts.length == 4) {
                return parts[0] + "." + parts[1] + ".*.**";
            }
        }
        
        // IPv6地址脱敏：只显示前两段
        if (ipAddress.contains(":")) {
            String[] parts = ipAddress.split(":");
            if (parts.length >= 2) {
                return parts[0] + ":" + parts[1] + ":****";
            }
        }
        
        return "****";
    }

    /**
     * 检查IPv4地址是否在同一子网
     */
    private static boolean isSameIPv4Subnet(byte[] ip1, byte[] ip2, int subnetMask) {
        if (ip1.length != 4 || ip2.length != 4 || subnetMask < 0 || subnetMask > 32) {
            return false;
        }
        
        int mask = 0xFFFFFFFF << (32 - subnetMask);
        
        int addr1 = ((ip1[0] & 0xFF) << 24) | ((ip1[1] & 0xFF) << 16) | ((ip1[2] & 0xFF) << 8) | (ip1[3] & 0xFF);
        int addr2 = ((ip2[0] & 0xFF) << 24) | ((ip2[1] & 0xFF) << 16) | ((ip2[2] & 0xFF) << 8) | (ip2[3] & 0xFF);
        
        return (addr1 & mask) == (addr2 & mask);
    }

    /**
     * IP地址变化风险等级枚举
     */
    public enum IpChangeRiskLevel {
        /**
         * 无风险（IP地址相同）
         */
        NONE("NONE", "无风险"),
        
        /**
         * 低风险（同一内网）
         */
        LOW("LOW", "低风险"),
        
        /**
         * 中等风险（同一网段）
         */
        MEDIUM("MEDIUM", "中等风险"),
        
        /**
         * 高风险（不同网段）
         */
        HIGH("HIGH", "高风险");
        
        private final String code;
        private final String description;
        
        IpChangeRiskLevel(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
