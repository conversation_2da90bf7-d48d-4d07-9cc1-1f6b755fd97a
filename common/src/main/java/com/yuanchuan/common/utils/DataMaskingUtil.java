package com.yuanchuan.common.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * 数据脱敏工具类
 * 用于对敏感信息进行脱敏处理
 */
public class DataMaskingUtil {

    /**
     * 手机号脱敏
     * 显示前3位和后4位，中间用****代替
     * 例如：13812345678 -> 138****5678
     *
     * @param phone 手机号
     * @return 脱敏后的手机号
     */
    public static String maskPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            return phone;
        }
        
        // 去除空格和特殊字符
        String cleanPhone = phone.replaceAll("[^0-9]", "");
        
        if (cleanPhone.length() < 7) {
            return phone; // 长度不够，不进行脱敏
        }
        
        if (cleanPhone.length() == 11) {
            // 标准11位手机号：138****5678
            return cleanPhone.substring(0, 3) + "****" + cleanPhone.substring(7);
        } else if (cleanPhone.length() >= 7) {
            // 其他长度：前3位 + **** + 后4位
            int endIndex = Math.max(cleanPhone.length() - 4, 3);
            return cleanPhone.substring(0, 3) + "****" + cleanPhone.substring(endIndex);
        }
        
        return phone;
    }

    /**
     * 邮箱脱敏
     * 显示@前的前2-3个字符和@后的所有字符，中间用***代替
     * 例如：<EMAIL> -> ex***@gmail.com
     *
     * @param email 邮箱
     * @return 脱敏后的邮箱
     */
    public static String maskEmail(String email) {
        if (StringUtils.isBlank(email)) {
            return email;
        }
        
        int atIndex = email.indexOf('@');
        if (atIndex <= 0) {
            return email; // 不是有效的邮箱格式
        }
        
        String localPart = email.substring(0, atIndex);
        String domainPart = email.substring(atIndex);
        
        if (localPart.length() <= 2) {
            // 用户名部分太短，只显示第一个字符
            return localPart.charAt(0) + "***" + domainPart;
        } else if (localPart.length() <= 4) {
            // 用户名部分较短，显示前2个字符
            return localPart.substring(0, 2) + "***" + domainPart;
        } else {
            // 用户名部分较长，显示前3个字符
            return localPart.substring(0, 3) + "***" + domainPart;
        }
    }

    /**
     * 姓名脱敏
     * 显示第一个字符，其余用*代替
     * 例如：张三 -> 张*，李四五 -> 李**
     *
     * @param name 姓名
     * @return 脱敏后的姓名
     */
    public static String maskName(String name) {
        if (StringUtils.isBlank(name)) {
            return name;
        }
        
        if (name.length() <= 1) {
            return name;
        }
        
        if (name.length() == 2) {
            return name.substring(0, 1) + "*";
        }
        
        return name.substring(0, 1) + "*".repeat(name.length() - 1);
    }

    /**
     * 身份证号脱敏
     * 显示前6位和后4位，中间用********代替
     * 例如：110101199001011234 -> 110101********1234
     *
     * @param idCard 身份证号
     * @return 脱敏后的身份证号
     */
    public static String maskIdCard(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return idCard;
        }
        
        String cleanIdCard = idCard.replaceAll("[^0-9Xx]", "");
        
        if (cleanIdCard.length() < 10) {
            return idCard; // 长度不够，不进行脱敏
        }
        
        if (cleanIdCard.length() == 18) {
            // 18位身份证
            return cleanIdCard.substring(0, 6) + "********" + cleanIdCard.substring(14);
        } else if (cleanIdCard.length() == 15) {
            // 15位身份证
            return cleanIdCard.substring(0, 6) + "*****" + cleanIdCard.substring(11);
        }
        
        return idCard;
    }

    /**
     * 银行卡号脱敏
     * 只显示后4位，其余用****代替
     * 例如：6222021234567890 -> ****7890
     *
     * @param cardNumber 银行卡号
     * @return 脱敏后的银行卡号
     */
    public static String maskCardNumber(String cardNumber) {
        if (StringUtils.isBlank(cardNumber)) {
            return cardNumber;
        }
        
        String cleanCardNumber = cardNumber.replaceAll("[^0-9]", "");
        
        if (cleanCardNumber.length() < 4) {
            return cardNumber; // 长度不够，不进行脱敏
        }
        
        return "****" + cleanCardNumber.substring(cleanCardNumber.length() - 4);
    }

    /**
     * 地址脱敏
     * 显示前6个字符，其余用****代替
     * 例如：北京市朝阳区某某街道123号 -> 北京市朝阳区****
     *
     * @param address 地址
     * @return 脱敏后的地址
     */
    public static String maskAddress(String address) {
        if (StringUtils.isBlank(address)) {
            return address;
        }
        
        if (address.length() <= 6) {
            return address;
        }
        
        return address.substring(0, 6) + "****";
    }
}
