package com.yuanchuan.common.utils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.HashSet;
import java.util.stream.Collectors;

public class CityDictionary {
    // 中国大陆城市+台湾地区所有县市（简繁体）
    private static final Set<String> CITY_NAMES = new HashSet<>();

    static {
        // 中国大陆主要城市（简体）
        addCities("北京", "上海", "广州", "深圳", "杭州", "成都", "重庆",
                "西安", "南京", "武汉", "苏州", "厦门", "三亚", "青岛");

        // 台湾地区所有县市（繁体为主，同时包含常用简体写法）
        addCities("臺北", "台北", "新北", "桃園", "桃园", "臺中", "台中",
                "臺南", "台南", "高雄", "基隆", "新竹", "新竹市", "新竹縣", "新竹县",
                "苗栗", "彰化", "南投", "雲林", "云林", "嘉義", "嘉义", "嘉義市", "嘉义市",
                "嘉義縣", "嘉义县", "屏東", "屏东", "宜蘭", "宜兰", "花蓮", "花莲",
                "臺東", "台东", "澎湖", "金門", "金门", "連江", "连江");
    }

    private static void addCities(String... cities) {
        for (String city : cities) {
            CITY_NAMES.add(city);
        }
    }

    /**
     * 检查输入是否是已知城市名称
     */
    public static boolean isCityName(String input) {
        if (input == null || input.isEmpty()) {
            return false;
        }
        return CITY_NAMES.contains(input.trim());
    }

}
