package com.yuanchuan.common.utils;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * @description
 * @date 2024-06-13 17:20
 */
public class DateTimeUtils {

    public static final String YDMHMS_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String ESTIMATE_DELIVERY_TIME = "MM-dd HH:mm";
    public static final String YDM_PATTERN = "yyyy-MM-dd";

    /**
     * 当前UTC时间
     **/
    public static LocalDateTime nowUtc() {
        //直接获取UTC的当前时间
        return LocalDateTime.now(ZoneId.of("UTC"));
    }

    /**
     * 当前UTC时间
     **/
    public static Date nowUtcDate() {
        //直接获取UTC的当前时间
        return new DateTime(TimeZone.getTimeZone("UTC"));
    }

    /**
     * 当前系统时间
     **/
    public static LocalDateTime nowDefault() {
        return LocalDateTime.now();
    }

    /**
     * 当前指定时间转UTC时间
     **/
    public static LocalDateTime convertToUtc(LocalDateTime dateTime) {
        ZonedDateTime utcZonedDateTime = dateTime.atZone(ZoneId.of("UTC"));
        return utcZonedDateTime.toLocalDateTime();
    }

    /**
     * UTC时间转当前时间
     **/
    public static LocalDateTime convertToDefault(LocalDateTime utcDateTime) {
        ZonedDateTime defultDateTime = utcDateTime.atZone(ZoneId.systemDefault());
        return defultDateTime.toLocalDateTime();
    }

    public static int timeToMinutes(String time) {
        String[] parts = time.split(":");
        int hours = Integer.parseInt(parts[0]);
        int minutes = Integer.parseInt(parts[1]);
        return hours * 60 + minutes;
    }

    /**
     * 获取星期数
     */
    public static int dayOfWeek(Date date) {
        int week = DateUtil.dayOfWeek(date);
        if (week == 1) {
            return 7;
        }
        return week - 1;
    }

    public static boolean isTimeOverlap(String startTime1, String endTime1, String startTime2, String endTime2) {
        int start1 = timeToMinutes(startTime1);
        int end1 = timeToMinutes(endTime1);
        int start2 = timeToMinutes(startTime2);
        int end2 = timeToMinutes(endTime2);

        // 检查时间是否重叠
        boolean overlaps = start1 < end2 && start2 < end1;

        // 检查时间是否连续
        boolean contiguous = end1 == start2 || end2 == start1;

        // 返回两个时间段是否重叠或者连续
        return overlaps || contiguous;
    }

    public static boolean checkOverlaps(List<String[]> timeIntervals) {
        for (int i = 0; i < timeIntervals.size(); i++) {
            for (int j = i + 1; j < timeIntervals.size(); j++) {
                String[] firstInterval = timeIntervals.get(i);
                String[] secondInterval = timeIntervals.get(j);
                if (isTimeOverlap(firstInterval[0], firstInterval[1], secondInterval[0], secondInterval[1])) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 返回yyyy-MM-dd HH:mm:ss 格式的当前的utc时间
     */
    public static String getCurrentUtcTime() {
        return DateUtil.format(nowUtcDate(), "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 返回utc的当前时间的时间戳
     */
    public static long getCurrentUtcTimeStamp() {
        return nowUtcDate().getTime();
    }

    /**
     * 获取指定时区的时间
     */
    public static Date getTimeByTimeZone(String timeZone) {
        return new DateTime(TimeZone.getTimeZone(timeZone));
    }

    /**
     * 将指定时间转化为指定时区的时间
     */
    public static Date convertTimeToTimeZone(Date date, String timeZone) {
        return new DateTime(date, TimeZone.getTimeZone(timeZone));
    }


    /**
     * 获取当天utc开始时间
     */
    public static LocalDateTime getTodayUTCStartTime(ZoneId zoneId) {
        LocalDateTime localStartTime = LocalDate.now().atStartOfDay();
        ZonedDateTime utcStartTime = localStartTime.atZone(zoneId).withZoneSameInstant(ZoneOffset.UTC);
        return utcStartTime.toLocalDateTime();
    }

    /**
     * 获取当天utc结束时间
     */
    public static LocalDateTime getTodayUTCEndTime(ZoneId zoneId) {
        LocalDateTime localEndTime = LocalDate.now().atTime(LocalTime.MAX);
        ZonedDateTime utcEndTime = localEndTime.atZone(zoneId).withZoneSameInstant(ZoneOffset.UTC);
        return utcEndTime.toLocalDateTime();
    }

    /**
     * 获取某utc开始时间
     */
    public static LocalDateTime getUTCStartTime(ZoneId zoneId,Integer minusDays) {
        LocalDateTime localStartTime = LocalDate.now().minusDays(minusDays).atStartOfDay();
        ZonedDateTime utcStartTime = localStartTime.atZone(zoneId).withZoneSameInstant(ZoneOffset.UTC);
        return utcStartTime.toLocalDateTime();
    }

    /**
     * 获取前n天utc结束时间
     */
    public static LocalDateTime getUTCEndTime(ZoneId zoneId,Integer minusDays) {
        LocalDateTime localEndTime = LocalDate.now().minusDays(minusDays).atTime(LocalTime.MAX);
        ZonedDateTime utcEndTime = localEndTime.atZone(zoneId).withZoneSameInstant(ZoneOffset.UTC);
        return utcEndTime.toLocalDateTime();
    }

    /**
     * 获取utc开始时间
     */
    public static LocalDateTime getUTCStartTime(ZoneId zoneId,LocalDateTime localDateTime) {
        LocalDateTime localStartTime = localDateTime.toLocalDate().atStartOfDay();
        ZonedDateTime utcStartTime = localStartTime.atZone(zoneId).withZoneSameInstant(ZoneOffset.UTC);
        return utcStartTime.toLocalDateTime();
    }

    /**
     * 获取utc结束时间
     */
    public static LocalDateTime getUTCEndTime(ZoneId zoneId,LocalDateTime localDateTime) {
        LocalDateTime localEndTime = localDateTime.toLocalDate().atTime(LocalTime.MAX);
        ZonedDateTime utcEndTime = localEndTime.atZone(zoneId).withZoneSameInstant(ZoneOffset.UTC);
        return utcEndTime.toLocalDateTime();
    }

    /**
     * 获取utc时间
     */
    public static LocalDateTime getUTCTime(ZoneId zoneId,LocalDateTime localDateTime) {
        ZonedDateTime utcDateTime = localDateTime.atZone(zoneId).withZoneSameInstant(ZoneOffset.UTC);
        return utcDateTime.toLocalDateTime();
    }

    /**
     * 获取某月的utc开始时间
     */
    public static LocalDateTime getMonthUTCStartTime(ZoneId zoneId, String monthStr) {
        // 使用 YearMonth 解析年份和月份
        YearMonth yearMonth = YearMonth.parse(monthStr);
        // 获取该月的第一天
        LocalDate startOfMonth = yearMonth.atDay(1);
        LocalDateTime localStartTime = startOfMonth.atStartOfDay();
        ZonedDateTime utcStartTime = localStartTime.atZone(zoneId).withZoneSameInstant(ZoneOffset.UTC);
        return utcStartTime.toLocalDateTime();
    }

    /**
     * 获取某月的utc结束时间
     */
    public static LocalDateTime getMonthUTCEndTime(ZoneId zoneId,String monthStr) {
        // 使用 YearMonth 解析年份和月份
        YearMonth yearMonth = YearMonth.parse(monthStr);
        // 获取该月的最后一天
        LocalDate endOfMonth = yearMonth.atEndOfMonth();
        LocalDateTime localEndTime = endOfMonth.atTime(LocalTime.MAX);
        ZonedDateTime utcEndTime = localEndTime.atZone(zoneId).withZoneSameInstant(ZoneOffset.UTC);
        return utcEndTime.toLocalDateTime();
    }

    /**
     * 获取某月的ZoneId开始时间
     */
    public static LocalDateTime getMonthStartTime(ZoneId zoneId, String monthStr) {
        // 使用 YearMonth 解析年份和月份
        YearMonth yearMonth = YearMonth.parse(monthStr);

        // 获取该月的第一天
        LocalDate startOfMonth = yearMonth.atDay(1);
        LocalDateTime localStartTime = startOfMonth.atStartOfDay();
        ZonedDateTime utcStartTime = localStartTime.atZone(zoneId);
        return utcStartTime.toLocalDateTime();
    }

    /**
     * 获取某月的ZoneId结束时间
     */
    public static LocalDateTime getMonthEndTime(ZoneId zoneId,String monthStr) {
        // 使用 YearMonth 解析年份和月份
        YearMonth yearMonth = YearMonth.parse(monthStr);
        // 获取该月的最后一天
        LocalDate endOfMonth = yearMonth.atEndOfMonth();
        LocalDateTime localEndTime = endOfMonth.atTime(LocalTime.MAX);
        ZonedDateTime utcEndTime = localEndTime.atZone(zoneId);
        return utcEndTime.toLocalDateTime();
    }

    /**
     * 计算两个日期之间的分钟差
     *
     * @param date1
     * @param date2
     * @return
     */
    public static Long timeDifferenceMinutes(Date date1, Date date2) {

        // 计算时间差值（分钟）
        long diffInMillis = date2.getTime() - date1.getTime();
        long diffInMinutes = diffInMillis / (60 * 1000); // 转换为分钟

        // 确保最小值为 1 分钟
        return Math.max(1, diffInMinutes);
    }

    /**
     * 计算指定时间之后的utc时间
     *
     * @param step       时间间隔
     * @param chronoUnit 时间单位
     * @return
     */
    public static Date plus(Integer step, ChronoUnit chronoUnit) {
        // 获取当前 UTC 时间
        Instant nowUtc = Instant.now();

        // 加上 15 天
        Instant futureUtc = nowUtc.plus(Optional.ofNullable(step).orElse(0), chronoUnit);

        // 将 Instant 转换为 Date 对象
        return Date.from(futureUtc);
    }

    /**
     * 计算指定时间之后的utc时间
     *
     * @param date       当前时间
     * @param step       时间间隔
     * @param chronoUnit 时间单位
     * @return
     */
    public static Date plus(Date date,Integer step, ChronoUnit chronoUnit) {
        // 将 Date 对象转换为 Instant 对象
        Instant originalInstant = date.toInstant();

        // 在 Instant 上加上 15 天
        Instant futureInstant = originalInstant.plus(step, chronoUnit);

        // 将计算后的 Instant 转换回 Date 对象
        return Date.from(futureInstant);
    }

    public static boolean isBefore24Hours(Date statusTime) {
        return plus(statusTime, 24, ChronoUnit.HOURS).getTime() < nowUtcDate().getTime();
    }

    public static String formatDateYDM(LocalDate date) {
        if (Objects.isNull(date)) {
            return "";
        }
        return date.format(DateTimeFormatter.ofPattern(YDM_PATTERN));
    }

    public static String formatDateYDMHMS(LocalDateTime date) {
        if (Objects.isNull(date)) {
            return "";
        }
        return date.format(DateTimeFormatter.ofPattern(YDMHMS_PATTERN));
    }

    /**
     * 获取两个时间之间的秒数
     */
    public static Long getDaysBetweenTwoTime(LocalDate startTime, LocalDate endTime) {
        if (Objects.isNull(startTime) || Objects.isNull(endTime) || startTime.isEqual(endTime)) {
            return 0L;
        }
        return ChronoUnit.DAYS.between(startTime, endTime);
    }

}
