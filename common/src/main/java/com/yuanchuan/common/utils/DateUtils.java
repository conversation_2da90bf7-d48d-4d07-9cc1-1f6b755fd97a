package com.yuanchuan.common.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class DateUtils {

    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";
    public static final String DEFAULT_DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT);
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern(DEFAULT_DATETIME_FORMAT);

    // ==================== LocalDate 转 String ====================

    public static String toDateString(LocalDate date) {
        return date.format(DATE_FORMATTER);
    }

    public static String toDateTimeString(LocalDateTime dateTime) {
        return dateTime.format(DATETIME_FORMATTER);
    }

    public static String toCustomString(LocalDateTime dateTime, String pattern) {
        return dateTime.format(DateTimeFormatter.ofPattern(pattern));
    }

    // ==================== String 转 LocalDate / LocalDateTime ====================

    public static LocalDate parseToLocalDate(String dateStr) {
        return LocalDate.parse(dateStr, DATE_FORMATTER);
    }

    public static LocalDateTime parseToLocalDateTime(String dateTimeStr) {
        return LocalDateTime.parse(dateTimeStr, DATETIME_FORMATTER);
    }

    public static LocalDateTime parseToLocalDateTime(String dateTimeStr, String pattern) {
        return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern(pattern));
    }

    // ==================== Date 与 LocalDateTime 互转 ====================

    public static LocalDateTime dateToLocalDateTime(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date localDateToDate(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    public static LocalDate dateToLocalDate(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }
    /**
            * 获取当前日期的格式化字符串（yyyy-MM-dd）
            * @return 示例：2025-05-27
            */
    public static String getCurrentDate() {
        return formatDate(new Date());
    }

    /**
            * 获取指定日期的格式化字符串（yyyy-MM-dd）
            * @param date 输入的日期对象
     * @return 示例：2025-12-25
            */
    public static String formatDate(Date date) {
        return DateUtil.format(date, DatePattern.NORM_DATE_FORMAT);
    }

    // ================== 星期部分 ================== //

    /**
            * 获取当前日期的星期缩写（全大写）
            * @return 示例：TUE
     */
    public static String getCurrentWeekday() {
        return getWeekdayAbbreviation(new Date());
    }

    /**
            * 获取指定日期的星期缩写（全大写）
            * @param date 输入的日期对象
     * @return 示例：THU
     */
    public static String getWeekdayAbbreviation(Date date) {
        int dayOfWeek = DateUtil.dayOfWeek(date); // 1=周一, 7=周日

        return switch (dayOfWeek) {
            case 2 -> "MON";
            case 3 -> "TUE";
            case 4 -> "WED";
            case 5 -> "THU";
            case 6 -> "FRI";
            case 7 -> "SAT";
            case 1 -> "SUN";
            default -> "UNKNOWN";
        };
    }

    // ================== 测试 ================== //
    public static void main(String[] args) {
        // 测试当前日期
        System.out.println("当前日期: " + getCurrentDate());      // 2025-05-27
        System.out.println("当前星期: " + getCurrentWeekday());  // TUE

        // 测试指定日期
        Date christmas = DateUtil.parse("2025-12-25");
        System.out.println("指定日期: " + formatDate(christmas));          // 2025-12-25
        System.out.println("指定星期: " + getWeekdayAbbreviation(christmas)); // THU
    }
}

