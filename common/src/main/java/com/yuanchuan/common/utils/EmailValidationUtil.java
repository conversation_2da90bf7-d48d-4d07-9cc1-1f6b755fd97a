package com.yuanchuan.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * 邮箱验证工具类
 * 提供更严格的邮箱格式验证
 *
 * <AUTHOR>
 * @date 2025/1/20
 * @version 1.0
 */
public class EmailValidationUtil {

    /**
     * 邮箱格式正则表达式
     * 符合RFC 5322标准的简化版本
     */
    private static final String EMAIL_REGEX = 
        "^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$";
    
    /**
     * 编译后的正则表达式模式
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile(EMAIL_REGEX);
    
    /**
     * 邮箱最大长度（RFC 5321标准）
     */
    private static final int MAX_EMAIL_LENGTH = 254;
    
    /**
     * 本地部分最大长度（@符号前的部分）
     */
    private static final int MAX_LOCAL_PART_LENGTH = 64;
    
    /**
     * 域名部分最大长度（@符号后的部分）
     */
    private static final int MAX_DOMAIN_PART_LENGTH = 253;

    /**
     * 验证邮箱格式是否正确
     *
     * @param email 邮箱地址
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidEmail(String email) {
        // 基本空值检查
        if (StringUtils.isBlank(email)) {
            return false;
        }
        
        // 长度检查
        if (email.length() > MAX_EMAIL_LENGTH) {
            return false;
        }
        
        // 基本格式检查
        if (!email.contains("@")) {
            return false;
        }
        
        // 检查@符号的数量（应该只有一个）
        long atCount = email.chars().filter(ch -> ch == '@').count();
        if (atCount != 1) {
            return false;
        }
        
        // 分割本地部分和域名部分
        String[] parts = email.split("@");
        if (parts.length != 2) {
            return false;
        }
        
        String localPart = parts[0];
        String domainPart = parts[1];
        
        // 检查本地部分
        if (!isValidLocalPart(localPart)) {
            return false;
        }
        
        // 检查域名部分
        if (!isValidDomainPart(domainPart)) {
            return false;
        }
        
        // 正则表达式验证
        return EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * 验证邮箱本地部分（@符号前的部分）
     *
     * @param localPart 本地部分
     * @return true表示有效，false表示无效
     */
    private static boolean isValidLocalPart(String localPart) {
        // 长度检查
        if (StringUtils.isBlank(localPart) || localPart.length() > MAX_LOCAL_PART_LENGTH) {
            return false;
        }
        
        // 不能以点号开始或结束
        if (localPart.startsWith(".") || localPart.endsWith(".")) {
            return false;
        }
        
        // 不能有连续的点号
        if (localPart.contains("..")) {
            return false;
        }
        
        return true;
    }

    /**
     * 验证邮箱域名部分（@符号后的部分）
     *
     * @param domainPart 域名部分
     * @return true表示有效，false表示无效
     */
    private static boolean isValidDomainPart(String domainPart) {
        // 长度检查
        if (StringUtils.isBlank(domainPart) || domainPart.length() > MAX_DOMAIN_PART_LENGTH) {
            return false;
        }
        
        // 不能以点号或连字符开始或结束
        if (domainPart.startsWith(".") || domainPart.endsWith(".") ||
            domainPart.startsWith("-") || domainPart.endsWith("-")) {
            return false;
        }
        
        // 不能有连续的点号
        if (domainPart.contains("..")) {
            return false;
        }
        
        // 必须包含至少一个点号（顶级域名）
        if (!domainPart.contains(".")) {
            return false;
        }
        
        // 检查域名标签
        String[] labels = domainPart.split("\\.");
        for (String label : labels) {
            if (!isValidDomainLabel(label)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 验证域名标签
     *
     * @param label 域名标签
     * @return true表示有效，false表示无效
     */
    private static boolean isValidDomainLabel(String label) {
        // 长度检查（每个标签最多63个字符）
        if (StringUtils.isBlank(label) || label.length() > 63) {
            return false;
        }
        
        // 不能以连字符开始或结束
        if (label.startsWith("-") || label.endsWith("-")) {
            return false;
        }
        
        // 只能包含字母、数字和连字符
        return label.matches("^[a-zA-Z0-9-]+$");
    }

    /**
     * 简单的邮箱格式验证（向后兼容）
     * 只检查是否包含@符号和基本长度
     *
     * @param email 邮箱地址
     * @return true表示格式基本正确，false表示格式错误
     */
    public static boolean isSimpleValidEmail(String email) {
        return StringUtils.isNotBlank(email) && 
               email.contains("@") && 
               email.length() <= MAX_EMAIL_LENGTH;
    }

    /**
     * 获取邮箱验证错误信息
     *
     * @param email 邮箱地址
     * @return 错误信息，如果格式正确则返回null
     */
    public static String getEmailValidationError(String email) {
        if (StringUtils.isBlank(email)) {
            return "邮箱地址不能为空";
        }
        
        if (email.length() > MAX_EMAIL_LENGTH) {
            return "邮箱地址长度不能超过" + MAX_EMAIL_LENGTH + "个字符";
        }
        
        if (!email.contains("@")) {
            return "邮箱地址必须包含@符号";
        }
        
        long atCount = email.chars().filter(ch -> ch == '@').count();
        if (atCount != 1) {
            return "邮箱地址只能包含一个@符号";
        }
        
        String[] parts = email.split("@");
        if (parts.length != 2) {
            return "邮箱地址格式不正确";
        }
        
        String localPart = parts[0];
        String domainPart = parts[1];
        
        if (StringUtils.isBlank(localPart)) {
            return "邮箱地址@符号前不能为空";
        }
        
        if (localPart.length() > MAX_LOCAL_PART_LENGTH) {
            return "邮箱地址@符号前的部分长度不能超过" + MAX_LOCAL_PART_LENGTH + "个字符";
        }
        
        if (StringUtils.isBlank(domainPart)) {
            return "邮箱地址@符号后不能为空";
        }
        
        if (!domainPart.contains(".")) {
            return "邮箱地址必须包含有效的域名";
        }
        
        if (!EMAIL_PATTERN.matcher(email).matches()) {
            return "邮箱地址格式不符合标准";
        }
        
        return null; // 格式正确
    }
}
