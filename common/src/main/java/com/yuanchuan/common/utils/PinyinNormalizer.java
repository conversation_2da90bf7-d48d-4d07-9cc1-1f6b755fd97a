package com.yuanchuan.common.utils;

import cn.hutool.core.util.StrUtil;

public class PinyinNormalizer {

    public static String normalize(String keyword) {
        if (StrUtil.isBlank(keyword)) return "";

        StringBuilder result = new StringBuilder();
        StringBuilder pinyinBuffer = new StringBuilder();

        for (char c : keyword.toCharArray()) {
            if (Character.isAlphabetic(c)) {
                pinyinBuffer.append(c);
            } else {
                // 遇到中文，先把之前拼音拼接处理
                if (pinyinBuffer.length() > 0) {
                    result.append(normalizePinyin(pinyinBuffer.toString()));
                    pinyinBuffer.setLength(0);
                }
                result.append(c);
            }
        }

        // 处理尾部拼音残留
        if (pinyinBuffer.length() > 0) {
            result.append(normalizePinyin(pinyinBuffer.toString()));
        }

        return result.toString();
    }

    private static String normalizePinyin(String pinyinPart) {
        // 此处可以扩展拼音简写全拼转换，如 ji -> 基
        // 你可以借助 HanLP、Pinyin4j、TinyPinyin 等库
        return pinyinPart.toLowerCase();
    }
}

