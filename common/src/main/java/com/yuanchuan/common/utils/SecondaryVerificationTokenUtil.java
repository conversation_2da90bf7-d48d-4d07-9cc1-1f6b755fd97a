package com.yuanchuan.common.utils;

import com.alibaba.fastjson2.JSON;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;

/**
 * 二次验证令牌工具类
 * 用于生成和解析二次验证过程中的加密令牌，替代明文userId传输
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/1/20
 */
@Slf4j
public class SecondaryVerificationTokenUtil {

    /**
     * AES加密算法
     */
    private static final String ALGORITHM = "AES";

    /**
     * 加密密钥（实际项目中应该从配置文件读取）
     */
    private static final String SECRET_KEY = "QLQJSecretKey123"; // 16字节密钥

    /**
     * 令牌有效期（分钟）
     */
    private static final int TOKEN_EXPIRE_MINUTES = 15;

    /**
     * 二次验证信息内部类
     */
    public static class SecondaryVerificationInfo {
        private Long userId;
        private String deviceId;
        private String createTime;

        public SecondaryVerificationInfo() {
        }

        public SecondaryVerificationInfo(Long userId, String deviceId, String createTime) {
            this.userId = userId;
            this.deviceId = deviceId;
            this.createTime = createTime;
        }

        // Getters and Setters
        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }

        public String getDeviceId() {
            return deviceId;
        }

        public void setDeviceId(String deviceId) {
            this.deviceId = deviceId;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }
    }

    /**
     * 生成二次验证令牌
     *
     * @param userId   用户ID
     * @param deviceId 设备ID
     * @return 加密的令牌字符串
     */
    public static String generateSecondaryVerificationToken(Long userId, String deviceId) {
        try {
            // 创建验证信息对象
            SecondaryVerificationInfo info = new SecondaryVerificationInfo();
            info.setUserId(userId);
            info.setDeviceId(deviceId);
            info.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

            // 转换为JSON字符串
            String jsonString = JSON.toJSONString(info);

            // AES加密
            String encryptedData = encrypt(jsonString);

            log.debug("生成二次验证令牌成功: userId={}, deviceId={}", userId, deviceId);
            return encryptedData;

        } catch (Exception e) {
            log.error("生成二次验证令牌失败: userId={}, deviceId={}", userId, deviceId, e);
            throw new BusinessException(UsersErrorCode.SYSTEM_ERROR.getCode(), UsersErrorCode.SYSTEM_ERROR.getMsg());
        }
    }

    /**
     * 解析二次验证令牌
     *
     * @param token 加密的令牌字符串
     * @return 解析出的验证信息
     * @throws BusinessException 令牌无效或已过期时抛出异常
     */
    public static SecondaryVerificationInfo parseSecondaryVerificationToken(String token) {
        if (StringUtils.isBlank(token)) {
            throw new BusinessException(UsersErrorCode.SECONDARY_VERIFICATION_TOKEN_INVALID.getCode(),
                    UsersErrorCode.SECONDARY_VERIFICATION_TOKEN_INVALID.getMsg());
        }

        try {
            // AES解密
            String decryptedData = decrypt(token);

            // 解析JSON
            SecondaryVerificationInfo info = JSON.parseObject(decryptedData, SecondaryVerificationInfo.class);

            // 验证令牌是否过期
            if (isTokenExpired(info.getCreateTime())) {
                log.warn("二次验证令牌已过期: createTime={}", info.getCreateTime());
                throw new BusinessException(UsersErrorCode.SECONDARY_VERIFICATION_TOKEN_INVALID.getCode(),
                        UsersErrorCode.SECONDARY_VERIFICATION_TOKEN_INVALID.getMsg());
            }

            log.debug("解析二次验证令牌成功: userId={}, deviceId={}", info.getUserId(), info.getDeviceId());
            return info;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("解析二次验证令牌失败: token={}", token, e);
            throw new BusinessException(UsersErrorCode.SECONDARY_VERIFICATION_TOKEN_INVALID.getCode(),
                    UsersErrorCode.SECONDARY_VERIFICATION_TOKEN_INVALID.getMsg());
        }
    }

    /**
     * AES加密
     *
     * @param data 待加密的数据
     * @return 加密后的Base64字符串
     */
    private static String encrypt(String data) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] encryptedData = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encryptedData);
    }

    /**
     * AES解密
     *
     * @param encryptedData 加密的Base64字符串
     * @return 解密后的原始数据
     */
    private static String decrypt(String encryptedData) throws Exception {
        SecretKeySpec secretKey = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        byte[] decodedData = Base64.getDecoder().decode(encryptedData);
        byte[] decryptedData = cipher.doFinal(decodedData);
        return new String(decryptedData, StandardCharsets.UTF_8);
    }

    /**
     * 检查令牌是否过期
     *
     * @param createTimeStr 创建时间字符串
     * @return true表示已过期，false表示未过期
     */
    private static boolean isTokenExpired(String createTimeStr) {
        try {
            LocalDateTime createTime = LocalDateTime.parse(createTimeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            LocalDateTime expireTime = createTime.plusMinutes(TOKEN_EXPIRE_MINUTES);
            return LocalDateTime.now().isAfter(expireTime);
        } catch (Exception e) {
            log.error("解析令牌创建时间失败: createTimeStr={}", createTimeStr, e);
            return true; // 解析失败认为已过期
        }
    }
}
