package com.yuanchuan.common.utils;

import java.util.HashMap;
import java.util.Map;

public class ZhuyinToPinyinUtil {

    private static final Map<String, String> zhuyinMap = new HashMap<>();

    static {
        // 聲母（21個）
        zhuyinMap.put("ㄅ", "b");
        zhuyinMap.put("ㄆ", "p");
        zhuyinMap.put("ㄇ", "m");
        zhuyinMap.put("ㄈ", "f");
        zhuyinMap.put("ㄉ", "d");
        zhuyinMap.put("ㄊ", "t");
        zhuyinMap.put("ㄋ", "n");
        zhuyinMap.put("ㄌ", "l");
        zhuyinMap.put("ㄍ", "g");
        zhuyinMap.put("ㄎ", "k");
        zhuyinMap.put("ㄏ", "h");
        zhuyinMap.put("ㄐ", "j");
        zhuyinMap.put("ㄑ", "q");
        zhuyinMap.put("ㄒ", "x");
        zhuyinMap.put("ㄓ", "zh");
        zhuyinMap.put("ㄔ", "ch");
        zhuyinMap.put("ㄕ", "sh");
        zhuyinMap.put("ㄖ", "r");
        zhuyinMap.put("ㄗ", "z");
        zhuyinMap.put("ㄘ", "c");
        zhuyinMap.put("ㄙ", "s");

        // 韻母（16個基本韻母 + 一些複合韻母）
        zhuyinMap.put("ㄧ", "i");
        zhuyinMap.put("ㄨ", "u");
        zhuyinMap.put("ㄩ", "ü");
        zhuyinMap.put("ㄚ", "a");
        zhuyinMap.put("ㄛ", "o");
        zhuyinMap.put("ㄜ", "e");
        zhuyinMap.put("ㄝ", "ê");
        zhuyinMap.put("ㄞ", "ai");
        zhuyinMap.put("ㄟ", "ei");
        zhuyinMap.put("ㄠ", "ao");
        zhuyinMap.put("ㄡ", "ou");
        zhuyinMap.put("ㄢ", "an");
        zhuyinMap.put("ㄣ", "en");
        zhuyinMap.put("ㄤ", "ang");
        zhuyinMap.put("ㄥ", "eng");
        zhuyinMap.put("ㄦ", "er");

        // 聲調符號（可選）
        zhuyinMap.put("ˉ", "");
        zhuyinMap.put("ˊ", "");
        zhuyinMap.put("ˇ", "");
        zhuyinMap.put("ˋ", "");
        zhuyinMap.put("˙", ""); // 輕聲

        // 空格和其他处理
        zhuyinMap.put("　", " "); // 全角空格
    }

    /**
     * 将注音符号字符串转换为对应拼音（不区分单词）
     * @param zhuyin 原始注音符号字符串（如：ㄎㄣˇ ㄉㄜˊ）
     * @return 转换后的拼音（如：ken3 de2）
     */
    public static String convert(String zhuyin) {
        if (zhuyin == null || zhuyin.isEmpty()) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (char ch : zhuyin.toCharArray()) {
            String s = String.valueOf(ch);
            sb.append(zhuyinMap.getOrDefault(s, s));
        }
        return sb.toString();
    }

    // 示例用法
    public static void main(String[] args) {
//        String input = "ㄎㄣˇ ㄉㄜˊ ㄐㄧ";
        String input = "肯德ㄐㄧ";
        if (containsZhuyin(input)){
            String result = convert(input);
            System.out.println("注音：" + input);
            System.out.println("拼音：" + result);
        }else {
            System.out.println(input);
        }
    }

    public static boolean containsZhuyin(String input) {
        for (char ch : input.toCharArray()) {
            if ((ch >= '\u3100' && ch <= '\u312F') || (ch >= '\u31A0' && ch <= '\u31BF')) {
                return true;
            }
        }
        return false;
    }
}

