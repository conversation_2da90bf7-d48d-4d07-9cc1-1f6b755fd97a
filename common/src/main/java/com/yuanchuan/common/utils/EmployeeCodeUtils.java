package com.yuanchuan.common.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @date 2025-06-04 11:35:11:35
 */
public class EmployeeCodeUtils {

    private static final String PREFIX = "EMP";
    private static final int RANDOM_LENGTH = 4;
    private static final String CHAR_POOL = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final int MAX_RETRY = 5;

    /**
     * 生成员工编码，格式：EMP + yyyyMMdd + 4位随机字符串
     * @return 员工编码字符串
     */
    public static String generateEmployeeCode() {
        String datePart = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String randomPart = generateRandomString(RANDOM_LENGTH);
        return PREFIX + datePart + randomPart;
    }

    /**
     * 带检测的生成员工编码，避免重复
     * @param existsCheck 判断编码是否已存在的函数接口，返回true表示已存在，需重试
     * @return 不重复的员工编码
     * @throws RuntimeException 超过最大重试次数仍未生成唯一编码
     */
    public static String generateUniqueEmployeeCode(Predicate<String> existsCheck) {
        for (int i = 0; i < MAX_RETRY; i++) {
            String code = generateEmployeeCode();
            if (!existsCheck.test(code)) {
                return code;
            }
        }
        throw new RuntimeException("超过最大重试次数，无法生成唯一员工编码");
    }

    private static String generateRandomString(int length) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append(CHAR_POOL.charAt(random.nextInt(CHAR_POOL.length())));
        }
        return sb.toString();
    }

    // 简单测试
    public static void main(String[] args) {
        // 不带检测，可能重复
        System.out.println(generateEmployeeCode());
    }
}
