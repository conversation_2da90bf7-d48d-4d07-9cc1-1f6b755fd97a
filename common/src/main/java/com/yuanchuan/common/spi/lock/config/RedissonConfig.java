package com.yuanchuan.common.spi.lock.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.yuanchuan.common.spi.lock.DistributedLock;
import com.yuanchuan.common.spi.lock.RedissonDistributedLock;

@Configuration
public class RedissonConfig {

    @Value("${spring.data.redis.host:localhost}")
    private String host;

    @Value("${spring.data.redis.port:6379}")
    private String port;

    @Value("${spring.data.redis.password:}")
    private String password;

     @Bean
     public RedissonClient redissonClient() {
         Config config = new Config();
         String address = String.format("redis://%s:%s", host, port);

         config.useSingleServer()
                 .setConnectionPoolSize(10)
                 .setConnectionMinimumIdleSize(5)
                 .setAddress(address)
                 .setPassword(password.isEmpty() ? null : password);
         return Redisson.create(config);
     }

//    @Bean
//    public RedissonClient redissonClient() {
//        Config config = new Config();
//        // 对于Azure Redis Cache，使用rediss://协议启用TLS
//        String address = String.format("rediss://%s:%s", host, port);
//
//        config.useSingleServer()
//                .setConnectionPoolSize(10)
//                .setConnectionMinimumIdleSize(5)
//                .setAddress(address)
//                .setPassword(password.isEmpty() ? null : password)
//                // 启用SSL/TLS
//                .setSslEnableEndpointIdentification(true)
//                // 可选：设置连接超时
//                .setConnectTimeout(10000)
//                .setTimeout(3000);
//
//        return Redisson.create(config);
//    }

    @Bean
    public DistributedLock distributedLock(RedissonClient redissonClient) {
        return new RedissonDistributedLock(redissonClient);
    }
}