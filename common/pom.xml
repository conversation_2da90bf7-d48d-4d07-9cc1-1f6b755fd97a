<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.yuanchuan.common</groupId>
    <artifactId>common</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>common</name>
    <description>common</description>
    <packaging>jar</packaging>
	<!--
    <scm>
        
        <connection>scm:git:http://gitlab.meichai.in/humancloud/javasrc/data-spec-util.git</connection>
        
        <developerConnection>scm:git:http://gitlab.meichai.in/humancloud/javasrc/data-spec-util.git
        </developerConnection>
        
        <url>http://gitlab.meichai.in/humancloud/javasrc/data-spec-util.git</url>
        <tag>HEAD</tag>
    </scm>
	-->
	<!--可读的SCM地址-->
	<!--可写的SCM地址-->
	<!--可以在浏览器中访问的scm地址-->
	
<!-- deploy pom -->
<!--    <distributionManagement>-->
<!--        <repository>-->
<!--            <id>${repository.id}</id>-->
<!--            <name>${repository.name}</name>-->
<!--            &lt;!&ndash;suppress UnresolvedMavenProperty &ndash;&gt;-->
<!--            <url>${repository.url}</url>-->
<!--            <layout>default</layout>-->
<!--        </repository>-->
<!--        <snapshotRepository>-->
<!--            <id>${repository.id}</id>-->
<!--            <name>${repository.name}</name>-->
<!--            &lt;!&ndash;suppress UnresolvedMavenProperty &ndash;&gt;-->
<!--            <url>${repository.url}</url>-->
<!--            <layout>default</layout>-->
<!--        </snapshotRepository>-->
<!--    </distributionManagement>-->
    <repositories>
        <repository>
            <id>aliyun</id>
            <name>Aliyun Maven Repository</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <!-- Confluent Maven仓库 -->
        <repository>
            <id>confluent</id>
            <name>Confluent Maven Repository</name>
            <url>https://packages.confluent.io/maven/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
    <properties>
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring-boot.version>3.2.3</spring-boot.version>
        <spring-cloud.version>2023.0.0</spring-cloud.version>
        <dubbo.version>3.2.9</dubbo.version>
        <mybatis-plus.version>3.5.5</mybatis-plus.version>
        <mybatis-spring.version>3.0.4</mybatis-spring.version>
        <mysql.version>8.0.33</mysql.version>
        <redisson.version>3.17.0</redisson.version>
        <lombok.version>1.18.30</lombok.version>
        <zookeeper.version>3.9.3</zookeeper.version>
        <curator.version>5.1.0</curator.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <jjwt.version>0.11.5</jjwt.version>
        <netty.version>4.1.100.Final</netty.version>
        <maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
        <maven-source-plugin.version>3.3.1</maven-source-plugin.version>
        <maven-compiler-plugin.version>3.6.0</maven-compiler-plugin.version>
        <kafka.version>3.3.4</kafka.version>
    </properties>

    <dependencies>
        <!-- Spring Boot Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- JWT Dependencies -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>${jjwt.version}</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>${jjwt.version}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>${jjwt.version}</version>
            <scope>runtime</scope>
        </dependency>

        <!-- Spring Security -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.25</version>
        </dependency>

        <!-- FastJSON2 -->
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>2.0.45</version>
        </dependency>

        <!-- Azure Service Bus -->
        <dependency>
            <groupId>com.azure</groupId>
            <artifactId>azure-messaging-servicebus</artifactId>
        </dependency>
        <!-- Azure Core -->
        <dependency>
            <groupId>com.azure</groupId>
            <artifactId>azure-core</artifactId>
        </dependency>
        <!-- Azure Core AMQP -->
        <dependency>
            <groupId>com.azure</groupId>
            <artifactId>azure-core-amqp</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
       
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-messaging</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>

        <!-- SpringDoc OpenAPI (替代 Springfox Swagger) -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>2.5.0</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.httpcomponents.client5/httpclient5 -->
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
            <version>5.4.4</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-databind -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.18.3</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.httpcomponents.core5/httpcore5 -->
        <dependency>
            <groupId>org.apache.httpcomponents.core5</groupId>
            <artifactId>httpcore5</artifactId>
            <version>5.3.4</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/commons-codec/commons-codec -->
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.18.0</version>
        </dependency>


    </dependencies>
    
    <dependencyManagement>
        <dependencies>
            <!--Spring Boot 版本依赖-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>Hoxton.SR5</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Azure Service Bus -->
            <dependency>
                <groupId>com.azure</groupId>
                <artifactId>azure-messaging-servicebus</artifactId>
                <version>7.15.0</version>
            </dependency>
            <!-- Azure Core -->
            <dependency>
                <groupId>com.azure</groupId>
                <artifactId>azure-core</artifactId>
                <version>1.45.0</version>
            </dependency>
            <!-- Azure Core AMQP -->
            <dependency>
                <groupId>com.azure</groupId>
                <artifactId>azure-core-amqp</artifactId>
                <version>2.9.0</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-messaging</artifactId>
                <version>6.1.5</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.integration</groupId>
                <artifactId>spring-integration-core</artifactId>
            </dependency>
            <dependency>
                <groupId>com.azure.spring</groupId>
                <artifactId>spring-cloud-azure-starter-servicebus</artifactId>
                <version>5.8.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <!--生成源码jar包-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven-source-plugin.version}</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>github</id>
            <name>GitHub Packages</name>
            <url>https://maven.pkg.github.com/farfarfar-away/background-common</url>
        </repository>
    </distributionManagement>

</project>
