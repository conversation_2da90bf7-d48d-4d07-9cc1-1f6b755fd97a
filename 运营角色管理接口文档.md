# 运营角色管理接口文档

## 文档信息
- **版本**：1.0
- **作者**：系统设计团队
- **日期**：2025-01-27
- **状态**：正式版

## 1. 概述

本文档定义了运营角色管理相关的API接口，包括角色的查询、创建、更新、删除等功能。这些接口主要用于运营后台管理系统，提供完整的角色管理能力。

## 2. 接口基础信息

### 2.1 基础路径
所有角色管理接口的基础路径为：`/api/roles`

### 2.2 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

**说明**：
- `code`：响应状态码，200表示成功
- `message`：响应消息，success表示成功
- `data`：响应数据，具体结构根据接口而定

## 3. 接口列表

### 3.1 分页查询角色列表

- **接口路径**：`POST /api/roles/list`
- **功能描述**：根据条件分页查询角色列表，支持按角色ID、状态、关键词等条件筛选
- **请求参数**：
  - 请求体：`RoleQueryRequest`
    - `roleId`：角色ID，字符串，可选
    - `status`：角色状态，整数，可选，0-禁用，1-启用
    - `keyword`：关键词搜索，字符串，可选，支持角色名称或权限名称搜索
    - `pageNo`：页码，整数，必填，从1开始
    - `pageSize`：每页大小，整数，必填，最大100
- **响应结果**：
  - 成功：`200 OK`，返回`PageResult<RoleDTO>`
  - 失败：
    - `400 Bad Request`：请求参数错误
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要管理员认证
- **请求示例**：

```http
POST /api/roles/list HTTP/1.1
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "roleId": "ADMIN",
  "status": 1,
  "keyword": "管理员",
  "pageNo": 1,
  "pageSize": 10
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "roleCode": "ADMIN",
        "roleName": "系统管理员",
        "description": "系统管理员角色",
        "status": 1,
        "permissionIds": [1, 2, 3],
        "permissionNames": ["用户管理", "角色管理", "权限管理"],
        "updatedAt": "2025-01-27T10:30:00",
        "updatedBy": "admin"
      }
    ],
    "total": 1,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

### 3.2 获取角色下拉列表

- **接口路径**：`GET /api/roles/select`
- **功能描述**：获取所有有效角色的下拉选项，用于角色选择的下拉列表
- **请求参数**：无
- **响应结果**：
  - 成功：`200 OK`，返回`List<RoleSelectDTO>`
  - 失败：
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要管理员认证
- **请求示例**：

```http
GET /api/roles/select HTTP/1.1
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "roleId": 1,
      "roleName": "系统管理员"
    },
    {
      "roleId": 2,
      "roleName": "运营管理员"
    }
  ]
}
```

### 3.3 创建角色

- **接口路径**：`POST /api/roles/createRole`
- **功能描述**：创建新的角色，包括角色基本信息和权限分配
- **请求参数**：
  - 请求体：`RoleCreateRequest`
    - `roleName`：角色名称，字符串，必填，最大50个字符
    - `roleCode`：角色编码，字符串，必填，最大50个字符
    - `description`：角色描述，字符串，可选，最大200个字符
    - `permissionIds`：权限ID列表，数组，必填
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：请求参数错误或角色编码已存在
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要管理员认证
- **请求示例**：

```http
POST /api/roles/createRole HTTP/1.1
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "roleName": "商户管理员",
  "roleCode": "MERCHANT_ADMIN",
  "description": "商户管理员角色，负责管理商户信息",
  "permissionIds": [1, 2, 3, 4]
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.4 更新角色

- **接口路径**：`POST /api/roles/updateRole`
- **功能描述**：更新角色信息，包括角色基本信息、状态和权限分配
- **请求参数**：
  - 请求体：`RoleUpdateRequest`
    - `id`：角色ID，Long类型，必填
    - `roleName`：角色名称，字符串，可选，最大50个字符
    - `roleCode`：角色编码，字符串，可选，最大50个字符
    - `description`：角色描述，字符串，可选，最大200个字符
    - `status`：角色状态，整数，可选，0-禁用，1-启用
    - `permissionIds`：权限ID列表，数组，可选
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：请求参数错误或角色不存在
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要管理员认证
- **请求示例**：

```http
POST /api/roles/updateRole HTTP/1.1
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "id": 123,
  "roleName": "商户管理员（更新）",
  "roleCode": "MERCHANT_ADMIN_V2",
  "description": "商户管理员角色（已更新）",
  "status": 1,
  "permissionIds": [1, 2, 3, 4, 5]
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.5 根据ID查询角色详情

- **接口路径**：`GET /api/roles/getRoleById/{id}`
- **功能描述**：查询指定ID的角色详细信息
- **请求参数**：
  - 路径参数：
    - `id`：角色ID，Long类型，必填
- **响应结果**：
  - 成功：`200 OK`，返回`RoleDTO`
  - 失败：
    - `400 Bad Request`：请求参数错误
    - `404 Not Found`：角色不存在
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要管理员认证
- **请求示例**：

```http
GET /api/roles/getRoleById/123 HTTP/1.1
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 123,
    "roleCode": "MERCHANT_ADMIN",
    "roleName": "商户管理员",
    "description": "商户管理员角色",
    "status": 1,
    "permissionIds": [1, 2, 3, 4],
    "permissionNames": ["商户管理", "订单管理", "财务管理", "数据统计"],
    "updatedAt": "2025-01-27T10:30:00",
    "updatedBy": "admin"
  }
}
```

### 3.6 删除角色

- **接口路径**：`DELETE /api/roles/deleteRole/{id}`
- **功能描述**：删除指定ID的角色
- **请求参数**：
  - 路径参数：
    - `id`：角色ID，Long类型，必填
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：请求参数错误或角色正在使用中
    - `404 Not Found`：角色不存在
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要管理员认证
- **请求示例**：

```http
DELETE /api/roles/deleteRole/123 HTTP/1.1
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.7 根据账户ID查询用户角色

- **接口路径**：`GET /api/roles/user/{accountId}`
- **功能描述**：查询指定账户的角色列表
- **请求参数**：
  - 路径参数：
    - `accountId`：账户ID，Long类型，必填
- **响应结果**：
  - 成功：`200 OK`，返回`List<RoleDTO>`
  - 失败：
    - `400 Bad Request`：请求参数错误
    - `404 Not Found`：账户不存在
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要管理员认证
- **请求示例**：

```http
GET /api/roles/user/123456 HTTP/1.1
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "roleCode": "ADMIN",
      "roleName": "系统管理员",
      "description": "系统管理员角色",
      "status": 1,
      "permissionIds": [1, 2, 3],
      "permissionNames": ["用户管理", "角色管理", "权限管理"],
      "updatedAt": "2025-01-27T10:30:00",
      "updatedBy": "admin"
    }
  ]
}
```

## 4. 数据模型

### 4.1 请求模型

#### 4.1.1 RoleQueryRequest

```java
@Data
@Schema(description = "角色查询请求")
public class RoleQueryRequest extends PageQueryV {
    @Schema(description = "角色ID")
    private String roleId;

    @Schema(description = "角色状态：0-禁用，1-启用")
    private Integer status;

    @Schema(description = "关键词搜索（角色名称或权限名称）")
    private String keyword;

    @Schema(description = "页码，从1开始")
    private Integer pageNo;

    @Schema(description = "每页大小，最大100")
    private Integer pageSize;
}
```

#### 4.1.2 RoleCreateRequest

```java
@Data
@Schema(description = "角色创建请求")
public class RoleCreateRequest {
    @NotBlank(message = "角色名稱不可為空")
    @Size(max = 50, message = "角色名稱不能超過 50 個字元")
    @Schema(description = "角色名称", required = true)
    private String roleName;

    @NotBlank(message = "角色編碼不可為空")
    @Size(max = 50, message = "角色編碼不能超過 50 個字元")
    @Schema(description = "角色编码", required = true)
    private String roleCode;

    @Size(max = 200, message = "角色描述不能超過 200 個字元")
    @Schema(description = "角色描述")
    private String description;

    @NotNull(message = "權限ID列表不可為空")
    @Schema(description = "权限ID列表", required = true)
    private List<Long> permissionIds;
}
```

#### 4.1.3 RoleUpdateRequest

```java
@Data
@Schema(description = "角色更新请求")
public class RoleUpdateRequest {
    @NotNull(message = "角色ID不可為空")
    @Schema(description = "角色ID", required = true)
    private Long id;

    @Size(max = 50, message = "角色名稱不能超過 50 個字元")
    @Schema(description = "角色名称")
    private String roleName;

    @Size(max = 50, message = "角色編碼不能超過 50 個字元")
    @Schema(description = "角色编码")
    private String roleCode;

    @Size(max = 200, message = "角色描述不能超過 200 個字元")
    @Schema(description = "角色描述")
    private String description;

    @Schema(description = "角色状态：0-禁用，1-启用")
    private Integer status;

    @Schema(description = "权限ID列表")
    private List<Long> permissionIds;
}
```

### 4.2 响应模型

#### 4.2.1 RoleDTO

```java
@Data
@Schema(description = "角色DTO")
public class RoleDTO {
    @Schema(description = "角色ID")
    private Long id;

    @Schema(description = "角色编码")
    private String roleCode;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "角色描述")
    private String description;

    @Schema(description = "角色状态：0-禁用，1-启用")
    private Integer status;

    @Schema(description = "所属权限Id列表")
    private List<Long> permissionIds;

    @Schema(description = "所属权限名称列表")
    private List<String> permissionNames;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Schema(description = "更新人")
    private String updatedBy;
}
```

#### 4.2.2 RoleSelectDTO

```java
@Data
@Schema(description = "角色下拉选择DTO")
public class RoleSelectDTO {
    @Schema(description = "角色ID")
    private Long roleId;

    @Schema(description = "角色名称")
    private String roleName;
}
```

#### 4.2.3 PageResult

```java
@Data
@Schema(description = "分页结果")
public class PageResult<T> {
    @Schema(description = "数据列表")
    private List<T> records;

    @Schema(description = "总记录数")
    private Long total;

    @Schema(description = "当前页码")
    private Integer pageNum;

    @Schema(description = "每页大小")
    private Integer pageSize;
}
```

## 5. 错误码

| 错误码 | 错误消息 | 描述 |
|-------|---------|------|
| ROLE_NOT_FOUND | 角色不存在 | 指定的角色ID不存在 |
| ROLE_CODE_EXISTS | 角色编码已存在 | 创建角色时使用的编码已被占用 |
| ROLE_IN_USE | 角色正在使用中 | 删除角色时，该角色正被用户使用 |
| ROLE_NAME_REQUIRED | 角色名称不能为空 | 创建角色时角色名称为空 |
| ROLE_CODE_REQUIRED | 角色编码不能为空 | 创建角色时角色编码为空 |
| ROLE_NAME_TOO_LONG | 角色名称过长 | 角色名称超过50个字符 |
| ROLE_CODE_TOO_LONG | 角色编码过长 | 角色编码超过50个字符 |
| ROLE_DESC_TOO_LONG | 角色描述过长 | 角色描述超过200个字符 |
| PERMISSION_IDS_REQUIRED | 权限ID列表不能为空 | 创建角色时权限ID列表为空 |
| INVALID_PERMISSION_ID | 无效的权限ID | 权限ID列表中包含不存在的权限ID |
| ACCOUNT_NOT_FOUND | 账户不存在 | 查询用户角色时指定的账户ID不存在 |
| UNAUTHORIZED | 未授权访问 | 访问接口时缺少有效的认证信息 |
| FORBIDDEN | 权限不足 | 当前用户没有执行该操作的权限 |

## 变更历史
| 版本 | 日期 | 作者 | 变更描述 | 关联功能/需求 |
|-----|------|------|---------|-------------|
| 1.0 | 2025-01-27 | 系统设计团队 | 初始版本 | 运营角色管理功能 |
