<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.yuanchuan.gateway</groupId>
    <artifactId>gateway-app</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>gateway-app</name>
    <description>Apache ShenYu Gateway Application</description>

    <properties>
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring-boot.version>3.3.9</spring-boot.version>
        <spring-cloud.version>2023.0.5</spring-cloud.version>
        <dubbo.version>3.2.9</dubbo.version>
        <mybatis-plus.version>3.5.5</mybatis-plus.version>
        <mybatis-spring.version>3.0.4</mybatis-spring.version>
        <mysql.version>8.0.33</mysql.version>
        <redisson.version>3.17.0</redisson.version>
        <lombok.version>1.18.30</lombok.version>
        <zookeeper.version>3.9.3</zookeeper.version>
        <curator.version>5.1.0</curator.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <jjwt.version>0.11.5</jjwt.version>
        <netty.version>4.1.100.Final</netty.version>
        <shenyu.version>2.6.1</shenyu.version>
        <knife4j.version>4.5.0</knife4j.version>
        <common.version>1.0.0-SNAPSHOT</common.version>
        <user.version>1.0.0-SNAPSHOT</user.version>
        <merchant.version>1.0.0-SNAPSHOT</merchant.version>
        <order.version>1.0.0-SNAPSHOT</order.version>
        <review.version>1.0.0-SNAPSHOT</review.version>
        <reservation.version>1.0.0-SNAPSHOT</reservation.version>
        <reservation.version>1.0.0-SNAPSHOT</reservation.version>
        <authentication.version>1.0.0-SNAPSHOT</authentication.version>
        <file.version>1.0.0-SNAPSHOT</file.version>
        <location.version>1.0.0-SNAPSHOT</location.version>
    </properties>

    <dependencies>
        <!-- ShenYu Gateway -->
        <dependency>
            <groupId>org.apache.shenyu</groupId>
            <artifactId>shenyu-spring-boot-starter-gateway</artifactId>
            <version>${shenyu.version}</version>
        </dependency>

        <!-- ShenYu Admin Client -->
        <dependency>
            <groupId>org.apache.shenyu</groupId>
            <artifactId>shenyu-spring-boot-starter-sync-data-websocket</artifactId>
            <version>${shenyu.version}</version>
        </dependency>

        <!-- ShenYu Dubbo Plugin -->
        <dependency>
            <groupId>org.apache.shenyu</groupId>
            <artifactId>shenyu-spring-boot-starter-plugin-apache-dubbo</artifactId>
            <version>${shenyu.version}</version>
        </dependency>

        <!-- Spring Cloud Config Client -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-config</artifactId>
        </dependency>
        <!-- Spring Cloud Bootstrap -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <!-- Spring Boot -->
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-webflux</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <!-- Dubbo -->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
            <version>${dubbo.version}</version>
        </dependency>

        <!-- Zookeeper -->
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <version>${zookeeper.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
            <version>${curator.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
            <version>${curator.version}</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.curator/curator-x-discovery -->
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-x-discovery</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.yuanchuan.common</groupId>
            <artifactId>common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yuanchuan.authentication</groupId>
            <artifactId>authentication-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yuanchuan.user</groupId>
            <artifactId>user-api</artifactId>
        </dependency>
        <dependency>
           <groupId>com.yuanchuan.user</groupId>
            <artifactId>user-commom</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.yuanchuan.merchant</groupId>
            <artifactId>merchant-api</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.yuanchuan.merchant</groupId>-->
<!--            <artifactId>merchant-api</artifactId>-->
<!--            <version>1.0.0-SNAPSHOT</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.yuanchuan.order</groupId>-->
<!--            <artifactId>order-api</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.yuanchuan.review</groupId>-->
<!--            <artifactId>review-api</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.yuanchuan.reservation</groupId>-->
<!--            <artifactId>reservation-api</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.yuanchuan.file</groupId>
            <artifactId>file-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.yuanchuan.location</groupId>
            <artifactId>location-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <!-- SpringDoc OpenAPI (替代 Springfox Swagger) -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>2.5.0</version>
        </dependency>

        <!-- Knife4j 增强 UI（适配 SpringDoc） -->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springdoc</groupId>
                    <artifactId>springdoc-openapi-core</artifactId>
                </exclusion>
            </exclusions>
            <version>4.5.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.yuanchuan.file</groupId>
                <artifactId>file-api</artifactId>
                <version>1.0.0-SNAPSHOP</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.yuanchuan.common</groupId>
                <artifactId>common</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuanchuan.authentication</groupId>
                <artifactId>authentication-api</artifactId>
                <version>${authentication.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuanchuan.user</groupId>
                <artifactId>user-api</artifactId>
                <version>${user.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yuanchuan.merchant</groupId>
                <artifactId>merchant-api</artifactId>
                <version>${merchant.version}</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>com.yuanchuan.order</groupId>-->
<!--                <artifactId>order-api</artifactId>-->
<!--                <version>${order.version}</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.yuanchuan.review</groupId>-->
<!--                <artifactId>review-api</artifactId>-->
<!--                <version>${review.version}</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.yuanchuan.reservation</groupId>-->
<!--                <artifactId>reservation-api</artifactId>-->
<!--                <version>${reservation.version}</version>-->
<!--            </dependency>-->

            <!-- https://mvnrepository.com/artifact/org.apache.curator/curator-x-discovery -->
            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-x-discovery</artifactId>
                <version>${curator.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <mainClass>com.yuanchuan.gateway.GatewayApplication</mainClass>
                    <layout>JAR</layout>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>