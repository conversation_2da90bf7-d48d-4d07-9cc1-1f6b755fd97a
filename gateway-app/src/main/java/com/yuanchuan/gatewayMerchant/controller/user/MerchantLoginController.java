package com.yuanchuan.gatewayMerchant.controller.user;

import com.alibaba.fastjson2.JSONObject;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.user.api.dto.AccountDeviceDTO;
import com.yuanchuan.user.api.dto.UserAccountUpdateDTO;
import com.yuanchuan.user.api.dto.UserResponse;
import com.yuanchuan.user.api.request.*;
import com.yuanchuan.user.api.response.ErrorResponse;
import com.yuanchuan.user.api.response.LoginResponse;
import com.yuanchuan.user.api.response.SendCodeResponse;
import com.yuanchuan.user.api.service.UserPersonService;
import com.yuanchuan.user.api.service.UserService;
import com.yuanchuan.user.api.service.VerificationService;
import com.yuanchuan.user.context.enums.RegistrationTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/merchant/api/v1")
@Tag(name = "商户认证接口", description = "包括商户注册、登录、验证码等认证相关接口")
@Slf4j
public class MerchantLoginController {

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.user}")
    private UserPersonService userPersonService;

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.user}")
    private VerificationService verificationService;

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.user}")
    private UserService userService;

    /**
     * 发送手机验证码
     *
     * @param request 包含手机号的请求对象
     * @return 验证码过期时间
     */
    @Operation(summary = "发送手机验证码", description = "向指定的台湾手机号发送6位数字验证码，用于商户注册或登录验证")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "验证码发送成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = SendCodeResponse.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误，如手机号格式不正确或发送频率受限"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/register/sendSmsCode")
    public SendCodeResponse sendSmsCode(
            @Parameter(description = "包含手机号和业务类型的请求对象", required = true)
            @Valid @RequestBody SendSmsCodeRequest request) {
        // 设置业务类型为商户注册
        Long expireTime = verificationService.sendSmsCode(request.getPhone(), request.getBusinessType());
        SendCodeResponse response = new SendCodeResponse();
        response.setExpireTime(expireTime);
        return response;
    }

    /**
     * 发送邮箱验证码
     *
     * @param request 包含邮箱的请求对象
     * @return 验证码过期时间
     */
    @Operation(summary = "发送邮箱验证码", description = "向指定的邮箱发送6位数字验证码，用于商户注册或登录验证")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "验证码发送成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = SendCodeResponse.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误，如邮箱格式不正确或发送频率受限"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/register/sendEmailCode")
    public SendCodeResponse sendEmailCode(
            @Parameter(description = "包含邮箱的请求对象", required = true)
            @Valid @RequestBody SendEmailCodeRequest request) {
        // 设置业务类型为商户注册
        Long expireTime = verificationService.sendEmailCode(request.getEmail(), request.getBusinessType());
        SendCodeResponse response = new SendCodeResponse();
        response.setExpireTime(expireTime);
        return response;
    }


    /**
     * 验证手机验证码并创建商户账户
     *
     * @param request     包含手机号和验证码的请求对象
     * @param httpRequest HTTP请求对象
     * @return 验证结果，验证通过时返回登录信息，失败时返回错误信息
     */
    @Operation(summary = "验证手机验证码并创建商户账户",
            description = "验证发送到手机的验证码并创建商户账户。\n" +
                    "- 验证失败时返回400状态码和错误信息\n" +
                    "- 验证成功时，创建商户账户并返回登录信息\n\n" +
                    "可能的错误情况：\n" +
                    "- 验证码不正确：返回400状态码和错误信息\n" +
                    "- 验证码已过期：返回400状态码和错误信息\n" +
                    "- 手机号格式不正确：返回400状态码和错误信息\n" +
                    "- 服务器内部错误：返回500状态码和错误信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "验证成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = LoginResponse.class))),
            @ApiResponse(responseCode = "400", description = "验证失败或请求参数错误",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = ErrorResponse.class))),
            @ApiResponse(responseCode = "500", description = "服务器内部错误",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = ErrorResponse.class)))
    })
    @PostMapping("/register/verifySmsCodeAndCreateMerchant")
    public LoginResponse verifySmsCodeAndCreateMerchant(
            @Parameter(description = "包含手机号和验证码的请求对象", required = true)
            @Valid @RequestBody VerifySmsCodeAndBindPhoneRequest request,
            HttpServletRequest httpRequest) {
        String clientIp = getClientIp(httpRequest);
        // 设置来源为商户登录
        request.setSource(PlatformType.MERCHANT);
        request.setRegistrationType(RegistrationTypeEnum.PHONE_REGISTRATION);
        return userPersonService.verifySmsCodeAndCreateMerchant(request, clientIp);
    }


    /**
     * 验证邮箱验证码并创建商户账户
     *
     * @param request     包含邮箱和验证码的请求对象
     * @param httpRequest HTTP请求对象
     * @return 验证结果，验证通过时返回登录信息，失败时返回错误信息
     */
    @Operation(summary = "验证邮箱验证码并创建商户账户",
            description = "验证发送到邮箱的验证码并创建商户账户。\n" +
                    "- 验证失败时返回400状态码和错误信息\n" +
                    "- 验证成功时，创建商户账户并返回登录信息\n\n" +
                    "可能的错误情况：\n" +
                    "- 验证码不正确：返回400状态码和错误信息\n" +
                    "- 验证码已过期：返回400状态码和错误信息\n" +
                    "- 邮箱格式不正确：返回400状态码和错误信息\n" +
                    "- 服务器内部错误：返回500状态码和错误信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "验证成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = LoginResponse.class))),
            @ApiResponse(responseCode = "400", description = "验证失败或请求参数错误",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = ErrorResponse.class))),
            @ApiResponse(responseCode = "500", description = "服务器内部错误",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = ErrorResponse.class)))
    })
    @PostMapping("/register/verifyEmailCodeAndCreateMerchant")
    public LoginResponse verifyEmailCodeAndCreateMerchant(
            @Parameter(description = "包含邮箱和验证码的请求对象", required = true)
            @Valid @RequestBody VerifyEmailCodeAndLoginRequest request,
            HttpServletRequest httpRequest) {
        String clientIp = getClientIp(httpRequest);
        request.setPlatform(PlatformType.MERCHANT);
        // 设置设备信息
        if (request.getDeviceInfo() == null) {
            request.setDeviceInfo(new AccountDeviceDTO());
        }
        // 设置设备来源为商户登录
        request.getDeviceInfo().setIpAddress(clientIp);
        return userPersonService.verifyEmailCodeAndCreateMerchant(request, clientIp);
    }

    /**
     * 退出登录
     */
    @Operation(summary = "退出登录")
    @PostMapping("/logout")
    public void logout() {
        userPersonService.logout();
    }

    /**
     * 获取客户端IP地址
     *
     * @param httpRequest HTTP请求对象
     * @return 客户端IP地址
     */
    private static String getClientIp(HttpServletRequest httpRequest) {
        // 获取客户端IP地址
        String clientIp = httpRequest.getHeader("X-Forwarded-For");
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = httpRequest.getHeader("Proxy-Client-IP");
        }
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = httpRequest.getHeader("WL-Proxy-Client-IP");
        }
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = httpRequest.getHeader("HTTP_CLIENT_IP");
        }
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = httpRequest.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = httpRequest.getRemoteAddr();
        }

        // 如果是多个代理，第一个IP为客户端真实IP
        if (clientIp != null && clientIp.contains(",")) {
            clientIp = clientIp.split(",")[0];
        }
        return clientIp;
    }

    /**
     * 验证邮箱验证码
     *
     * @param request 包含邮箱的请求对象
     * @return 验证码过期时间
     */
    @Operation(summary = "验证邮箱验证码", description = "向指定的邮箱发送6位数字验证码，用于注册或登录验证")
    @PostMapping("/verify/emailCode")
    public void verifyEmailCode(
            @Parameter(description = "包含邮箱的请求对象", required = true)
            @Valid @RequestBody VerifyEmailCodeRequest request) {
        boolean verified = verificationService.verifyEmailCode(request.getEmail(), request.getCode(), request.getBusinessType());
        if (!verified) {
            throw new BusinessException(UsersErrorCode.EMAIL_CODE_CHECK_ERROR.getCode(), UsersErrorCode.EMAIL_CODE_CHECK_ERROR.getMsg());
        }
    }

    /**
     * 验证手机验证码
     *
     * @param request 包含手机号的请求对象
     * @return 验证码过期时间
     */
    @Operation(summary = "验证手机验证码")
    @PostMapping("/verify/smsCode")
    public void verifySmsCode(
            @Parameter(description = "包含手机号的请求对象", required = true)
            @Valid @RequestBody VerifySmsCodeAndBindPhoneRequest request) {
        boolean verified = verificationService.verifySmsCode(request.getPhone(), request.getCode(), request.getBusinessType());
        if (!verified) {
            throw new BusinessException(UsersErrorCode.SMS_CODE_CHECK_ERROR.getCode(), UsersErrorCode.SMS_CODE_CHECK_ERROR.getMsg());
        }
    }


    @PostMapping("/phone/verify")
    @Operation(summary = "验证手机号")
    public void verifyPhone(@Parameter(description = "验证手机号请求对象", required = true)
                            @RequestBody @Valid UserAccountUpdateDTO.PhoneVerifyRequest request) {
        log.info("UserController#verifyPhone: {}", JSONObject.toJSONString(request));
        userService.verifyPhone(request);
    }


    @PostMapping("/phone/update")
    @Operation(summary = "换绑手机号")
    public void updatePhone(@Parameter(description = "换绑手机号请求对象", required = true)
                            @RequestBody @Valid UserAccountUpdateDTO.PhoneUpdateRequest request) {
        log.info("UserController#updatePhone: {}", JSONObject.toJSONString(request));
        userService.updatePhone(request);
    }

    @PostMapping("/email/bind")
    @Operation(summary = "绑定邮箱")
    public void bindEmail(@Parameter(description = "绑定邮箱请求对象", required = true)
                          @RequestBody @Valid UserAccountUpdateDTO.EmailBindRequest request) {
        log.info("UserController#bindEmail: {}", JSONObject.toJSONString(request));
        userService.bindEmail(request);
    }

    @PostMapping("/email/verify")
    @Operation(summary = "验证原邮箱")
    public void verifyOriginalEmail(@Parameter(description = "绑定邮箱请求对象", required = true)
                                    @RequestBody @Valid UserAccountUpdateDTO.EmailVerifyRequest request) {
        log.info("UserController#verifyOriginalEmail: {}", JSONObject.toJSONString(request));
        userService.verifyOriginalEmail(request);
    }

    @GetMapping("/profile")
    @Operation(summary = "获取用户个人信息")
    public UserResponse getUserProfile() {
        return userService.getUserProfile();
    }
}
