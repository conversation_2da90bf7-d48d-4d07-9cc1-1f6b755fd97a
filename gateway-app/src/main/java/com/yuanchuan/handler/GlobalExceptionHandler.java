//package com.yuanchuan.handler;
//
//import com.yuanchuan.common.exception.BusinessException;
//import com.yuanchuan.common.exception.InvalidOperationException;
//import com.yuanchuan.common.response.Result;
//import org.springframework.http.HttpStatus;
//import org.springframework.web.bind.annotation.ExceptionHandler;
//import org.springframework.web.bind.annotation.ResponseStatus;
//import org.springframework.web.bind.annotation.RestControllerAdvice;
//
//import lombok.extern.slf4j.Slf4j;
//
///**
// * 商户端全局异常处理器
// */
//@Slf4j
//@RestControllerAdvice(basePackages = {
//    "com.yuanchuan.gatewayMerchant.controller",
//    "com.yuanchuan.gatewayAdmin.controller",
//    "com.yuanchuan.gateway.controller"})
//public class GlobalExceptionHandler {
//
//    /**
//     * 处理业务异常
//     */
//    @ExceptionHandler(InvalidOperationException.class)
//    @ResponseStatus(HttpStatus.OK)
//    public Result<?> handleBusinessException(InvalidOperationException e) {
//        log.error("操作异常: {}", e.getMessage(), e);
//        return Result.error(e.getErrorCode(), e.getMessage());
//    }
//
//    /**
//     * 处理业务异常
//     */
//    @ExceptionHandler(BusinessException.class)
//    @ResponseStatus(HttpStatus.OK)
//    public Result<?> handleBusinessException(BusinessException e) {
//        log.error("业务异常: {}", e.getMessage(), e);
//        return Result.error(e.getErrorCode(), e.getMessage());
//    }
//
//    /**
//     * 处理其他未预期的异常
//     */
//    @ExceptionHandler(Exception.class)
//    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
//    public Result<?> handleException(Exception e) {
//        log.error("系统异常: {}", e.getMessage(), e);
//        return Result.error(e.getMessage());
//    }
//}