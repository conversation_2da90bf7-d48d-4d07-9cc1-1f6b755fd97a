package com.yuanchuan.gatewayAdmin.controller;

import com.yuanchuan.merchant.management.api.dto.MerchantShopApplicationDTO;
import com.yuanchuan.merchant.management.api.request.ApproveMerchantApplicationRequest;
import com.yuanchuan.merchant.management.api.request.GetMerchantApplicationDetailRequest;
import com.yuanchuan.merchant.management.api.request.PageMerchantApplicationsRequest;
import com.yuanchuan.merchant.management.api.request.RejectMerchantApplicationRequest;
import com.yuanchuan.merchant.management.api.response.ApproveMerchantApplicationResponse;
import com.yuanchuan.merchant.management.api.response.PageMerchantApplicationsResponse;
import com.yuanchuan.merchant.management.api.response.RejectMerchantApplicationResponse;
import com.yuanchuan.merchant.management.api.service.MerchantApplicationService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin-api/merchant/application")
public class AMerchantApplicationController {
    //@DubboReference(version = "1.0", group = "${dubbo.consumer.group.merchant}", retries = -1, timeout = 600000)
    private MerchantApplicationService merchantApplicationService;


    @PostMapping("/list")
    public PageMerchantApplicationsResponse getMerchantApplicationList(@RequestBody PageMerchantApplicationsRequest request) {
        return merchantApplicationService.pageMerchantApplications(request);
    }

    @GetMapping("/detail")
    public MerchantShopApplicationDTO getMerchantApplicationDetail(@RequestParam Long applicationId) {
        GetMerchantApplicationDetailRequest request = new GetMerchantApplicationDetailRequest();
        request.setApplicationId(applicationId);
        return merchantApplicationService.getMerchantShopApplicationDetail(request);
    }

    @PostMapping("/approve")
    public ApproveMerchantApplicationResponse approveApplication(@RequestBody ApproveMerchantApplicationRequest request) {

        return merchantApplicationService.approveMerchantApplication(request);
    }

    @PostMapping("/reject")
    public RejectMerchantApplicationResponse rejectApplication(@RequestBody RejectMerchantApplicationRequest request) {

        return merchantApplicationService.rejectMerchantApplication(request);
    }

//    @GetMapping
//    public

//    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.shop}", retries = -1, timeout = 600000)
//    private GroupMealService groupMealService;
//
//    // @DubboReference(version = "1.0", group = "${dubbo.consumer.group.shop}", retries = -1, timeout = 600000)
//    // private RecommendedDishService recommendedDishService;
//
//    // 创建店铺
//    @PostMapping
//    public Long createShop(@RequestBody ShopDTO shopDTO) {
//        //ShopDTO shopDTO = ShopMapper.INSTANCE.toDTO(request);
//        return shopService.createShop(shopDTO);
//    }
//
//    // 更新店铺信息
//    @PutMapping("/{shopId}")
//    public void updateShop(@RequestBody ShopDTO shopDTO) {
//        //ShopDTO shopDTO = ShopMapper.INSTANCE.toDTO(request);
//        //shopDTO.setId(shopId);
//        shopService.updateShop(shopDTO);
//    }
//
//    // 停用店铺
//    @PostMapping("/{shopId}/deactivate")
//    public void deactivateShop(@PathVariable Long shopId) {
//        shopService.updateShopStatus(shopId, "");
//    }
//
//    // 启用店铺
//    @PostMapping("/{shopId}/activate")
//    public void activateShop(@PathVariable Long shopId) {
//        shopService.updateShopStatus(shopId, "");
//    }
//
//    // 查询店铺详情
//    @GetMapping("/{shopId}/detail")
//    public ShopDetailVO getShopDetail(@PathVariable Long shopId) {
//        return shopService.getShopDetail(shopId);
//    }
//
//    // 查询店铺套餐列表 todo move to ShopDealController
//    @GetMapping("/{shopId}/goup-meal")
//    public List<GroupMealDTO> getShopGroupMeal(@PathVariable Long shopId) {
//        return groupMealService.getGroupMealByShopId(shopId);
//    }
//
//    // 查询店铺推荐菜列表
//    @GetMapping("/{shopId}/recommended-dishes")
//    public List<DishDTO> getShopRecommendedDishes(@PathVariable Long shopId) {
//        return shopService.listDishes(shopId, 0, 50);
//    }
//
//    // 查询所有店铺
//    @GetMapping
//    public List<ShopListVO> getAllShops(
//            @RequestParam(required = false) BigDecimal longitude,
//            @RequestParam(required = false) BigDecimal latitude,
//            @RequestParam(required = false) Double radius,
//            @RequestParam(defaultValue = "1") Integer pageNum,
//            @RequestParam(defaultValue = "10") Integer pageSize) {
//
//        longitude = new BigDecimal("121.5672");
//        latitude = new BigDecimal("25.0478");
//
//        return shopService.searchNearbyShops(
//            longitude,
//            latitude,
//            radius,
//            pageNum,
//            pageSize
//        );
//    }
//
//    // // 根据状态查询店铺列表
//    // @GetMapping("/status/{status}")
//    // public List<ShopDTO> getShopsByStatus(@PathVariable String status) {
//    //     return shopService.getShopsByStatus(status);
//    // }
//
//    // 推荐餐厅 demo用
//    @GetMapping("/recommoned")
//    public List<ShopListVO> getRecommoned() {
//        return shopService.getRecommoned();
//    }
}
