package com.yuanchuan.gatewayAdmin.controller;//package com.yuanchuan.user.api.controller;

import com.yuanchuan.common.response.PageResult;
import com.yuanchuan.common.response.Result;
import com.yuanchuan.user.api.dto.RoleDTO;
import com.yuanchuan.user.api.dto.RoleSelectDTO;
import com.yuanchuan.user.api.request.RoleCreateRequest;
import com.yuanchuan.user.api.request.RoleQueryRequest;
import com.yuanchuan.user.api.request.RoleUpdateRequest;
import com.yuanchuan.user.api.service.RoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 角色管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/roles")
@Tag(name = "运营权限管理", description = "角色管理相关接口")
public class ARoleController {

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.user}")
    private RoleService roleService;

    @PostMapping("/list")
    @Operation(summary = "分页查询角色列表", description = "根据条件分页查询角色列表")
    public PageResult<RoleDTO> queryRoles(@Parameter(description = "分页查询角色列表", required = true)
                                                      @RequestBody @Valid RoleQueryRequest request) {
        return roleService.queryRoles(request);
    }

    @GetMapping("/select")
    @Operation(summary = "获取角色下拉列表", description = "获取所有有效角色的下拉选项")
    public List<RoleSelectDTO> getRoleSelectList() {
        return roleService.getRoleSelectList();
    }

    @PostMapping("/createRole")
    @Operation(summary = "创建角色", description = "创建新的角色")
    public void createRole(@Parameter(description = "创建新的角色", required = true)
                                       @RequestBody @Valid RoleCreateRequest request) {
        roleService.createRole(request);
    }

    @PostMapping("/updateRole")
    @Operation(summary = "更新角色", description = "更新角色信息")
    public void updateRole(@Parameter(description = "更新角色信息", required = true)
                                          @RequestBody @Valid RoleUpdateRequest request) {
        roleService.updateRole(request);
    }

    @GetMapping("/getRoleById/{id}")
    @Operation(summary = "根据ID查询角色详情", description = "查询指定ID的角色详细信息")
    public RoleDTO getRoleById(
            @Parameter(description = "角色ID") @PathVariable Long id) {
        return roleService.getRoleById(id);
    }

    @DeleteMapping("/deleteRole/{id}")
    @Operation(summary = "删除角色", description = "删除指定ID的角色")
    public void deleteRole(
            @Parameter(description = "角色ID") @PathVariable Long id) {
        roleService.deleteRole(id);
    }

    @GetMapping("/user/{accountId}")
    @Operation(summary = "根据账户ID查询用户角色", description = "查询指定账户的角色列表")
    public List<RoleDTO> getUserRoles(
            @Parameter(description = "账户ID") @PathVariable Long accountId) {
        return  roleService.getUserRoles(accountId);
    }
}

