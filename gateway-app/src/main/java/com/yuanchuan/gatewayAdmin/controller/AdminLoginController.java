package com.yuanchuan.gatewayAdmin.controller;

import com.yuanchuan.authentication.api.service.AuthenticationService;
import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.common.response.Result;
import com.yuanchuan.user.api.dto.AccountDeviceDTO;
import com.yuanchuan.user.api.request.PasswordLoginRequest;
import com.yuanchuan.user.api.response.LoginResponse;
import com.yuanchuan.user.api.service.UserPersonService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 运营后台登录控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/20 10:38
 */
@RestController
@RequestMapping("/admin/api/v1")
@Tag(name = "运营认证接口", description = "登录")
@Slf4j
public class AdminLoginController {

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.user}")
    private UserPersonService userPersonService;

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.authentication}")
    private AuthenticationService authenticationService;

    /**
     * 账号密码登录
     *
     * @param request     包含账号和密码的请求对象
     * @param httpRequest HTTP请求对象
     * @return 登录结果，登录成功时返回登录信息，失败时返回错误信息
     */
    @Operation(summary = "账号密码登录", description = "使用账号（手机号或邮箱）和密码进行登录。\n" +
            "- 登录失败时返回400状态码和错误信息\n" +
            "- 登录成功时，返回登录信息和Token\n\n" +
            "可能的错误情况：\n" +
            "- 账号不存在：返回400状态码和错误信息\n" +
            "- 密码错误：返回400状态码和错误信息\n" +
            "- 账号被禁用：返回400状态码和错误信息\n" +
            "- 服务器内部错误：返回500状态码和错误信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "登录成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = LoginResponse.class))),
            @ApiResponse(responseCode = "400", description = "登录失败或请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/login")
    public LoginResponse login(
            @Parameter(description = "包含账号和密码的请求对象", required = true)
            @Valid @RequestBody PasswordLoginRequest request,
            HttpServletRequest httpRequest) {
        log.info("管理员账号密码登录请求: {}", request.getAccount());
        String clientIp = getClientIp(httpRequest);

        // 设置平台类型为管理员平台
        request.setPlatform(PlatformType.ADMIN);

        // 设置设备信息
        if (request.getDeviceInfo() == null) {
            request.setDeviceInfo(new AccountDeviceDTO());
        }
        request.getDeviceInfo().setIpAddress(clientIp);

        // 调用用户服务进行登录验证
        return userPersonService.passwordLogin(request, clientIp);
    }

    /**
     * 退出登录
     */
    @Operation(summary = "退出登录")
    @PostMapping("/logout")
    public void logout() {
        userPersonService.logout();
    }

    /**
     * 获取客户端IP地址
     *
     * @param httpRequest HTTP请求对象
     * @return 客户端IP地址
     */
    private static String getClientIp(HttpServletRequest httpRequest) {
        // 获取客户端IP地址
        String clientIp = httpRequest.getHeader("X-Forwarded-For");
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = httpRequest.getHeader("Proxy-Client-IP");
        }
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = httpRequest.getHeader("WL-Proxy-Client-IP");
        }
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = httpRequest.getHeader("HTTP_CLIENT_IP");
        }
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = httpRequest.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = httpRequest.getRemoteAddr();
        }

        // 如果是多个代理，第一个IP为客户端真实IP
        if (clientIp != null && clientIp.contains(",")) {
            clientIp = clientIp.split(",")[0];
        }
        return clientIp;
    }
}
