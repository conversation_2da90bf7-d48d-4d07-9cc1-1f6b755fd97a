package com.yuanchuan.gatewayAdmin.controller;


import com.yuanchuan.merchant.management.api.dto.CategoryDTO;
import com.yuanchuan.merchant.management.api.service.CategoryService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/admin-api/merchant/category")
public class ACategoryController {

    //@DubboReference(version = "1.0", group = "${dubbo.consumer.group.merchant}", retries = -1, timeout = 600000)
    private CategoryService categoryService;

    @GetMapping("/tree")
    public List<CategoryDTO> getCategoryTree() {
        return categoryService.getCategoryTree();
    }
}
