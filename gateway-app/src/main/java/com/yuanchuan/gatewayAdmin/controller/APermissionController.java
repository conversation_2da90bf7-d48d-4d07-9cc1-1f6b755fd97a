package com.yuanchuan.gatewayAdmin.controller;

import com.yuanchuan.common.response.PageResult;
import com.yuanchuan.common.response.Result;
import com.yuanchuan.user.api.dto.PermissionDTO;
import com.yuanchuan.user.api.dto.PermissionSelectDTO;
import com.yuanchuan.user.api.dto.PermissionTreeDTO;
import com.yuanchuan.user.api.request.PermissionCreateRequest;
import com.yuanchuan.user.api.request.PermissionQueryRequest;
import com.yuanchuan.user.api.request.PermissionUpdateRequest;
import com.yuanchuan.user.api.service.PermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 权限管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/permissions")
@Tag(name = "运营权限管理", description = "权限管理相关接口")
public class APermissionController {

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.user}")
    private PermissionService permissionService;

    @PostMapping("/user/{accountId}")
    @Operation(summary = "根据账户ID查询用户权限树", description = "查询指定账户的权限树结构")
    public List<PermissionTreeDTO> getUserPermissionTree(
            @Parameter(description = "账户ID") @PathVariable Long accountId) {
        return permissionService.getUserPermissionTree(accountId);
    }

    @PostMapping("/list")
    @Operation(summary = "分页查询权限列表", description = "根据条件分页查询权限列表")
    public PageResult<PermissionDTO> queryPermissions(@Parameter(description = "分页查询权限列表", required = true)
                                                                  @RequestBody @Valid PermissionQueryRequest request) {
        return permissionService.queryPermissions(request);
    }

    @GetMapping("/entries")
    @Operation(summary = "获取权限入口下拉列表", description = "获取类型为MENU的权限作为入口选项")
    public List<PermissionSelectDTO> getPermissionEntries() {
        return permissionService.getPermissionEntries();
    }

    @PostMapping("/createPermission")
    @Operation(summary = "创建权限", description = "创建新的权限")
    public void createPermission(@Parameter(description = "创建权限", required = true)
                                             @RequestBody @Valid  PermissionCreateRequest request) {
        permissionService.createPermission(request);
    }

    @PostMapping("/updatePermission")
    @Operation(summary = "更新权限", description = "更新权限信息")
    public void updatePermission(@Parameter(description = "更新权限", required = true)
                                                @RequestBody @Valid  PermissionUpdateRequest request) {
        permissionService.updatePermission(request);

    }

    @GetMapping("/getPermissionById/{id}")
    @Operation(summary = "根据ID查询权限详情", description = "查询指定ID的权限详细信息")
    public PermissionDTO getPermissionById(
            @Parameter(description = "权限ID") @PathVariable Long id) {
        return permissionService.getPermissionById(id);
    }

    @DeleteMapping("/deletePermission/{id}")
    @Operation(summary = "删除权限", description = "删除指定ID的权限")
    public void deletePermission(
            @Parameter(description = "权限ID") @PathVariable Long id) {
        permissionService.deletePermission(id);
    }
}
