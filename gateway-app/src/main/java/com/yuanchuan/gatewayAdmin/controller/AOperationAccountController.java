package com.yuanchuan.gatewayAdmin.controller;

import com.yuanchuan.common.response.PageResult;
import com.yuanchuan.user.api.dto.OperationAccountDTO;
import com.yuanchuan.user.api.request.OperationAccountCreateRequest;
import com.yuanchuan.user.api.request.OperationAccountQueryRequest;
import com.yuanchuan.user.api.request.OperationAccountUpdateRequest;
import com.yuanchuan.user.api.service.OperationAccountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

/**
 * 运营账号管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/operation/accounts")
@Tag(name = "运营账号管理", description = "运营账号管理相关接口")
public class AOperationAccountController {

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.user}")
    private OperationAccountService operationAccountService;

    @GetMapping("/list")
    @Operation(summary = "分页查询运营账号列表", description = "根据条件分页查询运营账号列表")
    public PageResult<OperationAccountDTO> queryOperationAccounts(@Parameter(description = "分页查询角色列表", required = true)
                                                                      @RequestBody @Valid OperationAccountQueryRequest request) {
        return operationAccountService.queryOperationAccounts(request);
    }

    @PostMapping("/createOperationAccount")
    @Operation(summary = "新增运营账号", description = "创建新的运营账号")
    public void createOperationAccount(@Parameter(description = "分页查询角色列表", required = true)
                                           @RequestBody @Valid OperationAccountCreateRequest request) {
        operationAccountService.createOperationAccount(request);
    }

    @PostMapping("/updateOperationAccount")
    @Operation(summary = "修改运营账号", description = "修改运营账号信息")
    public void updateOperationAccount(@Parameter(description = "分页查询角色列表", required = true)
                                           @RequestBody @Valid OperationAccountUpdateRequest request) {
        operationAccountService.updateOperationAccount(request);
    }

    @GetMapping("/getOperationAccountById/{id}")
    @Operation(summary = "查询运营账号详情", description = "根据ID查询运营账号详情")
    public OperationAccountDTO getOperationAccountById(
            @Parameter(description = "账号ID", required = true) @PathVariable Long id) {
        return operationAccountService.getOperationAccountById(id);
    }
}
