package com.yuanchuan.gateway.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenApiConfig {
    private static final String BEARER_SCHEME_NAME = "Bearer";
    private static final String AUTHORIZATION_SCHEME_NAME = "Authorization";

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(apiInfo())
                // 添加安全方案要求
                .addSecurityItem(new SecurityRequirement().addList(BEARER_SCHEME_NAME))
                .addSecurityItem(new SecurityRequirement().addList(AUTHORIZATION_SCHEME_NAME))
                // 定义Bearer安全方案
                .schemaRequirement(BEARER_SCHEME_NAME,
                        new SecurityScheme()
                                .name(BEARER_SCHEME_NAME)
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("bearer")
                                .bearerFormat("JWT"))
                // 定义Authorization安全方案
                .schemaRequirement(AUTHORIZATION_SCHEME_NAME,
                        new SecurityScheme()
                                .name(AUTHORIZATION_SCHEME_NAME)
                                .type(SecurityScheme.Type.APIKEY)
                                .in(SecurityScheme.In.HEADER)
                                .description("Authorization header"));
    }

    private Info apiInfo() {
        return new Info()
                .title("Gateway app C端API")
                .version("1.0")
                .description("Spring Boot 3 + Knife4j + OpenAPI 文档")
                .contact(new Contact()
                        .name("开发团队")
                        .email("<EMAIL>")
                        .url("https://example.com"))
                .license(new License()
                        .name("Apache 2.0")
                        .url("https://www.apache.org/licenses/LICENSE-2.0.html"));
    }
}