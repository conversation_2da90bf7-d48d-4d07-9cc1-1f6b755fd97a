package com.yuanchuan.gateway.config;

import com.yuanchuan.gateway.filter.AuthenticationFilter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;

@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
@Slf4j
public class SecurityConfig {


    @Autowired
    private CorsConfig corsConfig;

    // @Value("${dubbo.consumer.group.reservation}")
    // private String reservation;

    @Autowired
    private AuthenticationFilter authenticationFilter; // 关键改动

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {

        log.info("SecurityConfig securityFilterChain:");
        http
                .cors(cors -> cors.configurationSource(request -> {
                    CorsConfiguration configuration = new CorsConfiguration();
                    configuration.addAllowedOriginPattern("*");
                    configuration.addAllowedHeader("*");
                    configuration.addAllowedMethod("*");
                    configuration.setAllowCredentials(true);
                    configuration.addExposedHeader("*");
                    configuration.setMaxAge(3600L);
                    return configuration;
                }))
                .csrf(csrf -> csrf.disable())
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .authorizeHttpRequests(auth -> auth
                        .requestMatchers(
                                "merchant-api/**",
                                "merchant-api/merchant/application",
                                "merchant-api/merchant/application/**",
                                "admin-api/**",
                                "/api/**",
                                "/api/file_record/**",
                                "/api/location/**",
                                "/api/v1/register",
                                "/api/v1/register/**",
                                "/api/v1/login/**",
                                "/api/v1/verify/*",
                                "/api/v1/contentConfigs/*",
                                "/api/v1/*/callback",
                                "/api/v1/login/*",
                                "/api/v1/auth/token/refresh",  // 添加token刷新接口
                                "/merchant/api/v1/**",
                                "/admin/api/v1/**",
                                "/api/v1/reservations/**",
                                "/api/v1/reviews/**",
                                "/api/v1/reservation-rules/**",
                                "/api/v1/table-types/**",
                                "/api/location/**",
                                "/api/merchant/**",
                                "/api/merchant/shop/**",
                                "/api/merchant/deals/**",
                                "/api/orders/**",
                                // Knife4j和Swagger相关路径 - 完整放行
                                "/doc.html",
                                "/doc.html/**",
                                "/v3/api-docs",
                                "/v3/api-docs/**",
                                "/v3/api-docs.yaml",
                                "/v3/api-docs/swagger-config",
                                "/swagger-ui.html",
                                "/swagger-ui/**",
                                "/swagger-ui/index.html",
                                "/swagger-resources",
                                "/swagger-resources/**",
                                "/webjars",
                                "/webjars/**",
                                "/knife4j",
                                "/knife4j/**",
                                "/favicon.ico",
                                // 静态资源完整放行
                                "/css",
                                "/css/**",
                                "/js",
                                "/js/**",
                                "/images",
                                "/images/**",
                                "/fonts",
                                "/fonts/**",
                                "/static",
                                "/static/**",
                                "/health",
                                "/info",
                                "/api/v1/user/**","/pages/redirect/**","/api/v1/**","/api/v1/contentConfigs/html",
                                "/api/permissions/**","/api/roles/**"
                        ).permitAll()
                        .anyRequest().authenticated()
                )
                .addFilterBefore(authenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}