package com.yuanchuan.gateway.filter;

import com.alibaba.fastjson2.JSONObject;
import com.yuanchuan.authentication.api.dto.TokenDTO;
import com.yuanchuan.authentication.api.service.AuthenticationService;
import com.yuanchuan.common.context.UserContext;
import com.yuanchuan.common.enums.users.login.PlatformType;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class AuthenticationFilter extends OncePerRequestFilter {

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.authentication}")
    private AuthenticationService authenticationService;


    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        String path = request.getRequestURI();
        log.info("Request Path: {}", path);
         //白名单路径，不做 token 校验
        if (isWhiteList(path)) {
            log.info("白名单路径，直接放行: {}", path);
            filterChain.doFilter(request, response);
            return;
        }

        String authHeader = request.getHeader("Authorization");
        // 获取请求头中的 "platform" 字段
        String platform = request.getHeader("platform");
        String deviceid = request.getHeader("deviceid");

        log.info("authHeader1: {}", authHeader);
        log.info("AuthenticationFilter: doFilterInternal");
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            log.warn("路径 {} 被拦截：未提供token或token格式错误", path);
            response.setCharacterEncoding("UTF-8");
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json");
            response.getWriter().write("{\"error\":\"未提供 token 或 token 格式錯誤\"}");
            return;
        }

        // 获取平台类型
        String platformCode = request.getHeader("platform");
        PlatformType platformType = null;

        if (StringUtils.isNotEmpty(platformCode)) {
            platformType = PlatformType.getByCode(platformCode);
            if (platformType == null) {
                log.warn("无效的平台类型: {}", platformCode);
                response.setCharacterEncoding("UTF-8");
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.setContentType("application/json");
                response.getWriter().write("{\"error\":\"無效的平台類型\"}");
                return;
            }
        } else {
            log.error("未提供平台类型");
            response.setCharacterEncoding("UTF-8");
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            response.setContentType("application/json");
            response.getWriter().write("{\"error\":\"必須提供平台類型\"}");
            return;
        }

        try {
            String token = authHeader.substring(7);
            TokenDTO tokenDTO = authenticationService.validateToken(token, platformType.getCode());
            if (tokenDTO == null) {
                log.error("无效的token");
                response.setCharacterEncoding("UTF-8");
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.setContentType("application/json");
                response.getWriter().write("{\"error\":\"無效的token\"}");
                return;
            }


            // 解析token 得到userContext
            UserContext userContext = authenticationService.parseTokenToUserContext(token);
            if(userContext == null){
                log.error("userContextis null ,无效的token");
                response.setCharacterEncoding("UTF-8");
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.setContentType("application/json");
                response.getWriter().write("{\"error\":\"無效的token\"}");
                return;
            }
            HttpServletRequest requestWrapper = new HttpServletRequestWrapper(request) {
                @Override
                public String getHeader(String name) {
                    if ("X-User-Content".equalsIgnoreCase(name)) {
                        return Base64.getEncoder().encodeToString(
                                JSONObject.toJSONString(userContext).getBytes(StandardCharsets.UTF_8)
                        );
                    }

                    if ("X-Plat-Form".equalsIgnoreCase(name)) {
                        return platform;
                    }
                    if ("X-Device-Id".equalsIgnoreCase(name)) {
                        return deviceid;
                    }

                    return super.getHeader(name);
                }
            };


            filterChain.doFilter(requestWrapper, response);
        } catch (Exception e) {
            log.error("无效的token:{}",e);
            response.setCharacterEncoding("UTF-8");
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json");
            response.getWriter().write("{\"error\":\"無效的token\"}");
            return;
        }
    }

    private boolean isWhiteList(String path) {
        return path.matches("^/api/v1/login/.*$")
                || path.matches("^/api/v1/.*/callback$")
                || path.matches("^/api/v1/register.*$")
                || path.matches("^/api/v1/verify.*$")
                || path.matches("^/api/v1/contentConfigs.*$")
                || path.matches("^/api/v1/auth/token/refresh$")  // 添加token刷新接口到白名单
                // || path.matches("^/merchant/api/v1.*$")
                || path.matches("^/admin/api/v1.*$")
                || path.startsWith("/api/location/")  // 添加location API到白名单
                || path.startsWith("/v3/api-docs")
                || path.startsWith("/merchant/api/v1/")  // 商户API路径
                || path.startsWith("/swagger-ui/")
                || path.startsWith("/api/v1/verify")
                || path.startsWith("/merchant-api")
                || path.startsWith("/admin-api")
                || path.startsWith("/api/location")
                || path.startsWith("/api/file_record")
                || path.startsWith("/api/v1/contentConfigs")
                || path.equals("/swagger-ui.html")
                || path.equals("/doc.html")
                || path.startsWith("/doc.html")  // 支持doc.html的所有子路径
                || path.equals("/favicon.ico")
                || path.endsWith("/favicon.ico")  // 支持任意路径下的favicon
                || path.startsWith("/webjars/")
                || path.startsWith("/swagger-resources/")
                || path.startsWith("/knife4j/")  // 添加knife4j路径
                || path.startsWith("/css/")
                || path.startsWith("/js/")
                || path.startsWith("/images/")
                || path.startsWith("/fonts/")
                || path.startsWith("/static/")
                || path.equals("/health")
                || path.equals("/info")
                || path.equals("/api/v1/contentConfigs/html")
                || path.startsWith("/actuator/");  // 添加actuator端点到白名单
    }


    private Collection<? extends GrantedAuthority> getAuthorities(List<String> roles) {
        return roles.stream().map(SimpleGrantedAuthority::new).collect(Collectors.toList());
    }
}