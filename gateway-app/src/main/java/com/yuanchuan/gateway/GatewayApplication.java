package com.yuanchuan.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;

/**
 * Apache ShenYu Gateway Application
 */
// "com.yuanchuan.common.config.CorsConfig",
@ComponentScan({
    "com.yuanchuan.common.utils",
    "com.yuanchuan.common.response",
    "com.yuanchuan.gateway.*",
    "com.yuanchuan.handler",
    "com.yuanchuan.gatewayAdmin.*",
    "com.yuanchuan.gatewayMerchant.*"
    })
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, RedisAutoConfiguration.class})
public class GatewayApplication {

    public static void main(String[] args) {
        SpringApplication.run(GatewayApplication.class, args);
    }
}