package com.yuanchuan.gateway.controller.user;

import com.yuanchuan.common.enums.users.UsersErrorCode;
import com.yuanchuan.common.enums.users.login.PlatformType;
import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.common.response.Result;
import com.yuanchuan.user.api.dto.AuthCallbackRequest;
import com.yuanchuan.user.api.request.*;
import com.yuanchuan.user.api.response.ErrorResponse;
import com.yuanchuan.user.api.response.SendCodeResponse;
import com.yuanchuan.user.api.service.UserPersonService;
import com.yuanchuan.user.api.service.VerificationService;
import com.yuanchuan.user.context.enums.RegisterSourceEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.PrintWriter;

@RestController
@RequestMapping("/api/v1")
@Tag(name = "c端认证接口", description = "包括注册、登录、验证码等认证相关接口")
@Slf4j
public class LoginController {

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.user}")
    private UserPersonService userPersonService;

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.user}")
    private VerificationService verificationService;

    @Value("${authentication.callbackDomain}")
    private String authCallbackUrl;


    /**
     * 统一登录接口
     *
     * @param request     统一登录请求对象
     * @param httpRequest HTTP请求对象
     * @return 登录结果，成功时返回登录信息，失败时返回错误信息
     */
    @Operation(summary = "统一登录接口",
            description = "统一处理各种登录方式和多步骤登录流程\n" +
                    "- 支持短信验证码登录、邮箱验证码登录、账号密码登录、第三方登录\n" +
                    "- 支持邮箱注册绑定手机号、第三方登录绑定手机号等多步骤流程\n" +
                    "- 根据loginType和flowStatus确定当前处于哪个流程阶段\n" +
                    "- 成功时返回登录信息或下一步操作提示\n" +
                    "- 失败时返回错误信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "操作成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = UnifiedLoginResponse.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误或操作失败",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = ErrorResponse.class))),
            @ApiResponse(responseCode = "500", description = "服务器内部错误",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = ErrorResponse.class)))
    })
    @PostMapping("/register/unifiedLogin")
    public UnifiedLoginResponse unifiedLogin(
            @Parameter(description = "统一登录请求对象", required = true)
            @Valid @RequestBody UnifiedLoginRequest request,
            HttpServletRequest httpRequest) {
        this.requestHeardContext(request, httpRequest);
        return userPersonService.unifiedLogin(request);
    }


    /**
     * 发送手机注册验证码
     *
     * @param request 包含手机号的请求对象
     * @return 验证码过期时间
     */
    @Operation(summary = "发送手机验证码", description = "向指定的台湾手机号发送6位数字验证码，用于注册或登录验证")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "验证码发送成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = SendCodeResponse.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误，如手机号格式不正确或发送频率受限"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/register/sendSmsCode")
    public SendCodeResponse sendSmsCode(
            @Parameter(description = "包含手机号的请求对象", required = true)
            @Valid @RequestBody SendSmsCodeRequest request) {
        Long expireTime = verificationService.sendSmsCode(request.getPhone(), request.getBusinessType());
        SendCodeResponse response = new SendCodeResponse();
        response.setExpireTime(expireTime);
        return response;
    }


    /**
     * 验证手机验证码
     *
     * @param request 包含手机号的请求对象
     * @return 验证码过期时间
     */
    @Operation(summary = "验证手机验证码")
    @PostMapping("/verify/smsCode")
    public void verifySmsCode(
            @Parameter(description = "包含手机号的请求对象", required = true)
            @Valid @RequestBody VerifySmsCodeAndBindPhoneRequest request) {
        boolean verified = verificationService.verifySmsCode(request.getPhone(), request.getCode(), request.getBusinessType());
        if (!verified) {
            throw new BusinessException(UsersErrorCode.SMS_CODE_CHECK_ERROR.getCode(), UsersErrorCode.SMS_CODE_CHECK_ERROR.getMsg());
        }
    }


    /**
     * 发送邮箱验证码
     *
     * @param request 包含邮箱的请求对象
     * @return 验证码过期时间
     */
    @Operation(summary = "发送邮箱验证码", description = "向指定的邮箱发送6位数字验证码，用于注册或登录验证")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "验证码发送成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = SendCodeResponse.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误，如邮箱格式不正确或发送频率受限"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/register/sendEmailCode")
    public SendCodeResponse sendEmailCode(
            @Parameter(description = "包含邮箱的请求对象", required = true)
            @Valid @RequestBody SendEmailCodeRequest request) {
        Long expireTime = verificationService.sendEmailCode(request.getEmail(), request.getBusinessType());
        SendCodeResponse response = new SendCodeResponse();
        response.setExpireTime(expireTime);
        return response;
    }

    /**
     * 验证邮箱验证码
     *
     * @param request 包含邮箱的请求对象
     * @return 验证码过期时间
     */
    @Operation(summary = "验证邮箱验证码", description = "向指定的邮箱发送6位数字验证码，用于注册或登录验证")
    @PostMapping("/verify/emailCode")
    public void verifyEmailCode(
            @Parameter(description = "包含邮箱的请求对象", required = true)
            @Valid @RequestBody VerifyEmailCodeRequest request) {
        boolean verified = verificationService.verifyEmailCode(request.getEmail(), request.getCode(), request.getBusinessType());
        if (!verified) {
            throw new BusinessException(UsersErrorCode.EMAIL_CODE_CHECK_ERROR.getCode(), UsersErrorCode.EMAIL_CODE_CHECK_ERROR.getMsg());
        }
    }

    @Operation(summary = "三方登录")
    @GetMapping("/login/{type}")
    public void login(@PathVariable String type, HttpServletRequest request, HttpServletResponse response) {
        try {
            log.info("login type:{}", type);
            String callbackUrl = userPersonService.login(type);
            log.info("callbackUrl:{}", callbackUrl);

            String refere = request.getHeader("Referer");
            log.info("referer:{}", refere);

            response.setHeader("Referer", refere);
            response.sendRedirect(callbackUrl);
        } catch (Exception e) {
            writeJsonError(response, UsersErrorCode.USER_AUTHORIZATION_ERROR.getCode(),
                    UsersErrorCode.USER_AUTHORIZATION_ERROR.getMsg());
        }
    }

    @Operation(summary = "三方登录回调")
    @RequestMapping("/{type}/callback")
    public void loginCallback(@PathVariable String type, AuthCallbackRequest callback, HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            log.info("loginCallback type:{}", type);
            String redirectUrl = userPersonService.loginCallback(type, callback);
            log.info("redirectUrl:{}", redirectUrl);

            String refere = request.getHeader("Referer");
            log.info("referer:{}", refere);

            if (refere == null) {
                refere = authCallbackUrl;
            }

            response.sendRedirect(refere + redirectUrl);
        } catch (Exception e) {
            writeJsonError(response, UsersErrorCode.USER_AUTHORIZATION_ERROR.getCode(),
                    UsersErrorCode.USER_AUTHORIZATION_ERROR.getMsg());
        }
    }

    @Operation(summary = "退出登录")
    @PostMapping("/logout")
    public void logout() {
        userPersonService.logout();
    }

    @Operation(summary = "注销用户")
    @PostMapping("/deleteUser")
    public void deleteUser() {
        userPersonService.deletedUser();
    }


    private void requestHeardContext(UnifiedLoginRequest request, HttpServletRequest httpRequest) {
        String clientIp = getClientIp(httpRequest);
        String source = httpRequest.getHeader("source");
        if (StringUtils.isBlank(source)) {
            source = RegisterSourceEnum.APP.getName();
        }
        String platform = httpRequest.getHeader("Platform");
        if (StringUtils.isBlank(platform)) {
            platform = PlatformType.CUSTOMER.getCode();
        }

        request.setRegisterSourceEnum(RegisterSourceEnum.fromName(source));
        request.setPlatform(PlatformType.getByCode(platform));
        request.setClientIp(clientIp);
    }


    /**
     * 获取客户端IP地址
     *
     * @param httpRequest HTTP请求对象
     * @return 客户端IP地址
     */
    private static String getClientIp(HttpServletRequest httpRequest) {
        // 获取客户端IP地址
        String clientIp = httpRequest.getHeader("X-Forwarded-For");
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = httpRequest.getHeader("Proxy-Client-IP");
        }
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = httpRequest.getHeader("WL-Proxy-Client-IP");
        }
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = httpRequest.getHeader("HTTP_CLIENT_IP");
        }
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = httpRequest.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (clientIp == null || clientIp.isEmpty() || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = httpRequest.getRemoteAddr();
        }

        // 如果是多个代理，第一个IP为客户端真实IP
        if (clientIp != null && clientIp.contains(",")) {
            clientIp = clientIp.split(",")[0];
        }
        return clientIp;
    }

    private void writeJsonError(HttpServletResponse response, int code, String message) {
        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType("application/json;charset=UTF-8");
        try (PrintWriter writer = response.getWriter()) {
            String json = String.format("{\"code\":%d,\"msg\":\"%s\"}", code, message);
            writer.write(json);
            writer.flush();
        } catch (IOException ioException) {
            // 写响应时异常打印日志
            ioException.printStackTrace();
        }
    }


}
