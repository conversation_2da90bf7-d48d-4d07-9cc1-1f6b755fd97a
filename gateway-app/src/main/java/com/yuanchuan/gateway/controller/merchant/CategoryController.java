package com.yuanchuan.gateway.controller.merchant;


import com.azure.core.annotation.Get;
import com.yuanchuan.common.response.Result;
import com.yuanchuan.merchant.management.api.dto.CategoryDTO;
import com.yuanchuan.merchant.management.api.service.CategoryService;
//import com.yuanchuan.reservation.api.dto.ReservationDTO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/merchant/category")
public class CategoryController {

    //@DubboReference(version = "1.0", group = "${dubbo.consumer.group.merchant}", retries = -1, timeout = 600000)
    private CategoryService categoryService;

    @GetMapping("/tree")
    public Result<List<CategoryDTO>> getCategoryTree() {

        List<CategoryDTO>  categoryDTOList = categoryService.getCategoryTree();

        return  Result.success(categoryDTOList);
    }
}
