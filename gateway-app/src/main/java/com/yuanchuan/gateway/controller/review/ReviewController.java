package com.yuanchuan.gateway.controller.review;

import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.yuanchuan.common.response.PageResult;
//import com.yuanchuan.review.api.dto.CreateReplyRequest;
//import com.yuanchuan.review.api.dto.CreateReviewRequest;
//import com.yuanchuan.review.api.dto.GetUserReviewsRequest;
//import com.yuanchuan.review.api.dto.LikeReviewResult;
//import com.yuanchuan.review.api.dto.ReplyDTO;
//import com.yuanchuan.review.api.dto.ReviewDTO;
//import com.yuanchuan.review.api.dto.UpdateReviewRequest;
//import com.yuanchuan.review.api.service.ReviewService;

@RestController
@RequestMapping("/api/v1/reviews")
public class ReviewController {

//    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.review}", retries = -1, timeout = 600000)
//    private ReviewService reviewService;

    //@PostMapping
    //public ResponseEntity<ReviewDTO> createReview(@RequestBody CreateReviewRequest request) {
    //    ReviewDTO review = reviewService.createReview(request);
    //    return ResponseEntity.ok(review);
    //}
    //
    //@PutMapping("/{id}")
    //public ResponseEntity<ReviewDTO> updateReview(
    //        @PathVariable Long id,
    //        @RequestBody UpdateReviewRequest request) {
    //    ReviewDTO review = reviewService.updateReview(id, request);
    //    return ResponseEntity.ok(review);
    //}
    //
    //@DeleteMapping("/{id}")
    //public ResponseEntity<Void> deleteReview(@PathVariable Long id) {
    //    reviewService.deleteReview(id);
    //    return ResponseEntity.ok().build();
    //}
    //
    //@PostMapping("/{id}/like")
    //public ResponseEntity<LikeReviewResult> likeReview(@PathVariable Long id) {
    //    LikeReviewResult result = reviewService.likeReview(id);
    //    return ResponseEntity.ok(result);
    //}
    //
    //@PostMapping("/{id}/replies")
    //public ResponseEntity<ReplyDTO> createReply(
    //        @PathVariable Long id,
    //        @RequestBody CreateReplyRequest request) {
    //    ReplyDTO reply = reviewService.createReply(id, request);
    //    return ResponseEntity.ok(reply);
    //}
    //
    //@GetMapping
    //public ResponseEntity<PageResult<ReviewDTO>> getUserReviews(
    //        @RequestParam(required = false) String status,
    //        @RequestParam(defaultValue = "1") Integer page,
    //        @RequestParam(defaultValue = "10") Integer pageSize) {
    //    GetUserReviewsRequest request = new GetUserReviewsRequest();
    //    request.setStatus(status);
    //    request.setPageNum(page);
    //    request.setPageSize(pageSize);
    //
    //    PageResult<ReviewDTO> reviews = reviewService.getUserReviews(request);
    //    return ResponseEntity.ok(reviews);
    //}
    //@GetMapping("/shop/{shopId}")
    //public ResponseEntity<PageResult<ReviewDTO>> getShopReviews(
    //        @PathVariable Long shopId,
    //        @RequestParam(defaultValue = "1") Integer page,
    //        @RequestParam(defaultValue = "10") Integer pageSize) {
    //    PageResult<ReviewDTO> reviews = reviewService.getShopReviews(shopId, page, pageSize);
    //    return ResponseEntity.ok(reviews);
    //}
    //@GetMapping("/deal/{dealId}")
    //public ResponseEntity<PageResult<ReviewDTO>> getDealReviews(
    //        @PathVariable Long dealId,
    //        @RequestParam(defaultValue = "1") Integer page,
    //        @RequestParam(defaultValue = "10") Integer pageSize) {
    //    PageResult<ReviewDTO> reviews = reviewService.getDealReviews(dealId, page, pageSize);
    //    return ResponseEntity.ok(reviews);
    //}
}