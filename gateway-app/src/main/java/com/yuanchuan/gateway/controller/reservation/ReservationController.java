//package com.yuanchuan.gateway.controller.reservation;
//
//import java.time.LocalDate;
//import java.time.LocalTime;
//import java.util.List;
//
//import org.apache.dubbo.config.annotation.DubboReference;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.PutMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import com.yuanchuan.reservation.api.dto.ReservationDTO;
//import com.yuanchuan.reservation.api.dto.ReservationOptionsDTO;
//import com.yuanchuan.reservation.api.service.ReservationService;
//
///**
// * 预订管理API接口
// */
//@RestController
//@RequestMapping("/api/v1/reservations")
//public class ReservationController {
//
//    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.reservation}", retries = -1, timeout = 600000)
//    private ReservationService reservationService;
//
//    /**
//     * 创建预订
//     */
//    @PostMapping
//    public ResponseEntity<ReservationDTO> createReservation(@RequestBody ReservationDTO request) {
//
//        ReservationDTO reservation = reservationService.createReservation(
//            request
//        );
//        return ResponseEntity.ok(reservation);
//    }
//
//    /**
//     * 修改预订
//     */
//    @PutMapping("/{reservationId}")
//    public ResponseEntity<Void> updateReservation(@PathVariable Long reservationId, @RequestBody ReservationDTO request) {
//        request.setId(reservationId);
//        reservationService.updateReservation(request);
//        return ResponseEntity.ok().build();
//    }
//
//    /**
//     * 取消预订
//     */
//    @PostMapping("/{reservationId}/cancel")
//    public ResponseEntity<Void> cancelReservation(@PathVariable Long reservationId) {
//        reservationService.cancelReservation(reservationId);
//        return ResponseEntity.ok().build();
//    }
//
//    /**
//     * 确认预订
//     */
//    @PostMapping("/{reservationId}/confirm")
//    public ResponseEntity<Void> confirmReservation(@PathVariable Long reservationId) {
//        reservationService.confirmReservation(reservationId);
//        return ResponseEntity.ok().build();
//    }
//
//    /**
//     * 标记预订为未到店
//     */
//    @PostMapping("/{reservationId}/no-show")
//    public ResponseEntity<Void> markReservationAsNoShow(@PathVariable Long reservationId) {
//        reservationService.markReservationAsNoShow(reservationId);
//        return ResponseEntity.ok().build();
//    }
//
//    /**
//     * 完成预订
//     */
//    @PostMapping("/{reservationId}/complete")
//    public ResponseEntity<Void> completeReservation(@PathVariable Long reservationId) {
//        reservationService.completeReservation(reservationId);
//        return ResponseEntity.ok().build();
//    }
//
//    /**
//     * 获取预订选项
//     * @param shopId 店铺ID
//     * @param date 预订日期
//     * @param time 预订时间（可选）
//     * @param peopleCount 预订人数（可选）
//     * @param tableType 桌型（可选）
//     * @return 预订选项信息
//     */
//    @GetMapping("/options")
//    public ResponseEntity<ReservationOptionsDTO> getReservationOptions(
//            @RequestParam Long shopId,
//            @RequestParam(required = false) LocalDate date,
//            @RequestParam(required = false) LocalTime time,
//            @RequestParam(required = false) Integer peopleCount,
//            @RequestParam(required = false) String tableType) {
//        ReservationOptionsDTO options = reservationService.queryReservationOptions(
//            shopId,
//            date,
//            time,
//            peopleCount,
//            tableType
//        );
//        return ResponseEntity.ok(options);
//    }
//
//    /**
//     * 获取用户的预订列表
//     * @param userId 用户ID
//     * @param status 预订状态（可选）
//     * @param page 页码
//     * @param size 每页大小
//     * @return 预订列表
//     */
//    @GetMapping("/user/{userId}")
//    public ResponseEntity<List<ReservationDTO>> getUserReservations(
//            @PathVariable Long userId,
//            @RequestParam(required = false) String status,
//            @RequestParam(defaultValue = "1") int page,
//            @RequestParam(defaultValue = "10") int size) {
//        List<ReservationDTO> reservations = reservationService.getUserReservations(
//            userId,
//            status,
//            page,
//            size
//        );
//        return ResponseEntity.ok(reservations);
//    }
//}