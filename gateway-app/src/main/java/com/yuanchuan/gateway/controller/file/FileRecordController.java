package com.yuanchuan.gateway.controller.file;

import cn.hutool.core.io.IoUtil;
import com.yuanchuan.common.response.PageResult;
import com.yuanchuan.file.api.dto.FileData;
import com.yuanchuan.file.api.dto.FileDownloadResult;
import com.yuanchuan.file.api.dto.FileRecordDTO;
import com.yuanchuan.file.api.query.FileRecordQuery;
import com.yuanchuan.file.api.query.ReSizePictureQuery;
import com.yuanchuan.file.api.service.FileRecordService;
import com.yuanchuan.file.api.vo.FileRecordVO;
import com.yuanchuan.file.api.vo.SafetyResultVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static java.nio.charset.StandardCharsets.ISO_8859_1;
import static java.nio.charset.StandardCharsets.UTF_8;


/**
* 文件记录
*
* @<NAME_EMAIL>
* @since 1.0.0 2025-05-15
*/
@RestController
@RequestMapping("/api/file_record")
@Tag(name = "文件记录相关功能", description = "文件记录相关接口")
public class FileRecordController {


    // @DubboReference(version = "1.0", group = "${dubbo.consumer.group.file}", retries = 0, timeout = 10000)
    private FileRecordService fileRecordService;


    /**
     * 上传文件
     */
    @Operation(summary = "上传文件", description = "上传文件")
    @PostMapping("/upload")
    public FileRecordVO upload(@Parameter(description = "文件") @RequestPart("file") MultipartFile file,
                               @Parameter(description = "是否公开") @RequestParam(value = "isOpen", defaultValue = "1", required = false) Integer isOpen,
                               @Parameter(description = "业务类型") @RequestParam("businessType" ) String businessType) throws IOException {
        FileData fileData = FileData.builder()
                .originalFilename(file.getOriginalFilename())
                .contentType(file.getContentType())
                .fileBytes(file.getBytes())
                .size(file.getSize()).build();
        return fileRecordService.upload(fileData, isOpen, businessType);
    }

    /**
     * 更新图片
     */
    @Operation(summary = "更新图片", description = "更新图片")
    @PostMapping("/updateImg")
    public void updateImg(@Parameter(description = "文件") @RequestPart("file") MultipartFile file,
                          @Parameter(description = "是否公开") @RequestParam(value = "isOpen", defaultValue = "1", required = false) Integer isOpen,
                          @Parameter(description = "文件名") @RequestParam("fileUuidName") String fileUuidName,
                          @Parameter(description = "业务类型") @RequestParam("businessType") String businessType) throws IOException {
        FileData fileData = FileData.builder()
                .originalFilename(file.getOriginalFilename())
                .contentType(file.getContentType())
                .fileBytes(file.getBytes())
                .size(file.getSize()).build();
        fileRecordService.updateImg(fileData, isOpen,fileUuidName, businessType);
    }

//    /**
//     * 缩放图片
//     */
//    @Operation(summary = "缩放图片", description = "缩放图片")
//    @PostMapping("/resize-picture")
//    public void resizePicture(@RequestBody ReSizePictureQuery reSizePictureQuery,
//                              HttpServletResponse response) throws IOException {
//        FileDownloadResult fileDownloadResult =fileRecordService.resizePicture(reSizePictureQuery);
//        // 2. 手动写入HTTP响应
//        response.setContentType(fileDownloadResult.getContentType());
//        response.setHeader("Content-Disposition",
//                "attachment; filename=" + fileDownloadResult.getFileName());
//        response.getOutputStream().write(fileDownloadResult.getFileContent());
//    }

    /**
     * 下载文件
     */
    @Operation(summary = "下载文件", description = "下载文件")
    @GetMapping("/download")
    public void download(@Parameter(description = "唯一标识文件名") @RequestParam("fileUuidName") String fileUuidName,
                         HttpServletResponse response) throws IOException {
        FileDownloadResult fileDownloadResult = fileRecordService.download(fileUuidName);
        // 2. 手动写入HTTP响应
        response.setContentType(fileDownloadResult.getContentType());
        response.setHeader("Content-Disposition",
                "attachment; filename=" + fileDownloadResult.getFileName());
        response.getOutputStream().write(fileDownloadResult.getFileContent());
    }


    /**
     * 删除文件
     */
    @Operation(summary = "删除文件", description = "删除文件")
    @DeleteMapping("/deleteFile")
    public void deleteFile(@Parameter(description = "唯一标识文件名") @RequestParam("fileUuidName") String fileUuidName){
        fileRecordService.deleteByFileUuidName(fileUuidName);
    }




    /**
     * ai-图片检测
     */
    @Operation(summary = "ai-图片检测", description = "ai-图片检测")
    @PostMapping("/checkImg")
    public SafetyResultVO checkImg(@Parameter(description = "文件") @RequestPart("file") MultipartFile file) throws IOException {
        FileData fileData = FileData.builder()
                .originalFilename(file.getOriginalFilename())
                .contentType(file.getContentType())
                .fileBytes(file.getBytes())
                .size(file.getSize()).build();
        return fileRecordService.checkImg(fileData);
    }



    /**
     * ai-文本检测
     */
    @Operation(summary = "ai-文本检测", description = "ai-文本检测")
    @PostMapping("/checkText")
    public SafetyResultVO checkText(@Parameter(description = "需要检测的文本内容" , required = true) @RequestParam("text" ) String text) {
        return fileRecordService.checkText(text);
    }

}