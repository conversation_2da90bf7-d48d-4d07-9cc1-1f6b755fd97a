//package com.yuanchuan.gateway.controller.location;
//
//import com.yuanchuan.common.response.PageResult;
//import com.yuanchuan.location.api.dto.TaiwanRegionDTO;
//import com.yuanchuan.location.api.query.TaiwanRegionQuery;
//import com.yuanchuan.location.api.service.DistanceRangesService;
//import com.yuanchuan.location.api.service.MetroStationsService;
//import com.yuanchuan.location.api.service.TaiwanRegionService;
//import com.yuanchuan.location.api.vo.SearchRegionVO;
//import com.yuanchuan.location.api.vo.TaiwanRegionVO;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import org.apache.dubbo.config.annotation.DubboReference;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
//
///**
//* 行政区划表
//*
//* @<NAME_EMAIL>
//* @since 1.0.0 2025-05-22
//*/
//@RestController
//@RequestMapping("/api/location")
//@Tag(name = "位置领域聚合接口", description = "位置领域聚合接口")
//public class LocationController {
//
//
//    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.location}", retries = 0, timeout = 10000)
//    private DistanceRangesService distanceRangesService;
//
//    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.location}", retries = 0, timeout = 20000)
//    private TaiwanRegionService taiwanRegionService;
//
//    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.location}", retries = 0, timeout = 10000)
//    private MetroStationsService metroStationsService;
//
//
//
//
//    /**
//     * 获取三级行政区划树结构
//     */
//    @GetMapping("/getRegionTree")
//    @Operation(summary = "获取三级行政区划树结构", description = "获取三级行政区划树结构")
//    public List<TaiwanRegionVO> getRegionTree() {
//        return taiwanRegionService.getRegionTree();
//    }
//
//
//    /**
//     * C端搜索三级位置相关下拉树结构
//     */
//    @GetMapping("/getSearchRegionTree")
//    @Operation(summary = "C端搜索三级位置相关下拉树结构", description = "C端搜索三级位置相关下拉树结构")
//    public SearchRegionVO getSearchRegionTree() {return taiwanRegionService.getSearchRegionTree();
//    }
//
//
//
//
////    /**
////    * 新增
////    */
////    @PostMapping("/add")
////    @Operation(summary = "新增", description = "根据入参执行实体的新增操作")
////    public void add(@RequestBody TaiwanRegionDTO dto) {
////        taiwanRegionService.add(dto);
////    }
////
////    /**
////    * 修改
////    */
////    @PutMapping("/update")
////    @Operation(summary = "修改", description = "根据dto执行实体更新操作")
////    public void update(@RequestBody TaiwanRegionDTO dto) {
////        taiwanRegionService.update(dto);
////    }
////
////    /**
////    * 删除
////    */
////    @DeleteMapping("/delete")
////    @Operation(summary = "删除", description = "逻辑删除")
////    public void delete(@RequestParam("id") Long id) {
////        taiwanRegionService.delete(id);
////    }
////
////    /**
////    * 详情
////    */
////    @GetMapping("/detail")
////    @Operation(summary = "详情", description = "根据ID获取详情")
////    public TaiwanRegionVO detail(@RequestParam("id") Long id) {
////        return taiwanRegionService.detail(id);
////    }
////
////
////    @PostMapping("/voPage")
////    @Operation(summary = "分页", description = "根据入参执行分页查询")
////    public PageResult<TaiwanRegionVO> voPage(@RequestBody TaiwanRegionQuery query) {
////        return taiwanRegionService.voPage(query);
////    }
//
//}