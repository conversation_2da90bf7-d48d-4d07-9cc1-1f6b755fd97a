package com.yuanchuan.gateway.controller.reservation;

import java.util.List;

import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.yuanchuan.common.response.Result;
//import com.yuanchuan.reservation.api.dto.CreateReservationTimeRuleRequest;
//import com.yuanchuan.reservation.api.dto.ReservationTimeRuleDTO;
//import com.yuanchuan.reservation.api.dto.UpdateReservationTimeRuleRequest;
//import com.yuanchuan.reservation.api.service.ReservationTimeRuleService;

/**
 * 预订时段规则管理接口
 */
@RestController
@RequestMapping("/api/v1/reservation-rules")
public class ReservationTimeRuleController {

//    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.reservation}", retries = -1, timeout = 600000)
//    private ReservationTimeRuleService reservationTimeRuleService;
//
//    /**
//     * 创建预订时段规则
//     */
//    @PostMapping
//    public Result<ReservationTimeRuleDTO> createRule(@RequestBody CreateReservationTimeRuleRequest request) {
//        return Result.success(reservationTimeRuleService.createRule(request));
//    }
//
//    /**
//     * 更新预订时段规则
//     */
//    @PutMapping("/{id}")
//    public Result<ReservationTimeRuleDTO> updateRule(@PathVariable Long id,
//            @RequestBody UpdateReservationTimeRuleRequest request) {
//        request.setId(id);
//        return Result.success(reservationTimeRuleService.updateRule(request));
//    }
//
//    /**
//     * 删除预订时段规则
//     */
//    @DeleteMapping("/{id}")
//    public Result<Void> deleteRule(@PathVariable Long id) {
//        reservationTimeRuleService.deleteRule(id);
//        return Result.success();
//    }
//
//    /**
//     * 获取预订时段规则详情
//     */
//    @GetMapping("/{id}")
//    public Result<ReservationTimeRuleDTO> getRule(@PathVariable Long id) {
//        return Result.success(reservationTimeRuleService.getRule(id));
//    }
//
//    /**
//     * 查询店铺的预订时段规则列表
//     */
//    @GetMapping("/shop/{shopId}")
//    public Result<List<ReservationTimeRuleDTO>> listRulesByShop(@PathVariable Long shopId) {
//        return Result.success(reservationTimeRuleService.listRulesByShop(shopId));
//    }
//
//    /**
//     * 启用预订时段规则
//     */
//    @PutMapping("/{id}/enable")
//    public Result<Void> enableRule(@PathVariable Long id) {
//        reservationTimeRuleService.enableRule(id);
//        return Result.success();
//    }
//
//    /**
//     * 停用预订时段规则
//     */
//    @PutMapping("/{id}/disable")
//    public Result<Void> disableRule(@PathVariable Long id) {
//        reservationTimeRuleService.disableRule(id);
//        return Result.success();
//    }
}