package com.yuanchuan.gateway.controller.order;

import java.util.List;

import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.yuanchuan.common.response.PageResult;
//import com.yuanchuan.order.api.dto.CreateOrderRequest;
//import com.yuanchuan.order.api.dto.OrderDTO;
//import com.yuanchuan.order.api.dto.OrderItemCodeDTO;
//import com.yuanchuan.order.api.dto.OrderPageQuery;
//import com.yuanchuan.order.api.dto.PayOrderRequest;
//import com.yuanchuan.order.api.dto.VerifyCodeRequest;
//import com.yuanchuan.order.api.service.OrderService;

@RestController
@RequestMapping("/api/orders")
public class OrderController {

//    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.order}", retries = -1, timeout = 600000)
//    private OrderService orderService;
//
//    @PostMapping
//    public OrderDTO createOrder(@RequestBody CreateOrderRequest request) {
//        return orderService.createOrder(request);
//    }
//
//    @GetMapping("/{orderId}")
//    public OrderDTO getOrderDetail(@PathVariable Long orderId) {
//        return orderService.getOrderDetail(orderId);
//    }
//
//    @PostMapping("/{orderId}/pay")
//    public OrderDTO payOrder(@PathVariable Long orderId, @RequestBody PayOrderRequest request) {
//        return orderService.payOrder(orderId, request);
//    }
//
//    /**
//     * 分页查询订单列表
//     * @param query 查询条件，包含订单状态和分页参数
//     * @return 订单列表分页结果
//     */
//    @GetMapping
//    public PageResult<OrderDTO> getOrderList(OrderPageQuery query) {
//        return orderService.getOrderList(query);
//    }
//
//    /**
//     * 查询订单券码列表
//     * @param orderId 订单ID
//     * @return 订单券码列表
//     */
//    @GetMapping("/{orderId}/codes")
//    public List<OrderItemCodeDTO> getOrderItemCodes(@PathVariable Long orderId) {
//        return orderService.getOrderItemCodes(orderId);
//    }
//
//    /**
//     * 根据券码查询订单券码列表
//     * @param code 券码
//     * @param shopId 店铺ID
//     * @return 订单券码列表
//     */
//    @GetMapping("/codes/{code}")
//    public List<OrderItemCodeDTO> getOrderItemCodeByCode(@PathVariable String code, @RequestParam Long shopId) {
//        return orderService.getOrderItemCodeByCode(code, shopId);
//    }
//
//    /**
//     * 核销券码
//     * @param code 券码
//     * @param request 核销请求信息
//     * @return 核销后的券码信息
//     */
//    @PostMapping("/codes/{code}/verify")
//    public OrderItemCodeDTO verifyCode(@PathVariable String code, @RequestBody VerifyCodeRequest request) {
//        return orderService.verifyCode(code, request);
//    }

}