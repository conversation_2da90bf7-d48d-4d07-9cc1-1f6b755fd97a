package com.yuanchuan.gateway.controller.auth;

import com.yuanchuan.authentication.api.dto.TokenDTO;
import com.yuanchuan.authentication.api.request.TokenRefreshRequest;
import com.yuanchuan.authentication.api.response.TokenRefreshResponse;
import com.yuanchuan.authentication.api.service.AuthenticationService;
import com.yuanchuan.common.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Token刷新控制器
 * 提供无感刷新token的功能
 */
@RestController
@RequestMapping("/api/v1/auth")
@Tag(name = "Token刷新接口", description = "包括token刷新等认证相关接口")
@Slf4j
public class TokenRefreshController {

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.authentication}")
    private AuthenticationService authenticationService;

    /**
     * 刷新Token
     * 每次调用此接口会返回新的token和refreshToken，保证refreshToken不会过期
     *
     * @param request 包含refreshToken的请求对象
     * @return 新的token和refreshToken
     */
    @Operation(summary = "刷新Token", description = "使用refreshToken获取新的token和refreshToken，实现无感刷新")
    @PostMapping("/token/refresh")
    public Result<TokenRefreshResponse> refreshToken(
            @Parameter(description = "包含refreshToken的请求对象", required = true)
            @Valid @RequestBody TokenRefreshRequest request) {
        try {
            log.info("开始刷新Token，refreshToken: {}", request.getRefreshToken().substring(0, 10) + "...");
            
            // 调用认证服务刷新Token
            TokenDTO tokenDTO = authenticationService.refreshToken(request.getRefreshToken());
            
            // 构建响应对象
            TokenRefreshResponse response = TokenRefreshResponse.builder()
                    .accessToken(tokenDTO.getAccessToken())
                    .refreshToken(tokenDTO.getRefreshToken())
                    .tokenType("Bearer")
                    .expiresAt(tokenDTO.getExpiresAt())
                    .refreshExpiresAt(tokenDTO.getRefreshExpiresAt())
                    .build();
            
            log.info("Token刷新成功，用户ID: {}", tokenDTO.getUserId());
            return Result.success("Token刷新成功", response);
        } catch (IllegalArgumentException e) {
            log.error("Token刷新失败: {}", e.getMessage());
            return Result.error(e.getMessage());
        } catch (Exception e) {
            log.error("Token刷新过程中发生错误", e);
            return Result.error("伺服器內部錯誤: " + e.getMessage());
        }
    }
}
