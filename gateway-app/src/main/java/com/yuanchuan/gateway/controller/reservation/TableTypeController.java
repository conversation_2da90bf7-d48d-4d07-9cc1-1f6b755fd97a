//package com.yuanchuan.gateway.controller.reservation;
//
//import java.util.List;
//
//import org.apache.dubbo.config.annotation.DubboReference;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.DeleteMapping;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.PutMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import com.yuanchuan.common.response.Result;
//import com.yuanchuan.reservation.api.dto.TableTypeDTO;
//import com.yuanchuan.reservation.api.service.TableTypeService;
//
///**
// * 桌型管理接口
// */
//@RestController
//@RequestMapping("/api/v1/table-types")
//public class TableTypeController {
//
//    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.reservation}", retries = -1, timeout = 600000)
//    private TableTypeService tableTypeService;
//
//
//    /**
//     * 创建桌型
//     */
//    @PostMapping
//    public Result<Void> createTableType(@RequestBody @Validated TableTypeDTO tableTypeDTO) {
//        tableTypeService.createTableType(tableTypeDTO);
//        return Result.success();
//    }
//
//    /**
//     * 更新桌型
//     */
//    @PutMapping("/{id}")
//    public Result<Void> updateTableType(@PathVariable Long id, @RequestBody @Validated TableTypeDTO tableTypeDTO) {
//        tableTypeDTO.setId(id);
//        tableTypeService.updateTableType(tableTypeDTO);
//        return Result.success();
//    }
//
//    /**
//     * 删除桌型
//     */
//    @DeleteMapping("/{id}")
//    public Result<Void> deleteTableType(@PathVariable Long id) {
//        tableTypeService.deleteTableType(id);
//        return Result.success();
//    }
//
//    /**
//     * 根据ID查询桌型
//     */
//    @GetMapping("/{id}")
//    public Result<TableTypeDTO> getTableTypeById(@PathVariable Long id) {
//        return Result.success(tableTypeService.getTableTypeById(id));
//    }
//
//    /**
//     * 根据店铺ID查询桌型列表
//     */
//    @GetMapping("/shop/{shopId}")
//    public Result<List<TableTypeDTO>> getTableTypesByShopId(@PathVariable Long shopId) {
//        return Result.success(tableTypeService.getTableTypesByShopId(shopId));
//    }
//
//    /**
//     * 根据店铺ID和区域查询桌型列表
//     */
//    @GetMapping("/shop/{shopId}/area/{area}")
//    public Result<List<TableTypeDTO>> getTableTypesByShopIdAndArea(
//            @PathVariable Long shopId,
//            @PathVariable String area) {
//        return Result.success(tableTypeService.getTableTypesByShopIdAndArea(shopId, area));
//    }
//
//    /**
//     * 根据店铺ID和容纳人数查询桌型列表
//     */
//    @GetMapping("/shop/{shopId}/capacity/{capacity}")
//    public Result<List<TableTypeDTO>> getTableTypesByShopIdAndCapacity(
//            @PathVariable Long shopId,
//            @PathVariable int capacity) {
//        return Result.success(tableTypeService.getTableTypesByShopIdAndCapacity(shopId, capacity));
//    }
//
//    /**
//     * 检查桌型编码是否已存在
//     */
//    @GetMapping("/shop/{shopId}/code/{code}/exists")
//    public Result<Boolean> isTableTypeCodeExists(
//            @PathVariable Long shopId,
//            @PathVariable String code) {
//        return Result.success(tableTypeService.isTableTypeCodeExists(shopId, code));
//    }
//}