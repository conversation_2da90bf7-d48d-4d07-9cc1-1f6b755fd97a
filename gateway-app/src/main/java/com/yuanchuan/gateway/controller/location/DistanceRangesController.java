//package com.yuanchuan.gateway.controller.location;
//
//import com.yuanchuan.common.response.PageResult;
//import com.yuanchuan.location.api.service.DistanceRangesService;
//import com.yuanchuan.location.api.vo.DistanceRangesVO;
//import com.yuanchuan.location.api.dto.DistanceRangesDTO;
//import com.yuanchuan.location.api.query.DistanceRangesQuery;
//import org.springframework.web.bind.annotation.*;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import org.apache.dubbo.config.annotation.DubboReference;
//
//
///**
//* 距离范围配置表
//*
//* @<NAME_EMAIL>
//* @since 1.0.0 2025-05-22
//*/
//@RestController
//@RequestMapping("/api/distance_ranges")
//@Tag(name = "距离范围配置表相关功能", description = "距离范围配置表相关接口")
//public class DistanceRangesController {
//
//
//    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.location}", retries = 0, timeout = 10000)
//    private DistanceRangesService distanceRangesService;
//
//
//    /**
//    * 新增
//    */
//    @PostMapping("/add")
//    @Operation(summary = "新增", description = "根据入参执行实体的新增操作")
//    public void add(@RequestBody DistanceRangesDTO dto) {
//        distanceRangesService.add(dto);
//    }
//
//    /**
//    * 修改
//    */
//    @PutMapping("/update")
//    @Operation(summary = "修改", description = "根据dto执行实体更新操作")
//    public void update(@RequestBody DistanceRangesDTO dto) {
//        distanceRangesService.update(dto);
//    }
//
//    /**
//    * 删除
//    */
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除", description = "逻辑删除")
//    public void delete(@RequestParam("id") Long id) {
//        distanceRangesService.delete(id);
//    }
//
//    /**
//    * 详情
//    */
//    @GetMapping("/detail")
//    @Operation(summary = "详情", description = "根据ID获取详情")
//    public DistanceRangesVO detail(@RequestParam("id") Long id) {
//        return distanceRangesService.detail(id);
//    }
//
//
//    @PostMapping("/voPage")
//    @Operation(summary = "分页", description = "根据入参执行分页查询")
//    public PageResult<DistanceRangesVO> voPage(@RequestBody DistanceRangesQuery query) {
//        return distanceRangesService.voPage(query);
//    }
//
//}