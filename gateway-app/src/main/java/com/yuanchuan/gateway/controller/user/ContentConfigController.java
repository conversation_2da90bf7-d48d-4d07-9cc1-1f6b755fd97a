package com.yuanchuan.gateway.controller.user;

import com.yuanchuan.common.response.Result;
import com.yuanchuan.common.utils.ContextUtils;
import com.yuanchuan.user.api.dto.ContentConfigDTO;
import com.yuanchuan.user.api.dto.ContentConfigDetailDTO;
import com.yuanchuan.user.api.request.ContentConfigModifyRequest;
import com.yuanchuan.user.api.request.ContentConfigQueryRequest;
import com.yuanchuan.user.api.request.ContentHtmlRequest;
import com.yuanchuan.user.api.service.ContentConfigService;
import com.yuanchuan.user.context.enums.ContentType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 内容配置控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/contentConfigs")
@Tag(name = "内容配置", description = "内容配置相关接口，包括协议、隐私政策等")
public class ContentConfigController {

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.user}")
    private ContentConfigService contentConfigService;

    @PostMapping
    @Operation(summary = "创建内容配置", description = "创建内容配置，需要管理员权限，适用于所有类型的内容")
    public Long createContentConfig(
            @RequestBody @Valid ContentConfigDTO dto,
            @Parameter(description = "内容类型，如果与dto中的类型不一致，则使用此参数") @RequestParam(required = true) ContentType contentType) {
        String operator = ContextUtils.getCurrentUsername();
        return contentConfigService.createContentConfig(dto, operator);
    }


    @GetMapping("/{id}")
    @Operation(summary = "获取内容配置详情", description = "根据ID获取内容配置详情，适用于所有类型的内容")
    public ContentConfigDetailDTO getContentConfigDetail(
            @Parameter(description = "内容配置ID") @PathVariable Long id) {
        return contentConfigService.getContentConfigDetail(id);
    }


    @PostMapping("/queryConfigs")
    @Operation(summary = "获取内容配置列表", description = "获取内容配置列表，支持按类型、状态过滤，当类型为HELP时，可以返回层级结构")
    public List<ContentConfigDTO> getContentConfigs(@RequestBody ContentConfigQueryRequest request) {
        return contentConfigService.getContentConfigs(request);
    }


    @PostMapping("/modifyContentConfig")
    @Operation(summary = "更新内容配置", description = "更新内容配置，需要管理员权限")
    public ContentConfigDetailDTO updateContentConfig(
            @RequestBody @Valid ContentConfigModifyRequest request) {
        String operator = ContextUtils.getCurrentUsername();
        request.setOperator(operator);
        return contentConfigService.updateContentConfig(request);
    }


    /**
     * 获取内容（HTML格式）
     *
     * @param request 请求参数，包含内容类型和标题
     * @return 内容的HTML格式
     */
    @PostMapping(value = "/html", produces = MediaType.TEXT_HTML_VALUE)
    @Operation(summary = "获取协议内容（HTML格式）", description = "根据类型和标题获取内容，返回HTML格式，适用于协议、帮助等各种类型的内容")
    public String getContentHtml(@RequestBody @Valid ContentHtmlRequest request) {
        try {
            ContentType type = request.getType();
            String title = request.getTitle();

            List<ContentConfigDTO> dtoList;
            if (title != null && !title.isEmpty()) {
                // 如果指定了标题，则查询特定标题的内容
                dtoList = contentConfigService.getEnabledContentConfigsByType(type.name());
                if (dtoList != null) {
                    for (ContentConfigDTO dto : dtoList) {
                        if (title.equals(dto.getTitle())) {
                            return dto.getContent();
                        }
                    }
                }
                return "<h1>未找到指定内容</h1>";
            } else {
                // 如果未指定标题，则返回该类型的第一个内容
                dtoList = contentConfigService.getEnabledContentConfigsByType(type.name());
                if (dtoList != null && !dtoList.isEmpty()) {
                    return dtoList.get(0).getContent();
                }
                return "<h1>未找到内容</h1>";
            }
        } catch (Exception e) {
            log.error("获取内容异常", e);
            return "<h1>获取内容失败</h1>";
        }
    }
}
