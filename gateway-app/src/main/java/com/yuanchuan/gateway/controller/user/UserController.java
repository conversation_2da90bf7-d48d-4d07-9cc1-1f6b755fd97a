package com.yuanchuan.gateway.controller.user;

import com.alibaba.fastjson2.JSONObject;
import com.yuanchuan.user.api.dto.*;
import com.yuanchuan.user.api.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-14 16:26:16:26
 */
@RestController
@RequestMapping("/api/v1/user")
@Tag(name = "用户相关", description = "包括注册、登录、验证码等认证相关接口")
@Slf4j
public class UserController {

    @DubboReference(version = "1.0", group = "${dubbo.consumer.group.user}")
    private UserService userService;

    @GetMapping("/profile")
    @Operation(summary = "获取用户个人信息")
    public UserResponse getUserProfile() {
        return userService.getUserProfile();
    }

    @PostMapping("/nickname")
    @Operation(summary = "更新用户昵称")
    public void updateNickname(@Parameter(description = "更新用户昵称请求对象", required = true)
                               @RequestBody @Valid UserProfileUpdateDTO.NicknameUpdateRequest request) {
        log.info("UserController#updateNickname: {}", JSONObject.toJSONString(request));
        userService.updateNickname(request);

    }

    @PostMapping("/avatar")
    @Operation(summary = "更新用户头像")
    public void updateAvatar(@Parameter(description = "更新用户头像请求对象", required = true)
                             @RequestBody @Valid UserProfileUpdateDTO.AvatarUpdateRequest request) {
        log.info("UserController#updateAvatar: {}", JSONObject.toJSONString(request));
        userService.updateAvatar(request);
    }

    @PostMapping("/gender")
    @Operation(summary = "更新用户性别")
    public void updateGender(@Parameter(description = "更新用户性别请求对象", required = true)
                             @RequestBody @Valid UserProfileUpdateDTO.GenderUpdateRequest request) {
        log.info("UserController#updateGender: {}", JSONObject.toJSONString(request));
        userService.updateGender(request);
    }

    @PostMapping("/birthday")
    @Operation(summary = "更新用户生日")
    public void updateBirthday(@Parameter(description = "更新用户生日请求对象", required = true)
                               @RequestBody @Valid UserProfileUpdateDTO.BirthdayUpdateRequest request) {
        log.info("UserController#updateBirthday: {}", JSONObject.toJSONString(request));
        userService.updateBirthday(request);
    }

    @PostMapping("/address")
    @Operation(summary = "更新用户常居地")
    public void updateAddress(@Parameter(description = "更新用户常居地请求对象", required = true)
                              @RequestBody @Valid UserProfileUpdateDTO.AddressUpdateRequest request) {
        log.info("UserController#updateAddress: {}", JSONObject.toJSONString(request));
        userService.updateAddress(request);
    }


    @PostMapping("/phone/verify")
    @Operation(summary = "验证手机号")
    public void verifyPhone(@Parameter(description = "验证手机号请求对象", required = true)
                            @RequestBody @Valid UserAccountUpdateDTO.PhoneVerifyRequest request) {
        log.info("UserController#verifyPhone: {}", JSONObject.toJSONString(request));
        userService.verifyPhone(request);
    }


    @PostMapping("/phone/update")
    @Operation(summary = "换绑手机号")
    public void updatePhone(@Parameter(description = "换绑手机号请求对象", required = true)
                            @RequestBody @Valid UserAccountUpdateDTO.PhoneUpdateRequest request) {
        log.info("UserController#updatePhone: {}", JSONObject.toJSONString(request));
        userService.updatePhone(request);
    }

    @PostMapping("/email/bind")
    @Operation(summary = "绑定邮箱")
    public void bindEmail(@Parameter(description = "绑定邮箱请求对象", required = true)
                          @RequestBody @Valid UserAccountUpdateDTO.EmailBindRequest request) {
        log.info("UserController#bindEmail: {}", JSONObject.toJSONString(request));
        userService.bindEmail(request);
    }

    @PostMapping("/email/verify")
    @Operation(summary = "验证原邮箱")
    public void verifyOriginalEmail(@Parameter(description = "绑定邮箱请求对象", required = true)
                                    @RequestBody @Valid UserAccountUpdateDTO.EmailVerifyRequest request) {
        log.info("UserController#verifyOriginalEmail: {}", JSONObject.toJSONString(request));
        userService.verifyOriginalEmail(request);
    }

    @PostMapping("/password/set")
    @Operation(summary = "设置密码")
    public void setPassword(@Parameter(description = "设置密码请求对象", required = true)
                            @RequestBody @Valid UserAccountUpdateDTO.PasswordSetRequest request) {
        log.info("UserController#setPassword: {}", JSONObject.toJSONString(request));
        userService.setPassword(request);
    }

    @PostMapping("/password/update")
    @Operation(summary = "验证原密码")
    public void verifyOriginalPassword(@Parameter(description = "验证原密码请求对象", required = true)
                                       @RequestBody @Valid UserAccountUpdateDTO.PasswordUpdateRequest request) {
        log.info("UserController#verifyOriginalPassword: {}", JSONObject.toJSONString(request));
        userService.verifyOriginalPassword(request);
    }

    @GetMapping("/binding/list")
    @Operation(summary = "获取第三方绑定列表")
    public ThirdPartyBindingDTO.ThirdPartyBindingListResponse getThirdPartyBindingList() {
        return userService.getThirdPartyBindingList();
    }

    @PostMapping("/unbind")
    @Operation(summary = "解绑第三方账号")
    public void unbindThirdParty(@Parameter(description = "解绑第三方账号请求对象", required = true)
                                 @RequestBody @Valid ThirdPartyBindingDTO.UnbindThirdPartyRequest request) {
        userService.unbindThirdParty(request);
    }

    @PostMapping("/bind")
    @Operation(summary = "绑定第三方账号")
    public void bindThirdParty(@Parameter(description = "绑定第三方账号", required = true)
                               @RequestBody @Valid ThirdPartyBindingDTO.BindThirdPartyRequest request) {
        userService.bindThirdParty(request);
    }


    /**
     * 根据坐标获取地址信息
     *
     * @param request 坐标请求
     * @return 地址信息
     */
    @PostMapping("/address/coordinate")
    @Operation(summary = "定位获取地址")
    public AddressLookupDTO.AddressResponse getAddressByCoordinate(@Parameter(description = "定位获取地址", required = true)
                                                                   @RequestBody @Valid AddressLookupDTO.CoordinateRequest request) {
        return userService.getAddressByCoordinate(request);
    }

    /**
     * 根据名称搜索地址信息
     *
     * @param request 名称搜索请求
     * @return 地址信息
     */
    @PostMapping("/search/address")
    @Operation(summary = "名称搜索地址")
    public List<AddressLookupDTO.AddressResponse> getAddressByName(@Parameter(description = "名称搜索地址", required = true)
                                                                   @RequestBody @Valid AddressLookupDTO.NameRequest request) {
        return userService.getAddressByName(request);
    }

    @PostMapping("/verify/newEmail")
    @Operation(summary = "校验邮箱是否被用")
    public void verifyNewEmail(@Parameter(description = "名称搜索地址", required = true)
                               @RequestBody @Valid UserAccountUpdateDTO.EmailVerifyRequest request) {
        userService.verifyNewEmail(request);
    }


}
