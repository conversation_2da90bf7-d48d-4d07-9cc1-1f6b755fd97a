package com.yuanchuan.gateway.exception;

import com.yuanchuan.common.exception.BusinessException;
import com.yuanchuan.common.response.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Slf4j
@RestControllerAdvice(basePackages = {
        "com.yuanchuan.gatewayMerchant.controller",
        "com.yuanchuan.gatewayAdmin.controller",
        "com.yuanchuan.gateway.controller"})
public class GlobalExceptionHandlerAdvice {

    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<Object> handleValidException(MethodArgumentNotValidException e) {
        // 日志记录错误信息
        log.error(Objects.requireNonNull(e.getBindingResult().getFieldError()).getDefaultMessage());
        // 将错误信息返回给前台
        return Result.error(500, Objects.requireNonNull(e.getBindingResult().getFieldError()).getDefaultMessage());
    }


    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    @ExceptionHandler(BusinessException.class)
    public Result<Object> handleTokenException(BusinessException e) {
        log.error(e.toString());
        // 将错误信息返回给前台
        log.info("BusinessException: code={}, message={}", e.getErrorCode(), e.getErrorMessage());
        return Result.error(e.getErrorCode(), e.getErrorMessage());
    }

    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    @ExceptionHandler(IllegalArgumentException.class)
    public Result<Object> handleIllegalArgumentException(IllegalArgumentException e) {
        log.error(e.toString());
        // 将错误信息返回给前台
        return Result.error(HttpStatus.BAD_REQUEST.value(), e.getMessage());
    }


    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    @ExceptionHandler(RuntimeException.class)
    public Result<Object> handleRunTimeException(RuntimeException e) {
        log.error(e.toString());
        // 递归获取最底层的原始异常
        BusinessException businessException = findBusinessException(e);
        if (businessException != null) {
            // 处理业务异常
            log.error("捕获到 BusinessException:{}", businessException);
            return Result.error(businessException.getErrorCode(), businessException.getErrorMessage());
        }

        // 将错误信息返回给前台
        log.info("RuntimeException msg [{}]",e.getCause(),e);
        return Result.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "請求錯誤");
    }



    /**
     * 递归获取最底层的原始异常
     */
    private BusinessException findBusinessException(Throwable throwable) {
        while (true) {
            if (throwable instanceof BusinessException) {
                return (BusinessException) throwable;
            }

            // 如果有 cause，继续找
            if (throwable.getCause() != null && throwable.getCause() != throwable) {
                throwable = throwable.getCause();
            } else {
                // 如果当前 Throwable 是 RuntimeException，并且 message 中含有 BusinessException 字样
                if (throwable instanceof RuntimeException runtimeException) {
                    String msg = runtimeException.getMessage();
                    if (msg != null && msg.contains("BusinessException")) {
                        BusinessException parsed = parseBusinessExceptionFromMessage(msg);
                        if (parsed != null) {
                            return parsed;
                        }
                    }
                }
                break;
            }
        }

        return null;
    }

    private BusinessException parseBusinessExceptionFromMessage(String message) {
        // 正则表达式匹配：BusinessException(errorCode=..., errorMessage=...)
        Pattern pattern = Pattern.compile("BusinessException\\(errorCode=(\\d+),\\s*errorMessage=([^\\)]+)\\)");
        Matcher matcher = pattern.matcher(message);

        if (matcher.find()) {
            try {
                int errorCode = Integer.parseInt(matcher.group(1));
                String errorMessage = matcher.group(2).trim();
                return new BusinessException(errorCode, errorMessage);
            } catch (NumberFormatException e) {
                // 格式不正确或数值转换失败
                return null;
            }
        }

        return null;
    }

}
