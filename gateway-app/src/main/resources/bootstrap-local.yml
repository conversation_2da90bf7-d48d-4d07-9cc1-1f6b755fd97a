server:
  port: 8091

spring:
  application:
    name: gateway-app-juno
  main:
    allow-bean-definition-overriding: true

shenyu:
  sync:
    websocket:
      urls: ws://127.0.0.1:9095/websocket  # ShenYu admin websocket地址
      allow-origin: "*"
  cross:
    enabled: true
  dubbo:
    parameter: multi
  switchConfig:
    local: true
  file:
    enabled: true
    max-size : 10
  exclude:
    enabled: true
    paths:
      - /favicon.ico
  extPlugin:
    path: plugins
    enabled: true
    threads: 1
    scheduleTime: 300
    scheduleDelay: 30
  scheduler:
    enabled: false
    type: fixed
    threads: 16
  upstreamCheck:
    enabled: false
    timeout: 3000
    healthyThreshold: 1
    unhealthyThreshold: 1
    interval: 5000
    printEnabled: true
    printInterval: 60000

# Dubbo配置
dubbo:
  registry:
    address: zookeeper://**************:2181
    parameters:
      enableEmptyProtection: false
    client: curator  
  application:
    name: gateway-app11
    qos-enable: false
  protocol:
    name: dubbo
    port: -1

logging:
  level:
    root: info
    org.apache.shenyu: info
    org.springframework.boot: info

authentication:
  callbackDomain: http://dev.goldenmilestech.com

# dubbo 消费者group
#dubbo.consumer.group.user: user-dev
dubbo.consumer.group.user: user-local-juno
#dubbo.consumer.group.merchant: merchant-dev
dubbo.consumer.group.order: order-dev
#dubbo.consumer.group.file: file-dev
dubbo.consumer.group.reservation: reservation-dev
dubbo.consumer.group.review: review-dev
#dubbo.consumer.group.authentication: authentication-dev
dubbo.consumer.group.authentication: authentication-local-juno