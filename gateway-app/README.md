# Apache ShenYu Gateway Application

这是一个基于Apache ShenYu的网关应用，用于处理Dubbo服务的请求转发和服务治理。

# Gateway 网关微服务项目 - API网关
包路径：com.yuanchuan.gateway

### 服务基本信息

- **服务名称**: gateway-app
- **服务类型**: HTTP API 网关服务 (Apache ShenYu)
- **主要端口**:
  - HTTP 服务端口: `8081` (对外提供API服务)
  - 管理端口: `8081` (健康检查和监控)

### 核心功能

- **API网关**: 统一入口，处理所有外部HTTP请求
- **服务路由**: 将HTTP请求路由到对应的后端Dubbo服务
- **负载均衡**: 支持多种负载均衡策略
- **跨域处理**: 统一处理CORS跨域请求
- **认证授权**: 统一的身份认证和权限控制
- **限流熔断**: 保护后端服务，防止过载
- **监控日志**: 请求链路追踪和监控

### API路径规划

#### C端用户API (需要通过Ingress暴露)
#### 临时参考
```
/user-api/**           # 用户相关API
/merchant-api/**       # 商户相关API  
/order-api/**          # 订单相关API
/review-api/**         # 评价相关API
/reservation-api/**    # 预约相关API
/file-api/**           # 文件上传下载API
```

#### B端管理API (需要通过Ingress暴露)
```
/admin-api/**          # 管理后台API
```

#### 系统API (内部使用)
```
/health               # 健康检查
/info                 # 服务信息
/actuator/**          # Spring Boot Actuator监控端点
```

### 健康检查配置

#### 1. Spring Boot Actuator 健康检查（推荐）
项目已集成Spring Boot Actuator，提供完整的健康检查功能：

```yaml
# K8s健康检查配置
livenessProbe:
  httpGet:
    path: /actuator/health/liveness
    port: 8081
  initialDelaySeconds: 60
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

readinessProbe:
  httpGet:
    path: /actuator/health/readiness
    port: 8081
  initialDelaySeconds: 30
  periodSeconds: 5
  timeoutSeconds: 3
  failureThreshold: 3
```

#### 2. 自定义健康检查端点（备选方案）
```yaml
# 使用自定义健康检查端点
livenessProbe:
  httpGet:
    path: /health
    port: 8081
  initialDelaySeconds: 60
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

readinessProbe:
  httpGet:
    path: /health
    port: 8081
  initialDelaySeconds: 30
  periodSeconds: 5
  timeoutSeconds: 3
  failureThreshold: 3
```

### Service 配置

```yaml
apiVersion: v1
kind: Service
metadata:
  name: gateway-app
  namespace: yuanchuan
  labels:
    app: gateway-app
spec:
  selector:
    app: gateway-app
  ports:
  - name: http
    port: 8081
    targetPort: 8081
    protocol: TCP
  type: ClusterIP
```

### Deployment 配置示例

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gateway-app
  namespace: yuanchuan
spec:
  replicas: 3
  selector:
    matchLabels:
      app: gateway-app
  template:
    metadata:
      labels:
        app: gateway-app
    spec:
      containers:
      - name: gateway-app
        image: gateway-app:latest
        ports:
        - containerPort: 8081
          name: http
        # Spring Boot Actuator健康检查（推荐）
        livenessProbe:
          httpGet:
            path: /actuator/health/liveness
            port: 8081
          initialDelaySeconds: 90
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8081
          initialDelaySeconds: 60
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        env:
        - name: SERVER_PORT
          value: "8081"
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: SHENYU_ADMIN_URL
          value: "ws://shenyu-admin:9095/websocket"
        - name: ZOOKEEPER_ADDRESS
          value: "zookeeper-service:2181"
        - name: REDIS_HOST
          value: "redis-service"
        - name: REDIS_PORT
          value: "6379"
        - name: CONFIG_SERVER_URI
          value: "http://config-server:8888"
```

### Ingress 配置

#### 1. C端用户API Ingress
```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gateway-app-user
  namespace: yuanchuan
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
spec:
  ingressClassName: nginx
  rules:
  - host: api.yuanchuan.com
    http:
      paths:
      # 用户相关API
      - path: /user-api/(.*)
        pathType: Prefix
        backend:
          service:
            name: gateway-app
            port:
              number: 8081
      # 商户相关API
      - path: /merchant-api/(.*)
        pathType: Prefix
        backend:
          service:
            name: gateway-app
            port:
              number: 8081
      # 订单相关API
      - path: /order-api/(.*)
        pathType: Prefix
        backend:
          service:
            name: gateway-app
            port:
              number: 8081
      # 评价相关API
      - path: /review-api/(.*)
        pathType: Prefix
        backend:
          service:
            name: gateway-app
            port:
              number: 8081
      # 预约相关API
      - path: /reservation-api/(.*)
        pathType: Prefix
        backend:
          service:
            name: gateway-app
            port:
              number: 8081
      # 文件相关API
      - path: /file-api/(.*)
        pathType: Prefix
        backend:
          service:
            name: gateway-app
            port:
              number: 8081
  tls:
  - hosts:
    - api.yuanchuan.com
    secretName: yuanchuan-api-tls
```

#### 2. B端管理API Ingress
```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gateway-app-admin
  namespace: yuanchuan
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$1
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    # 可选：添加IP白名单限制管理API访问
    # nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8,**********/12,***********/16"
spec:
  ingressClassName: nginx
  rules:
  - host: admin-api.yuanchuan.com
    http:
      paths:
      # 管理后台API
      - path: /admin-api/(.*)
        pathType: Prefix
        backend:
          service:
            name: gateway-app
            port:
              number: 8081
  tls:
  - hosts:
    - admin-api.yuanchuan.com
    secretName: yuanchuan-admin-api-tls
```

### HPA 自动扩缩容配置

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: gateway-app-hpa
  namespace: yuanchuan
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: gateway-app
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
```

### ConfigMap 配置

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: gateway-app-config
  namespace: yuanchuan
data:
  application.yml: |
    server:
      port: 8081
    
    spring:
      application:
        name: gateway-app
      profiles:
        active: ${SPRING_PROFILES_ACTIVE:prod}
    
    shenyu:
      sync:
        websocket:
          urls: ${SHENYU_ADMIN_URL:ws://shenyu-admin:9095/websocket}
      cross:
        enabled: true
      dubbo:
        parameter: multi
    
    dubbo:
      registry:
        address: ${ZOOKEEPER_ADDRESS:zookeeper://zookeeper-service:2181}
      application:
        name: gateway-app
        qos-enable: false
    
    management:
      endpoints:
        web:
          exposure:
            include: health,info,metrics,prometheus
      endpoint:
        health:
          probes:
            enabled: true
          show-details: always
      health:
        livenessstate:
          enabled: true
        readinessstate:
          enabled: true
```

### 监控和观测

#### 1. Prometheus 监控配置
```yaml
# ServiceMonitor for Prometheus
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: gateway-app-monitor
  namespace: yuanchuan
spec:
  selector:
    matchLabels:
      app: gateway-app
  endpoints:
  - port: http
    path: /actuator/prometheus
    interval: 30s
```

#### 2. 日志收集配置
```yaml
# 通过sidecar收集日志
volumes:
- name: logs
  emptyDir: {}
volumeMounts:
- name: logs
  mountPath: /app/logs
```

### 注意事项

1. **网关特性**: 作为API网关，需要通过Ingress暴露HTTP服务供外部访问
2. **健康检查**: 推荐使用Spring Boot Actuator的liveness和readiness探针
3. **跨域配置**: 已在应用层和Ingress层都配置了CORS支持
4. **负载均衡**: 通过K8s Service和ShenYu网关双重负载均衡
5. **安全考虑**: 
   - 管理API可通过独立域名和IP白名单限制访问
   - 支持TLS/SSL加密传输
   - 集成认证授权机制
6. **性能优化**: 
   - 配置合适的资源限制和HPA策略
   - 启用连接池和缓存机制
   - 监控关键指标如响应时间、吞吐量等
7. **依赖服务**: 
   - 依赖ShenYu Admin进行配置管理
   - 依赖ZooKeeper进行服务发现
   - 依赖Redis进行缓存和会话管理
   - 依赖Config Server进行配置管理

### 运维命令

```bash
# 查看网关状态
kubectl get pods -n yuanchuan -l app=gateway-app

# 查看服务日志
kubectl logs -n yuanchuan -l app=gateway-app -f

# 查看健康状态
curl http://gateway-app:8081/actuator/health

# 查看监控指标
curl http://gateway-app:8081/actuator/metrics

# 查看ShenYu网关配置
curl http://gateway-app:8081/actuator/shenyu

# 扩缩容操作
kubectl scale deployment gateway-app -n yuanchuan --replicas=5

# 滚动更新
kubectl rollout restart deployment gateway-app -n yuanchuan
kubectl rollout status deployment gateway-app -n yuanchuan
```

## 项目结构

```
gateway-app
├── src/main/java
│   └── com/example/gateway
│       ├── GatewayApplication.java
│       ├── config
│       │   ├── CorsConfig.java
│       │   └── DubboPluginConfig.java
│       ├── controller
│       │   └── HealthController.java
│       ├── exception
│       │   └── GlobalExceptionHandler.java
│       └── filter
│           └── LoggingFilter.java
├── src/main/resources
│   └── application.yml
└── pom.xml
```

## 技术栈

- Java 17
- Spring Boot 3.3.9
- Apache ShenYu 2.6.1
- Apache Dubbo 3.2.9
- Apache ZooKeeper 3.9.3

## 快速开始

### 前提条件

- JDK 17+
- Maven 3.6+
- ZooKeeper 3.5+
- ShenYu Admin 2.6.1

### 启动步骤

1. 确保ShenYu Admin已经启动，默认地址为：`http://localhost:9095`
2. 确保ZooKeeper已经启动，默认地址为：`localhost:2181`
3. 编译并启动网关应用：

```bash
mvn clean package
java -jar target/gateway-app-0.0.1-SNAPSHOT.jar
```

4. 网关启动成功后，可以通过`http://localhost:8081`访问

## 配置说明

### 网关配置

网关配置在`application.yml`文件中，主要包括：

- 服务端口：9195
- ShenYu Admin连接：ws://localhost:9095/websocket
- Dubbo注册中心：zookeeper://localhost:2181

### 插件配置

本网关已启用以下插件：

- Dubbo插件：用于处理Dubbo服务的请求转发
- 跨域插件：用于处理跨域请求

## 健康检查

- 健康检查端点：`http://localhost:9195/health`
- 网关信息端点：`http://localhost:9195/info`

