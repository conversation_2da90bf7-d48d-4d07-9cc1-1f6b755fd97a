# 使用 Alpine Linux 作為基礎映像
FROM alpine:3.18

# 安裝 OpenJDK 17 和 curl
RUN apk add --no-cache openjdk17-jre curl

# 設置工作目錄
WORKDIR /app

# 複製 JAR 檔案到容器中
COPY target/gateway-app-*.jar app.jar

# 暴露端口
EXPOSE 8081 22222

# 設置 JVM 參數（針對網關服務優化）
ENV JAVA_OPTS="-Xmx1024m -Xms512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"

# 健康檢查 - 使用 Spring Boot Actuator 健康端點
HEALTHCHECK --interval=30s --timeout=5s --start-period=90s --retries=3 \
  CMD curl -f http://localhost:8081/actuator/health || exit 1

# 啟動應用程序
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"] 