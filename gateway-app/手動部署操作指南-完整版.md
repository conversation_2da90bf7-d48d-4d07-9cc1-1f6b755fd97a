# Gateway 网关服务手動部署操作指南

## 步驟1：載入配置變數
```bash
source deploy-config.sh
show_config
```

## 步驟2：檢查必要工具
```bash
check_tools
```

## 步驟3：檢查Azure登錄狀態
```bash
check_azure_login
```

## 步驟4：檢查K8s集群現有服務
```bash
# 檢查現有deployment 管理Pod的控制器
kubectl get deployment gateway-app
# 刪除deployment（會自動刪除pods）
kubectl delete deployment gateway-app --ignore-not-found=true

# 檢查現有service 為Pod提供穩定網絡訪問的抽象層
kubectl get svc gateway-app
# 刪除service
kubectl delete svc gateway-app --ignore-not-found=true

# 檢查現有configmap
kubectl get configmap gateway-config
# 刪除configmap
kubectl delete configmap gateway-config --ignore-not-found=true

# 檢查現有pods Kubernetes中最小的部署單元
kubectl get pods -l app=gateway-app
# 删除
kubectl delete pods -l app=gateway-app
kubectl delete pods -l app=gateway-app --force --grace-period=0

# 確認清理完成
kubectl get all -l app=gateway-app
```

## 步驟5：構建Java應用
```bash
# 清理並構建
mvn clean package -DskipTests

# 檢查構建結果
ls -la target/gateway-app-*.jar
```

## 步驟6：構建Docker映像
```bash
# 使用ACR遠程構建
az acr build --registry $ACR_NAME --image $IMAGE_NAME:$IMAGE_TAG .

# 查看ACR中所有的倉庫(查看，非必须)
az acr repository list --name $ACR_NAME --output table

# 檢查映像是否成功推送
az acr repository show-tags --name $ACR_NAME --repository $IMAGE_NAME --output table
```

## 步驟7：連接K8s集群
```bash
# kubectl cluster-info 有信息后，后续可跳过
kubectl cluster-info

# 獲取集群憑證
az aks get-credentials --resource-group $RESOURCE_GROUP --name $AKS_CLUSTER --overwrite-existing

# 檢查集群連接
kubectl cluster-info

# 檢查節點狀態
kubectl get nodes
```

## 步驟8：應用ConfigMap（已有配置文件）
```bash
# 直接應用已有的ConfigMap配置
kubectl apply -f k8s/configmap.yaml

# 查看ConfigMap內容
kubectl get configmap gateway-config -o yaml
```

## 步驟9：應用Service（已有配置文件）
```bash
# 直接應用已有的Service配置（ClusterIP类型）
kubectl apply -f k8s/service.yaml

# 檢查Service狀態
kubectl get svc gateway-app
```

## 步驟10：應用Ingress（通過Application Gateway暴露）
```bash
# 直接應用已有的Ingress配置
kubectl apply -f k8s/ingress.yaml

# 檢查Ingress狀態
kubectl get ingress gateway-app-ingress

# 查看Ingress詳細信息
kubectl describe ingress gateway-app-ingress

# 檢查AGIC是否正在運行
kubectl get pods -n kube-system | grep ingress
```

## 步驟11：更新並應用Deployment
```bash
# 更新deployment.yaml中的鏡像標籤
sed -i.bak "s|image: frchacrdev.azurecr.io/gateway-app:.*|image: $ACR_LOGIN_SERVER/$IMAGE_NAME:$IMAGE_TAG|" k8s/deployment.yaml

# 應用deployment配置
kubectl apply -f k8s/deployment.yaml

# 檢查deployment狀態
kubectl get deployment gateway-app
```

## 步驟12：檢查部署狀態
```bash
# 檢查所有資源
kubectl get all -l app=gateway-app

# 檢查deployment狀態
kubectl get deployment gateway-app

# 檢查service狀態（顯示所有端口和外部IP）
kubectl get svc gateway-app -o wide

# 檢查configmap
kubectl get configmap gateway-config
```

## 步驟13：檢查Pod狀態
```bash
# 檢查pod狀態
kubectl get pods -l app=gateway-app -o wide

# 檢查pod詳細信息
kubectl describe pods -l app=gateway-app

# 檢查pod事件
kubectl get events --field-selector involvedObject.kind=Pod --sort-by='.lastTimestamp'
```

## 步驟14：檢查應用日誌
```bash
# 查看所有pod日誌
kubectl logs -l app=gateway-app --tail=50

# 實時查看日誌
kubectl logs -l app=gateway-app -f

# 查看特定pod日誌（如果需要）
kubectl logs deployment/gateway-app
```

## 步驟15：檢查服務健康狀態
```bash
# 等待pod ready
kubectl wait --for=condition=ready pod -l app=gateway-app --timeout=300s

# 檢查端點
kubectl get endpoints gateway-app

# 測試網關健康檢查
kubectl port-forward service/gateway-app 8081:8081 &
sleep 5
echo "檢查網關健康狀態："
curl -s http://localhost:8081/actuator/health
echo -e "\n檢查網關信息："
curl -s http://localhost:8081/actuator/info
pkill -f "kubectl port-forward"

# 如果启用了QoS，也可以检查
kubectl port-forward service/gateway-app 22222:22222 &
sleep 5
echo -e "\n檢查Dubbo QoS狀態："
curl -s http://localhost:22222/status || echo "QoS未启用或无法访问"
pkill -f "kubectl port-forward"
```

## 步驟16：獲取外部訪問地址
```bash
# 注意：现在使用Application Gateway，不再使用LoadBalancer的外部IP
# 检查Application Gateway的公网IP
echo "Application Gateway公网IP: ***********"

# 检查Ingress状态
kubectl get ingress gateway-app-ingress

# 等待Application Gateway配置更新
echo "等待Application Gateway配置更新..."
sleep 30

# 检查Application Gateway后端池配置
az network application-gateway show \
  --resource-group frch-rg-dev \
  --name frch-agw-dev \
  --query "backendAddressPools" \
  -o table

echo "网关访问地址: http://***********/"
echo "健康检查地址: http://***********/actuator/health"

# 基本連通性驗證
kubectl run test-gateway --image=busybox --rm -it --restart=Never -- nslookup gateway-app
```

## 步驟17：測試網關功能
```bash
# 檢查網關是否正常接收請求
GATEWAY_POD=$(kubectl get pods -l app=gateway-app -o jsonpath='{.items[0].metadata.name}')
if [ ! -z "$GATEWAY_POD" ]; then
  echo "測試網關Pod: $GATEWAY_POD"
  
  # 检查网关健康状态
  kubectl exec $GATEWAY_POD -- curl -s http://localhost:8081/actuator/health
  
  # 检查是否能访问ShenYu的默认端点
  kubectl exec $GATEWAY_POD -- curl -s http://localhost:8081/ || echo "网关根路径访问测试"
fi

# 確認健康檢查狀態
kubectl describe pods -l app=gateway-app | grep -A3 "Conditions:"
```

## 步驟18：最終驗證
```bash
# 檢查所有資源狀態
echo "=== Deployment Status ==="
kubectl get deployment gateway-app

echo "=== Pod Status ==="
kubectl get pods -l app=gateway-app

echo "=== Service Status (ClusterIP类型) ==="
kubectl get svc gateway-app -o wide

echo "=== Ingress Status ==="
kubectl get ingress gateway-app-ingress

echo "=== ConfigMap Status ==="
kubectl get configmap gateway-config

echo "=== 端口信息 ==="
echo "- 網關HTTP端口: 8081 (主要服務端口)"
echo "- Dubbo QoS端口: 22222 (健康檢查端口)"
echo "- 服務類型: ClusterIP (通过Ingress和Application Gateway暴露)"

echo "=== 外部訪問信息 ==="
echo "網關外部訪問地址: http://***********/"
echo "健康檢查地址: http://***********/actuator/health"
echo "通过Application Gateway (frch-agw-dev) 暴露服务"

echo "=== Recent Events ==="
kubectl get events --sort-by='.lastTimestamp' | tail -10

echo "=== 服務健康檢查 ==="
GATEWAY_POD=$(kubectl get pods -l app=gateway-app -o jsonpath='{.items[0].metadata.name}')
if [ ! -z "$GATEWAY_POD" ]; then
  echo "檢查Pod: $GATEWAY_POD"
  kubectl exec $GATEWAY_POD -- curl -s http://localhost:8081/actuator/health || echo "健康檢查失敗"
fi

echo "=== Application Gateway验证 ==="
echo "测试通过Application Gateway访问："
curl -H "Host: ***********" http://***********/actuator/health || echo "Application Gateway访问测试"
```

## 步驟19：清理測試資源（可選）
```bash
# 如果需要重新部署，執行清理
# kubectl delete -f k8s/
# 或使用腳本清理
# kubectl delete deployment gateway-app
# kubectl delete service gateway-app
# kubectl delete configmap gateway-config
```

## 快速部署命令（整合版）
```bash
# 一鍵部署（適用於重複部署）
source deploy-config.sh && deploy_app

# 或者分步驟執行
source deploy-config.sh
check_tools && check_azure_login
mvn clean package -DskipTests
az acr build --registry $ACR_NAME --image $IMAGE_NAME:$IMAGE_TAG .
az aks get-credentials --resource-group $RESOURCE_GROUP --name $AKS_CLUSTER --overwrite-existing
sed -i.bak "s|image: frchacrdev.azurecr.io/gateway-app:.*|image: $ACR_LOGIN_SERVER/$IMAGE_NAME:$IMAGE_TAG|" k8s/deployment.yaml
kubectl apply -f k8s/
kubectl rollout status deployment/gateway-app
kubectl get all -l app=gateway-app
kubectl get ingress gateway-app-ingress
```

## 故障排除
```bash
# 如果Pod啟動失敗
kubectl describe pod -l app=gateway-app
kubectl logs -l app=gateway-app --previous

# 如果健康檢查失敗
kubectl exec deployment/gateway-app -- curl -v http://localhost:8081/actuator/health

# 如果Application Gateway无法访问
kubectl get ingress gateway-app-ingress -o yaml
kubectl describe ingress gateway-app-ingress

# 检查AGIC Controller
kubectl get pods -n kube-system | grep ingress
kubectl logs -n kube-system -l app=ingress-appgw

# 检查Application Gateway配置
az network application-gateway show \
  --resource-group frch-rg-dev \
  --name frch-agw-dev \
  --query "backendAddressPools"

# 重新載入配置（如果ConfigMap修改）
kubectl patch configmap gateway-config --patch '
data:
  application.yml: |
    # 在這裡放置新的配置內容
'
kubectl rollout restart deployment gateway-app
```

## 注意事項
- 统一网关作为系统入口，提供HTTP API访问
- 网关端口：8081，用于接收外部HTTP请求
- **服務類型為ClusterIP，通过Ingress和Application Gateway暴露服务**
- **外部访问地址：http://***********/ (Application Gateway公网IP)**
- 健康檢查使用Spring Boot Actuator端點
- 网关类型为Apache ShenYu，支持动态路由和插件扩展
- Dubbo QoS用于监控Dubbo消费者连接状态
- 确保Zookeeper服务可用，因为网关需要通过Dubbo调用后端服务
- 网关会消费其他微服务，但自身不注册为Dubbo提供者
- **AGIC (Application Gateway Ingress Controller) 自动配置路由规则**
- **所有流量通过frch-agw-dev Application Gateway进入，符合企业网络架构要求**