apiVersion: apps/v1
kind: Deployment
metadata:
  name: gateway-app
  labels:
    app: gateway-app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gateway-app
  template:
    metadata:
      labels:
        app: gateway-app
    spec:
      containers:
      - name: gateway-app
        image: frchacrdev.azurecr.io/gateway-app:20250603141741
        ports:
        - containerPort: 8081
          name: http
        - containerPort: 22222
          name: qos
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "default"
        - name: SPRING_CONFIG_LOCATION
          value: "file:/app/config/application.yml"
        - name: JAVA_OPTS
          value: "-Xmx1024m -Xms512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+ExitOnOutOfMemoryError -Djava.awt.headless=true -Dfile.encoding=UTF-8"
        - name: TZ
          value: "Asia/Taipei"
        - name: JAVA_TOOL_OPTIONS
          value: "-Duser.timezone=Asia/Taipei"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1.5Gi"
            cpu: "1.5"
        lifecycle:
          preStop:
            exec:
              command: ["/bin/sh", "-c", "sleep 15"]
        # 使用实际API端点做健康检查
        livenessProbe:
          httpGet:
            path: /health
            port: 8081
          initialDelaySeconds: 120  # 给应用更多启动时间
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 5  # 增加失败容忍次数
        readinessProbe:
          httpGet:
            path: /health
            port: 8081
          initialDelaySeconds: 60   # 给应用足够的启动时间
          periodSeconds: 15
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: config-volume
        configMap:
          name: gateway-config
      terminationGracePeriodSeconds: 60
