apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gateway-app-ingress
  annotations:
    # AGIC (Application Gateway Ingress Controller) 注解
    # kubernetes.io/ingress.class: azure/application-gateway  # 废弃，改用spec.ingressClassName
    # 配置Application Gateway的后端协议
    appgw.ingress.kubernetes.io/backend-protocol: "http"
    # 使用实际的API端点做健康检查
    appgw.ingress.kubernetes.io/health-probe-path: "/health"
    # 配置连接超时
    appgw.ingress.kubernetes.io/connection-draining-timeout: "60"
    # 配置请求超时
    appgw.ingress.kubernetes.io/request-timeout: "60"
    # 设置优先级确保gateway-app的路由规则优先于hello-ingress
    appgw.ingress.kubernetes.io/rule-priority: "1000"
spec:
  # 使用现代的ingressClassName字段
  ingressClassName: azure-application-gateway
  rules:
    # 不指定host，匹配所有请求（可以通过IP访问）
    - http:
        paths:
          # 明确声明/api路径归gateway-app处理
          - path: /api
            pathType: Prefix
            backend:
              service:
                name: gateway-app
                port:
                  number: 8081
          # 明确声明/merchant-api路径归gateway-app处理
          - path: /merchant-api
            pathType: Prefix
            backend:
              service:
                name: gateway-app
                port:
                  number: 8081
          # 明确声明/admin-api路径归gateway-app处理
          - path: /admin-api
            pathType: Prefix
            backend:
              service:
                name: gateway-app
                port:
                  number: 8081
          # 明确声明/merchant 路径归gateway-app处理
          - path: /merchant
            pathType: Prefix
            backend:
              service:
                name: gateway-app
                port:
                  number: 8081
          - path: /admin
            pathType: Prefix
            backend:
              service:
                name: gateway-app
                port:
                  number: 8081
          # 默认路径处理
          - path: /
            pathType: Prefix
            backend:
              service:
                name: gateway-app
                port:
                  number: 8081
    # 可选：如果有域名的话
    - host: "gateway.frch.dev"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: gateway-app
                port:
                  number: 8081