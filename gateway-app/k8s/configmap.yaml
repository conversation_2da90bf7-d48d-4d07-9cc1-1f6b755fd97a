apiVersion: v1
kind: ConfigMap
metadata:
  name: gateway-config
  namespace: default
data:
  application.yml: |
    server:
      port: 8081
      shutdown: graceful

    spring:
      application:
        name: gateway-app
      main:
        allow-bean-definition-overriding: true
      lifecycle:
        timeout-per-shutdown-phase: 30s
      data:
        redis:
          host: frch-redis-dev.redis.cache.windows.net
          port: 6380
          username: default
          password: fX9W6BYENhvxUfaqRIV4rAlyvHVkKhvvPAzCaMMMpWk=
          database: 0
          ssl:
            enabled: true
          timeout: 10000
          lettuce:
            pool:
              max-active: 8
              max-wait: -1
              max-idle: 8
              min-idle: 0
            shutdown-timeout: 100ms

    # ShenYu网关配置
    shenyu:
      sync:
        websocket:
          urls: ws://shenyu-admin:9095/websocket
          allow-origin: "*"
      cross:
        enabled: true
        allow-origin: "*"
      exclude:
        enabled: true
        paths:
          - /doc.html
          - /doc.html/**
          - /v3/api-docs
          - /v3/api-docs/**
          - /v3/api-docs/swagger-config
          - /swagger-ui.html
          - /swagger-ui/**
          - /swagger-resources
          - /swagger-resources/**
          - /webjars/**
          - /knife4j/**
          - /favicon.ico

    # Dubbo配置优化
    dubbo:
      application:
        name: gateway-app
        # 优雅关闭配置
        shutdown-wait-time: 15000
        shutdown-timeout: 30000
      protocol:
        name: dubbo
        port: -1
        host: 0.0.0.0
      registry:
        address: zookeeper://zookeeper-0.zookeeper.default.svc.cluster.local:2181,zookeeper-1.zookeeper.default.svc.cluster.local:2181
        timeout: 60000
        # 注册中心配置优化
        session-timeout: 180000
        connection-timeout: 30000
        retry-times: 3
        retry-interval: 5000
        # 优雅关闭时清理配置
        register: false
        block-until-connected-wait: 60000
        enable-empty-protection: false
        file-cache: true
        check: false
      consumer:
        timeout: 60000
        retries: 2
        loadbalance: random
        check: false
      provider:
        timeout: 60000
        retries: 0
        loadbalance: random
      # QoS配置
      qos:
        enable: true
        port: 22222
        accept-foreign-ip: false
        # 添加更严格的QoS配置
        anonymous-access-allow: false
        accept-foreign-ip-whitelist: 127.0.0.1,10.0.0.0/8
      # 关闭日志优化
      shutdown:
        graceful: true
        timeout: 30000

    # 日志配置 - 抑制QoS连接错误
    logging:
      level:
        org.apache.curator.framework.recipes.cache.NodeCache: WARN
        org.apache.zookeeper.ClientCnxn: WARN
        org.apache.dubbo: INFO
        org.apache.shenyu: INFO
        # 抑制QoS连接重置错误
        io.netty.channel.DefaultChannelPipeline: ERROR
        org.apache.dubbo.qos: WARN
      pattern:
        console: "%d{yyyy-MM-dd HH:mm:ss.SSS} %5level %logger{36} - %msg%n"

    # 管理端点配置
    management:
      endpoints:
        web:
          exposure:
            include: health,info,prometheus
      endpoint:
        health:
          show-details: always
      health:
        redis:
          enabled: true
    authentication:
      callbackDomain: http://dev.goldenmilestech.com

    # dubbo 消费者group
    dubbo.consumer.group.user: user-dev
    dubbo.consumer.group.merchant: merchant-dev
    dubbo.consumer.group.order: order-dev
    dubbo.consumer.group.reservation: reservation-dev
    dubbo.consumer.group.review: review-dev
    dubbo.consumer.group.marketing: marketing-dev
    dubbo.consumer.group.file: file-dev
    dubbo.consumer.group.authentication: authentication-dev
    dubbo.consumer.group.location: location-dev