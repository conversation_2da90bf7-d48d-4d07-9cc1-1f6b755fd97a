# 运营权限管理接口文档

## 文档信息
- **版本**：1.0
- **作者**：系统设计团队
- **日期**：2025-01-27
- **状态**：正式版

## 1. 概述

本文档定义了运营权限管理相关的API接口，包括权限的查询、创建、更新、删除等功能。这些接口主要用于运营后台管理系统，提供完整的权限管理能力。

## 2. 接口基础信息

### 2.1 基础路径
所有权限管理接口的基础路径为：`/api/permissions`

### 2.2 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

**说明**：
- `code`：响应状态码，200表示成功
- `message`：响应消息，success表示成功
- `data`：响应数据，具体结构根据接口而定

## 3. 接口列表

### 3.1 根据账户ID查询用户权限树

- **接口路径**：`POST /api/permissions/user/{accountId}`
- **功能描述**：查询指定账户的权限树结构，用于展示用户拥有的权限层级关系
- **请求参数**：
  - 路径参数：
    - `accountId`：账户ID，Long类型，必填
- **响应结果**：
  - 成功：`200 OK`，返回`List<PermissionTreeDTO>`
  - 失败：
    - `400 Bad Request`：请求参数错误
    - `404 Not Found`：账户不存在
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要管理员认证
- **请求示例**：

```http
POST /api/permissions/user/123456 HTTP/1.1
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "permissionId": 1,
      "permissionCode": "SYSTEM_MANAGE",
      "type": "MENU",
      "permissionName": "系统管理",
      "url": "/system",
      "children": [
        {
          "permissionId": 2,
          "permissionCode": "USER_MANAGE",
          "type": "PAGE",
          "permissionName": "用户管理",
          "url": "/system/user",
          "children": []
        }
      ]
    }
  ]
}
```

### 3.2 分页查询权限列表

- **接口路径**：`POST /api/permissions/list`
- **功能描述**：根据条件分页查询权限列表，支持按权限入口、状态、关键词等条件筛选
- **请求参数**：
  - 请求体：`PermissionQueryRequest`
    - `permissionMenu`：权限入口，字符串，可选
    - `status`：权限状态，整数，可选，0-禁用，1-启用
    - `keyword`：关键词搜索，字符串，可选，支持权限名称或权限编码搜索
    - `pageNo`：页码，整数，必填，从1开始
    - `pageSize`：每页大小，整数，必填，最大100
- **响应结果**：
  - 成功：`200 OK`，返回`PageResult<PermissionDTO>`
  - 失败：
    - `400 Bad Request`：请求参数错误
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要管理员认证
- **请求示例**：

```http
POST /api/permissions/list HTTP/1.1
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "permissionMenu": "系统管理",
  "status": 1,
  "keyword": "用户",
  "pageNo": 1,
  "pageSize": 10
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "permissionId": 1,
        "permissionMenu": "系统管理",
        "permissionName": "用户管理",
        "permissionCode": "USER_MANAGE",
        "description": "用户管理权限",
        "roleNames": "管理员,运营",
        "roleIds": [1, 2],
        "status": 1,
        "updatedAt": "2025-01-27T10:30:00",
        "updatedBy": "admin"
      }
    ],
    "total": 1,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

### 3.3 获取权限入口下拉列表

- **接口路径**：`GET /api/permissions/entries`
- **功能描述**：获取类型为MENU的权限作为入口选项，用于权限筛选的下拉列表
- **请求参数**：无
- **响应结果**：
  - 成功：`200 OK`，返回`List<PermissionSelectDTO>`
  - 失败：
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要管理员认证
- **请求示例**：

```http
GET /api/permissions/entries HTTP/1.1
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "permissionName": "系统管理",
      "permissionCode": "SYSTEM_MANAGE"
    },
    {
      "id": 2,
      "permissionName": "商户管理",
      "permissionCode": "MERCHANT_MANAGE"
    }
  ]
}
```

### 3.4 创建权限

- **接口路径**：`POST /api/permissions/createPermission`
- **功能描述**：创建新的权限，支持创建菜单、页面、按钮、接口等类型的权限
- **请求参数**：
  - 请求体：`PermissionCreateRequest`
    - `permissionName`：权限名称，字符串，必填，最大40个字符
    - `description`：权限说明，字符串，可选，最大200个字符
    - `permissionCode`：权限Key值，字符串，必填，最大200个字符
    - `type`：权限类型，字符串，可选，MENU-菜单, PAGE-页面, BUTTON-按钮, API-接口
    - `parentId`：父权限ID，Long类型，可选
    - `url`：页面路径或API路径，字符串，可选
    - `httpMethod`：HTTP方法，字符串，可选
    - `icon`：菜单图标，字符串，可选
    - `componentPath`：组件路径，字符串，可选
    - `orderNum`：排序字段，整数，可选
    - `isVisible`：是否可见，整数，可选，0-隐藏，1-显示
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：请求参数错误或权限代码已存在
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要管理员认证
- **请求示例**：

```http
POST /api/permissions/createPermission HTTP/1.1
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "permissionName": "订单管理",
  "description": "订单管理相关权限",
  "permissionCode": "ORDER_MANAGE",
  "type": "MENU",
  "parentId": null,
  "url": "/order",
  "icon": "order-icon",
  "orderNum": 3,
  "isVisible": 1
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.5 更新权限

- **接口路径**：`POST /api/permissions/updatePermission`
- **功能描述**：更新权限信息，包括权限名称、描述、编码、状态等
- **请求参数**：
  - 请求体：`PermissionUpdateRequest`
    - `id`：权限ID，Long类型，必填
    - `permissionName`：权限名称，字符串，可选，最大40个字符
    - `description`：权限说明，字符串，可选，最大200个字符
    - `permissionCode`：权限Key值，字符串，可选，最大200个字符
    - `status`：权限状态，整数，可选，0-禁用，1-启用
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：请求参数错误或权限不存在
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要管理员认证
- **请求示例**：

```http
POST /api/permissions/updatePermission HTTP/1.1
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "id": 123,
  "permissionName": "订单管理（更新）",
  "description": "订单管理相关权限（已更新）",
  "permissionCode": "ORDER_MANAGE_V2",
  "status": 1
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.6 根据ID查询权限详情

- **接口路径**：`GET /api/permissions/getPermissionById/{id}`
- **功能描述**：查询指定ID的权限详细信息
- **请求参数**：
  - 路径参数：
    - `id`：权限ID，Long类型，必填
- **响应结果**：
  - 成功：`200 OK`，返回`PermissionDTO`
  - 失败：
    - `400 Bad Request`：请求参数错误
    - `404 Not Found`：权限不存在
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要管理员认证
- **请求示例**：

```http
GET /api/permissions/getPermissionById/123 HTTP/1.1
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "permissionId": 123,
    "permissionMenu": "系统管理",
    "permissionName": "用户管理",
    "permissionCode": "USER_MANAGE",
    "description": "用户管理权限",
    "roleNames": "管理员,运营",
    "roleIds": [1, 2],
    "status": 1,
    "updatedAt": "2025-01-27T10:30:00",
    "updatedBy": "admin"
  }
}
```

### 3.7 删除权限

- **接口路径**：`DELETE /api/permissions/deletePermission/{id}`
- **功能描述**：删除指定ID的权限
- **请求参数**：
  - 路径参数：
    - `id`：权限ID，Long类型，必填
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：请求参数错误或权限正在使用中
    - `404 Not Found`：权限不存在
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要管理员认证
- **请求示例**：

```http
DELETE /api/permissions/deletePermission/123 HTTP/1.1
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

## 4. 数据模型

### 4.1 请求模型

#### 4.1.1 PermissionQueryRequest

```java
@Data
@Schema(description = "权限查询请求")
public class PermissionQueryRequest extends PageQueryV {
    @Schema(description = "权限入口")
    private String permissionMenu;

    @Schema(description = "权限状态：0-禁用，1-启用")
    private Integer status;

    @Schema(description = "关键词搜索（权限名称或权限编码）")
    private String keyword;

    @Schema(description = "页码，从1开始")
    private Integer pageNo;

    @Schema(description = "每页大小，最大100")
    private Integer pageSize;
}
```

#### 4.1.2 PermissionCreateRequest

```java
@Data
@Schema(description = "权限创建请求")
public class PermissionCreateRequest {
    @NotBlank(message = "權限名稱不能為空")
    @Size(max = 40, message = "權限名稱不能超過 40 個字元")
    @Schema(description = "权限名称", required = true)
    private String permissionName;

    @Size(max = 200, message = "權限說明不能超過 200 個字元")
    @Schema(description = "权限说明")
    private String description;

    @NotBlank(message = "權限 Key 值不能為空")
    @Size(max = 200, message = "權限 Key 值不能超過 200 個字元")
    @Schema(description = "权限Key值", required = true)
    private String permissionCode;

    @Schema(description = "权限类型：MENU-菜单, PAGE-页面, BUTTON-按钮, API-接口")
    private String type;

    @Schema(description = "父权限ID")
    private Long parentId;

    @Schema(description = "页面路径或API路径")
    private String url;

    @Schema(description = "HTTP方法")
    private String httpMethod;

    @Schema(description = "菜单图标")
    private String icon;

    @Schema(description = "组件路径")
    private String componentPath;

    @Schema(description = "排序字段")
    private Integer orderNum;

    @Schema(description = "是否可见：0-隐藏，1-显示")
    private Integer isVisible;
}
```

#### 4.1.3 PermissionUpdateRequest

```java
@Data
@Schema(description = "权限更新请求")
public class PermissionUpdateRequest {
    @NotNull(message = "權限ID不能為空")
    @Schema(description = "权限ID", required = true)
    private Long id;

    @Size(max = 40, message = "權限名稱不能超過 40 個字元")
    @Schema(description = "权限名称")
    private String permissionName;

    @Size(max = 200, message = "權限說明不能超過 200 個字元")
    @Schema(description = "权限说明")
    private String description;

    @Size(max = 200, message = "權限Key值不能超過 200 個字元")
    @Schema(description = "权限Key值")
    private String permissionCode;

    @Schema(description = "权限状态：0-禁用，1-启用")
    private Integer status;
}
```

### 4.2 响应模型

#### 4.2.1 PermissionDTO

```java
@Data
@Schema(description = "权限DTO")
public class PermissionDTO {
    @Schema(description = "权限ID")
    private Long permissionId;

    @Schema(description = "权限入口")
    private String permissionMenu;

    @Schema(description = "权限名称")
    private String permissionName;

    @Schema(description = "权限编码")
    private String permissionCode;

    @Schema(description = "权限描述")
    private String description;

    @Schema(description = "关联角色列表")
    private String roleNames;

    @Schema(description = "关联角色ID列表")
    private List<Long> roleIds;

    @Schema(description = "权限状态：0-禁用，1-启用")
    private Integer status;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Schema(description = "更新人")
    private String updatedBy;
}
```

#### 4.2.2 PermissionTreeDTO

```java
@Data
@Schema(description = "权限树形结构DTO")
public class PermissionTreeDTO {
    @Schema(description = "权限ID")
    private Long permissionId;

    @Schema(description = "权限编码")
    private String permissionCode;

    @Schema(description = "权限类型：MENU-菜单, PAGE-页面, BUTTON-按钮, API-接口")
    private String type;

    @Schema(description = "权限名称")
    private String permissionName;

    @Schema(description = "页面路径或API路径")
    private String url;

    @Schema(description = "子权限列表")
    private List<PermissionTreeDTO> children;
}
```

#### 4.2.3 PermissionSelectDTO

```java
@Data
@Schema(description = "权限入口下拉选择DTO")
public class PermissionSelectDTO {
    @Schema(description = "权限ID")
    private Long id;

    @Schema(description = "权限名称")
    private String permissionName;

    @Schema(description = "权限编码")
    private String permissionCode;
}
```

#### 4.2.4 PageResult

```java
@Data
@Schema(description = "分页结果")
public class PageResult<T> {
    @Schema(description = "数据列表")
    private List<T> records;

    @Schema(description = "总记录数")
    private Long total;

    @Schema(description = "当前页码")
    private Integer pageNum;

    @Schema(description = "每页大小")
    private Integer pageSize;
}
```

## 5. 错误码

| 错误码 | 错误消息 | 描述 |
|-------|---------|------|
| PERMISSION_NOT_FOUND | 权限不存在 | 指定的权限ID不存在 |
| PERMISSION_CODE_EXISTS | 权限编码已存在 | 创建权限时使用的编码已被占用 |
| PERMISSION_IN_USE | 权限正在使用中 | 删除权限时，该权限正被角色使用 |
| INVALID_PERMISSION_TYPE | 无效的权限类型 | 权限类型必须是MENU、PAGE、BUTTON或API |
| PERMISSION_NAME_REQUIRED | 权限名称不能为空 | 创建或更新权限时权限名称为空 |
| PERMISSION_CODE_REQUIRED | 权限编码不能为空 | 创建权限时权限编码为空 |
| PERMISSION_NAME_TOO_LONG | 权限名称过长 | 权限名称超过40个字符 |
| PERMISSION_CODE_TOO_LONG | 权限编码过长 | 权限编码超过200个字符 |
| PERMISSION_DESC_TOO_LONG | 权限描述过长 | 权限描述超过200个字符 |
| ACCOUNT_NOT_FOUND | 账户不存在 | 查询权限树时指定的账户ID不存在 |
| UNAUTHORIZED | 未授权访问 | 访问接口时缺少有效的认证信息 |
| FORBIDDEN | 权限不足 | 当前用户没有执行该操作的权限 |

## 变更历史
| 版本 | 日期 | 作者 | 变更描述 | 关联功能/需求 |
|-----|------|------|---------|-------------|
| 1.0 | 2025-01-27 | 系统设计团队 | 初始版本 | 运营权限管理功能 |