package com.yuanchuan.merchant.management.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 商户DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MerchantDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户类型
     */
    private String merchantType;

    /**
     * 商户状态
     */
    private String merchantStatus;

    /**
     * 商户地址
     */
    private String merchantAddress;

    /**
     * 商户电话
     */
    private String merchantPhone;

    /**
     * 商户邮箱
     */
    private String merchantEmail;

    /**
     * 商户网站
     */
    private String merchantWebsite;

    /**
     * 商户描述
     */
    private String merchantDescription;

    /**
     * 营业执照号
     */
    private String businessLicenseNo;

    /**
     * 营业执照图片
     */
    private String businessLicenseImage;

    /**
     * 法定代表人姓名
     */
    private String legalRepresentative;

    /**
     * 法定代表人身份证号
     */
    private String legalIdCardNo;

    /**
     * 法定代表人手机号
     */
    private String legalPhone;

    /**
     * 负责人姓名
     */
    private String businessOwner;

    /**
     * 负责人身份证号
     */
    private String businessOwnerIdCardNo;

    /**
     * 负责人身份证正面照片
     */
    private String businessOwnerIdCardFront;

    /**
     * 负责人身份证背面照片
     */
    private String businessOwnerIdCardBack;

    /**
     * 负责人手机号
     */
    private String businessOwnerPhone;

    /**
     * 食品业者登记字号
     */
    private String foodBusinessRegistrationNo;

    /**
     * 默认品牌ID
     */
    private String defaultBrandId;

    /**
     * 默认品牌名称
     */
    private String defaultBrandName;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 创建人
     */
    private Long createdBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 更新人
     */
    private Long updatedBy;
}
