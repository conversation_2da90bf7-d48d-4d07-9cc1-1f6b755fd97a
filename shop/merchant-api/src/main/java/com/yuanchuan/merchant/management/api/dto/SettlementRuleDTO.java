package com.yuanchuan.merchant.management.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 结算规则DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SettlementRuleDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 规则ID
     */
    private String ruleId;
    
    /**
     * 账户ID
     */
    private String accountId;
    
    /**
     * 结算周期类型
     */
    private String cycleType;
    
    /**
     * 结算周期值
     */
    private Integer cycleValue;
    
    /**
     * 结算方式
     */
    private String settlementMethod;
    
    /**
     * 手续费率
     */
    private BigDecimal feeRate;
    
    /**
     * 最低手续费
     */
    private BigDecimal minFee;
    
    /**
     * 最高手续费
     */
    private BigDecimal maxFee;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 创建人
     */
    private Long createdBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 更新人
     */
    private Long updatedBy;
}
