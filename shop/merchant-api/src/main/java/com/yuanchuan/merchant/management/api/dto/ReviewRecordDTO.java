package com.yuanchuan.merchant.management.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 审核记录DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReviewRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 审核ID
     */
    private Long reviewId;

    /**
     * 申请ID
     */
    private Long applicationId;

    /**
     * 审核状态
     */
    private String reviewStatus;

    /**
     * 审核拒绝回退到:
     */
    private List<String> backStep;

    /**
     * 审核意见
     */
    private String reviewComment;

    /**
     * 审核人
     */
    private Long reviewer;

    /**
     * 审核时间
     */
    private LocalDateTime reviewedAt;
}
