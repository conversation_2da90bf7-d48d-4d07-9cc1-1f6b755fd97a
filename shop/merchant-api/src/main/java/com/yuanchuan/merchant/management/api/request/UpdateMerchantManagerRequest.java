package com.yuanchuan.merchant.management.api.request;

import com.yuanchuan.merchant.management.api.dto.PermissionDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 更新商户管理员请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateMerchantManagerRequest implements Serializable {

    /**
     * ID
     */
    private Long id;

    /**
     * 角色
     */
    private String role;

    /**
     * 状态
     */
    private String status;

    /**
     * 权限列表
     */
    private List<PermissionDTO> permissions;

    /**
     * 操作人
     */
    private String operator;
}
