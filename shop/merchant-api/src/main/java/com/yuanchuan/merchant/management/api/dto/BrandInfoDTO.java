package com.yuanchuan.merchant.management.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 品牌信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BrandInfoDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 品牌名称
     */
    private String brandName;
    
    /**
     * 品牌Logo URL
     */
    private String logoUrl;
    
    /**
     * 品牌描述
     */
    private String description;
    
    /**
     * 品牌官网
     */
    private String website;
    
    /**
     * 品牌故事
     */
    private String brandStory;
    
    /**
     * 品牌创立年份
     */
    private Integer foundingYear;
    
    /**
     * 品牌创始人
     */
    private String founder;
    
    /**
     * 品牌所在国家
     */
    private String country;
    
    /**
     * 品牌标语
     */
    private String slogan;
}
