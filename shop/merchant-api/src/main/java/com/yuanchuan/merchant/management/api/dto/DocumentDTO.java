package com.yuanchuan.merchant.management.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 证明材料DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 文档ID
     */
    private String documentId;
    
    /**
     * 申请ID
     */
    private Long applicationId;
    
    /**
     * 文档类型
     */
    private String documentType;
    
    /**
     * 文档名称
     */
    private String documentName;
    
    /**
     * 文档URL
     */
    private String documentUrl;
    
    /**
     * 文档描述
     */
    private String description;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 创建人
     */
    private Long createdBy;
}
