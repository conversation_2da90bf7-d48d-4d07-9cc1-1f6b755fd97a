package com.yuanchuan.merchant.management.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 更新结算账户请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateSettlementAccountRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 账户ID
     */
    @NotBlank(message = "账户ID不能为空")
    private String accountId;
    
    /**
     * 账户名称
     */
    private String accountName;
    
    /**
     * 账户类型
     */
    private String accountType;
    
    /**
     * 银行名称
     */
    private String bankName;
    
    /**
     * 银行账号
     */
    private String bankAccount;
    
    /**
     * 开户行
     */
    private String bankBranch;
    
    /**
     * 开户人
     */
    private String accountHolder;
    
    /**
     * 银行许可证号
     */
    private String bankLicenseNo;
    
    /**
     * 银行联行号
     */
    private String bankCode;
    
    /**
     * 银行卡照片
     */
    private String bankCardImage;
    
    /**
     * 开户许可证照片
     */
    private String bankLicenseImage;
    
    /**
     * 结算周期类型
     */
    private String cycleType;
    
    /**
     * 结算周期值
     */
    private Integer cycleValue;
    
    /**
     * 结算方式
     */
    private String settlementMethod;
    
    /**
     * 手续费率
     */
    private BigDecimal feeRate;
    
    /**
     * 最低手续费
     */
    private BigDecimal minFee;
    
    /**
     * 最高手续费
     */
    private BigDecimal maxFee;
    
    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private Long operator;
}
