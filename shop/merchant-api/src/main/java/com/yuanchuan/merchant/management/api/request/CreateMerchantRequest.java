package com.yuanchuan.merchant.management.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 创建商户请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateMerchantRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 商户基本信息
     */
    private String merchantName;
    
    /**
     * 商户类型
     */
    private String merchantType;
    
    /*
     * 营业执照号
     */
    private String businessLicenseNo;
    
    /**
     * 营业执照图片
     */
    private String businessLicenseImage;
    
    /**
     * 法定代表人
     */
    private String legalRepresentative;
    
    /**
     * 商户地址
     */
    private String merchantAddress;
    
    /**
     * 商户电话
     */
    private String merchantPhone;
    
    /**
     * 商户邮箱
     */
    private String merchantEmail;
    
    /**
     * 商户网站
     */
    private String merchantWebsite;
    
    /**
     * 商户描述
     */
    private String merchantDescription;
    
    /**
     * 联系人信息
     */
    @NotBlank(message = "联系人姓名不能为空")
    private String contactName;
    
    /**
     * 联系人电话
     */
    @NotBlank(message = "联系人电话不能为空")
    private String contactPhone;
    
    /**
     * 联系人邮箱
     */
    private String contactEmail;
    
    /**
     * 联系人职位
     */
    private String contactPosition;
    
    /**
     * 默认品牌信息
     */
    @NotBlank(message = "默认品牌名称不能为空")
    private String defaultBrandName;
    
    /**
     * 品牌描述
     */
    private String brandDescription;
    
    /**
     * 品牌Logo URL
     */
    private String brandLogoUrl;
    
    /**
     * 结算账户信息
     */
    private String accountName;
    
    /**
     * 账户类型
     */
    private String accountType;
    
    /**
     * 银行名称
     */
    private String bankName;
    
    /**
     * 银行账号
     */
    private String bankAccount;
    
    /**
     * 开户行
     */
    private String bankBranch;
    
    /**
     * 开户人
     */
    private String accountHolder;
    
    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private Long operator;
}
