package com.yuanchuan.merchant.management.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 账单DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 账单ID
     */
    private String billId;
    
    /**
     * 账户ID
     */
    private String accountId;
    
    /**
     * 商户ID
     */
    private Long merchantId;
    
    /**
     * 账单周期开始时间
     */
    private LocalDateTime billPeriodStart;
    
    /**
     * 账单周期结束时间
     */
    private LocalDateTime billPeriodEnd;
    
    /**
     * 账单金额
     */
    private BigDecimal billAmount;
    
    /**
     * 手续费
     */
    private BigDecimal fee;
    
    /**
     * 实际结算金额
     */
    private BigDecimal settlementAmount;
    
    /**
     * 账单状态
     */
    private String billStatus;
    
    /**
     * 结算时间
     */
    private LocalDateTime settlementTime;
    
    /**
     * 结算交易ID
     */
    private String settlementTransactionId;
    
    /**
     * 账单备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 创建人
     */
    private Long createdBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 更新人
     */
    private Long updatedBy;
}
