package com.yuanchuan.merchant.management.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 审批通过商户申请请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApproveMerchantApplicationRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 申请ID
     */
    @NotBlank(message = "申请ID不能为空")
    private Long applicationId;
    
    /**
     * 审核意见
     */
    private String reviewComment;
    
    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private Long operator;
}
