package com.yuanchuan.merchant.management.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 商户等级DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MerchantLevelDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 商户ID
     */
    private Long merchantId;
    
    /**
     * 商户等级
     */
    private String merchantLevel;
    
    /**
     * 商户等级名称
     */
    private String merchantLevelName;
    
    /**
     * 商户等级描述
     */
    private String merchantLevelDescription;
    
    /**
     * 商户等级积分
     */
    private Integer merchantLevelScore;
    
    /**
     * 商户等级折扣
     */
    private Double merchantLevelDiscount;
    
    /**
     * 商户等级有效期开始时间
     */
    private LocalDateTime validStartTime;
    
    /**
     * 商户等级有效期结束时间
     */
    private LocalDateTime validEndTime;
    
    /**
     * 是否永久有效
     */
    private Boolean isPermanent;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 更新人
     */
    private Long updatedBy;
}
