package com.yuanchuan.merchant.management.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 冻结商户请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FreezeMerchantRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 商户ID
     */
    @NotBlank(message = "商户ID不能为空")
    private Long merchantId;
    
    /**
     * 冻结原因
     */
    @NotBlank(message = "冻结原因不能为空")
    private String reason;
    
    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private Long operator;
}
