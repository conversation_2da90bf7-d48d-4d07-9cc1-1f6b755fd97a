package com.yuanchuan.merchant.management.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 设置默认品牌请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SetDefaultBrandRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 商户ID
     */
    @NotBlank(message = "商户ID不能为空")
    private Long merchantId;
    
    /**
     * 品牌ID
     */
    @NotBlank(message = "品牌ID不能为空")
    private Long brandId;
    
    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private Long operator;
}
