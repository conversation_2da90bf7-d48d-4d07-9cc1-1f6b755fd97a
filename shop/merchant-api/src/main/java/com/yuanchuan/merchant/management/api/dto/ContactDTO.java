package com.yuanchuan.merchant.management.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 联系人DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContactDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 联系人ID
     */
    private String contactId;
    
    /**
     * 商户ID
     */
    private Long merchantId;
    
    /**
     * 联系人姓名
     */
    private String contactName;
    
    /**
     * 联系人电话
     */
    private String contactPhone;
    
    /**
     * 联系人邮箱
     */
    private String contactEmail;
    
    /**
     * 联系人职位
     */
    private String contactPosition;
    
    /**
     * 是否主要联系人
     */
    private Boolean isPrimary;
}
