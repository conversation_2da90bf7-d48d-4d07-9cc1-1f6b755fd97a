package com.yuanchuan.merchant.management.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 地址DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddressDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 地址ID
     */
    private String addressId;
    
    /**
     * 关联ID（商户ID、店铺ID等）
     */
    private String relatedId;
    
    /**
     * 地址类型（商户地址、店铺地址等）
     */
    private String addressType;
    
    /**
     * 国家
     */
    private String country;
    
    /**
     * 省份
     */
    private String province;
    
    /**
     * 城市
     */
    private String city;
    
    /**
     * 区县
     */
    private String district;
    
    /**
     * 详细地址
     */
    private String detailAddress;
    
    /**
     * 邮政编码
     */
    private String postalCode;
    
    /**
     * 经度
     */
    private Double longitude;
    
    /**
     * 纬度
     */
    private Double latitude;
    
    /**
     * 是否默认地址
     */
    private Boolean isDefault;
}
