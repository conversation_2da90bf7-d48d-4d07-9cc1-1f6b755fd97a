package com.yuanchuan.merchant.management.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 创建结算账户请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateSettlementAccountRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 商户ID
     */
    @NotBlank(message = "商户ID不能为空")
    private Long merchantId;
    
    /**
     * 账户名称
     */
    @NotBlank(message = "账户名称不能为空")
    private String accountName;
    
    /**
     * 账户类型
     */
    @NotBlank(message = "账户类型不能为空")
    private String accountType;
    
    /**
     * 银行名称
     */
    @NotBlank(message = "银行名称不能为空")
    private String bankName;
    
    /**
     * 银行账号
     */
    @NotBlank(message = "银行账号不能为空")
    private String bankAccount;
    
    /**
     * 开户行
     */
    @NotBlank(message = "开户行不能为空")
    private String bankBranch;
    
    /**
     * 开户人
     */
    @NotBlank(message = "开户人不能为空")
    private String accountHolder;
    
    /**
     * 银行许可证号
     */
    private String bankLicenseNo;
    
    /**
     * 银行联行号
     */
    private String bankCode;
    
    /**
     * 银行卡照片
     */
    private String bankCardImage;
    
    /**
     * 开户许可证照片
     */
    private String bankLicenseImage;
    
    /**
     * 结算周期类型
     */
    @NotBlank(message = "结算周期类型不能为空")
    private String cycleType;
    
    /**
     * 结算周期值
     */
    @NotNull(message = "结算周期值不能为空")
    private Integer cycleValue;
    
    /**
     * 结算方式
     */
    @NotBlank(message = "结算方式不能为空")
    private String settlementMethod;
    
    /**
     * 手续费率
     */
    @NotNull(message = "手续费率不能为空")
    private BigDecimal feeRate;
    
    /**
     * 最低手续费
     */
    private BigDecimal minFee;
    
    /**
     * 最高手续费
     */
    private BigDecimal maxFee;
    
    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private Long operator;
}
