package com.yuanchuan.merchant.management.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 创建品牌请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateBrandRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 商户ID
     */
    @NotBlank(message = "商户ID不能为空")
    private Long merchantId;
    
    /**
     * 品牌名称
     */
    @NotBlank(message = "品牌名称不能为空")
    private String brandName;
    
    /**
     * 品牌描述
     */
    private String description;
    
    /**
     * 品牌Logo URL
     */
    private String logoUrl;
    
    /**
     * 品牌官网
     */
    private String website;
    
    /**
     * 品牌故事
     */
    private String brandStory;
    
    /**
     * 品牌创立年份
     */
    private Integer foundingYear;
    
    /**
     * 品牌创始人
     */
    private String founder;
    
    /**
     * 品牌所在国家
     */
    private String country;
    
    /**
     * 品牌标语
     */
    private String slogan;
    
    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private Long operator;
}
