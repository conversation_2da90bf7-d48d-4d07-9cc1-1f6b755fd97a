package com.yuanchuan.merchant.management.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 查询商户申请详情请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetMerchantApplicationDetailRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请ID
     */
    @NotNull(message = "申请ID不能为空")
    private Long applicationId;
}
