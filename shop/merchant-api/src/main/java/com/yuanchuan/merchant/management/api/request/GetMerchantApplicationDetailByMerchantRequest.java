package com.yuanchuan.merchant.management.api.request;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 查询商户申请详情请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetMerchantApplicationDetailByMerchantRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 申请ID
     */
    @NotBlank(message = "商户ID不能为空")
    private Long merchantId;
}
