package com.yuanchuan.merchant.management.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 更新结算账户响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateSettlementAccountResponse implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 账户ID
     */
    private String accountId;
    
    /**
     * 商户ID
     */
    private Long merchantId;
    
    /**
     * 账户名称
     */
    private String accountName;
    
    /**
     * 账户类型
     */
    private String accountType;
    
    /**
     * 账户状态
     */
    private String accountStatus;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 更新人
     */
    private Long updatedBy;
}
