package com.yuanchuan.merchant.management.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 查询商户详情请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetMerchantDetailRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 商户ID
     */
    @NotBlank(message = "商户ID不能为空")
    private Long merchantId;
}
