package com.yuanchuan.merchant.management.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 更新品牌响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateBrandResponse implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 品牌ID
     */
    private Long brandId;
    
    /**
     * 品牌名称
     */
    private String brandName;
    
    /**
     * 品牌状态
     */
    private String brandStatus;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 更新人
     */
    private Long updatedBy;
}
