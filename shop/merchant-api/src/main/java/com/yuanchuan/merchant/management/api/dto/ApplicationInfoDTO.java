package com.yuanchuan.merchant.management.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 申请信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplicationInfoDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 商户名称
     */
    private String merchantName;
    
    /**
     * 商户类型
     */
    private String merchantType;
    
    /**
     * 营业执照号
     */
    private String businessLicenseNo;
    
    /**
     * 营业执照图片
     */
    private String businessLicenseImage;
    
    /**
     * 法定代表人
     */
    private String legalRepresentative;
    
    /**
     * 联系人姓名
     */
    private String contactName;
    
    /**
     * 联系人电话
     */
    private String contactPhone;
    
    /**
     * 联系人邮箱
     */
    private String contactEmail;
    
    /**
     * 联系人职位
     */
    private String contactPosition;
    
    /**
     * 地址
     */
    private String address;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 证明材料列表
     */
    private List<DocumentDTO> documents;
}
