package com.yuanchuan.merchant.management.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 品牌DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BrandDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 品牌ID
     */
    private Long brandId;
    
    /**
     * 品牌名称
     */
    private String brandName;
    
    /**
     * 品牌状态
     */
    private String brandStatus;
    
    /**
     * 品牌Logo URL
     */
    private String logoUrl;
    
    /**
     * 品牌描述
     */
    private String description;
    
    /**
     * 所属商户ID
     */
    private Long merchantId;
    
    /**
     * 所属商户名称
     */
    private String merchantName;
    
    /**
     * 是否默认品牌
     */
    private Boolean isDefault;
    
    /**
     * 店铺数量
     */
    private Integer storeCount;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 创建人
     */
    private Long createdBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 更新人
     */
    private Long updatedBy;
}
