package com.yuanchuan.merchant.management.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 账户信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountInfoDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 账户名称
     */
    private String accountName;
    
    /**
     * 账户类型
     */
    private String accountType;
    
    /**
     * 银行名称
     */
    private String bankName;
    
    /**
     * 银行账号
     */
    private String bankAccount;
    
    /**
     * 开户行
     */
    private String bankBranch;
    
    /**
     * 开户人
     */
    private String accountHolder;
    
    /**
     * 银行许可证号
     */
    private String bankLicenseNo;
    
    /**
     * 银行联行号
     */
    private String bankCode;
    
    /**
     * 银行卡照片
     */
    private String bankCardImage;
    
    /**
     * 开户许可证照片
     */
    private String bankLicenseImage;
}
