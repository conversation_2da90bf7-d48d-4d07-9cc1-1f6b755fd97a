package com.yuanchuan.merchant.management.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易记录DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionRecordDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 交易ID
     */
    private String transactionId;
    
    /**
     * 账户ID
     */
    private String accountId;
    
    /**
     * 交易类型
     */
    private String transactionType;
    
    /**
     * 交易金额
     */
    private BigDecimal amount;
    
    /**
     * 交易后余额
     */
    private BigDecimal balance;
    
    /**
     * 交易时间
     */
    private LocalDateTime transactionTime;
    
    /**
     * 交易描述
     */
    private String description;
    
    /**
     * 关联ID
     */
    private String referenceId;
    
    /**
     * 关联类型
     */
    private String referenceType;
    
    /**
     * 交易状态
     */
    private String transactionStatus;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 创建人
     */
    private Long createdBy;
}
