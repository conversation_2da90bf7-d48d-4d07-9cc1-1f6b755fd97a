package com.yuanchuan.merchant.management.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 执行结算请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExecuteSettlementRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 账单ID
     */
    @NotBlank(message = "账单ID不能为空")
    private String billId;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private Long operator;
}
