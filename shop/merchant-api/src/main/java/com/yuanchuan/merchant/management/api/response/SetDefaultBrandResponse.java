package com.yuanchuan.merchant.management.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 设置默认品牌响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SetDefaultBrandResponse implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 商户ID
     */
    private Long merchantId;
    
    /**
     * 商户名称
     */
    private String merchantName;
    
    /**
     * 品牌ID
     */
    private Long brandId;
    
    /**
     * 品牌名称
     */
    private String brandName;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 更新人
     */
    private Long updatedBy;
}
