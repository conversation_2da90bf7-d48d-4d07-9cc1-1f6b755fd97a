package com.yuanchuan.merchant.management.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 提交商户申请响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubmitMerchantApplicationResponse implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 申请ID
     */
    private Long applicationId;
    
    /**
     * 商户名称
     */
    private String merchantName;
    
    /**
     * 商户类型
     */
    private String merchantType;
    
    /**
     * 申请状态
     */
    private String applicationStatus;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 创建人
     */
    private Long createdBy;
}
