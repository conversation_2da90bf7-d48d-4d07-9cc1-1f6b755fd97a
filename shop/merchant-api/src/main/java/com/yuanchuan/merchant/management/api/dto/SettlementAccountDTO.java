package com.yuanchuan.merchant.management.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 结算账户DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SettlementAccountDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 账户ID
     */
    private String accountId;
    
    /**
     * 商户ID
     */
    private Long merchantId;
    
    /**
     * 账户名称
     */
    private String accountName;
    
    /**
     * 账户类型
     */
    private String accountType;
    
    /**
     * 账户状态
     */
    private String accountStatus;
    
    /**
     * 银行名称
     */
    private String bankName;
    
    /**
     * 银行账号
     */
    private String bankAccount;
    
    /**
     * 开户行
     */
    private String bankBranch;
    
    /**
     * 开户人
     */
    private String accountHolder;
    
    /**
     * 账户余额
     */
    private BigDecimal balance;
    
    /**
     * 结算规则
     */
    private SettlementRuleDTO settlementRule;
    
    /**
     * 交易记录列表
     */
    private List<TransactionRecordDTO> transactions;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 创建人
     */
    private Long createdBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 更新人
     */
    private Long updatedBy;
}
