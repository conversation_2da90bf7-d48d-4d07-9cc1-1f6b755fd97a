package com.yuanchuan.merchant.management.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 拒绝商户申请请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RejectMerchantApplicationRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 申请ID
     */
    @NotBlank(message = "申请ID不能为空")
    private Long applicationId;
    
    /**
     * 拒绝原因
     */
    @NotBlank(message = "拒绝原因不能为空")
    private String rejectReason;
    
    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private Long operator;

    /**
     * 错误步骤
     */
    private List<String> errorStep;
}
