package com.yuanchuan.merchant.management.api.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PageMerchantApplicationsRequest implements Serializable {
    /**
     * 商户名称 like %%
     * 或 merchantId
     * 或 googlePlaceId
     */
    private String searchText;
    /**
     * 審核狀態
     */
    private String applicationStatus;
    /**
     * 分类 多选
     */
    private List<Long> categoryIds;
    /**
     * 当前分配人 id
     */
    private Long reviewer;

    private Integer pageNum = 1;
    private Integer pageSize = 10;
}