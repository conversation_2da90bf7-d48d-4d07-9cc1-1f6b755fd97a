package com.yuanchuan.merchant.management.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import com.yuanchuan.merchant.shop.api.dto.ShopApplicationDTO;

/**
 * 商户和店铺申请组合DTO
 * 用于联合查询商户申请和店铺申请
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MerchantShopApplicationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户申请ID
     */
    private Long applicationId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户类型
     */
    private String merchantType;

    /**
     * 营业执照号
     */
    private String businessLicenseNo;

    /**
     * 营业执照图片
     */
    private String businessLicenseImage;

    /**
     * 法定代表人姓名
     */
    private String legalRepresentative;

    /**
     * 法定代表人身份证号
     */
    private String legalIdCardNo;

    /**
     * 法定代表人手机号
     */
    private String legalPhone;

    /**
     * 负责人姓名
     */
    private String businessOwner;

    /**
     * 负责人身份证号
     */
    private String businessOwnerIdCardNo;

    /**
     * 负责人身份证正面照片
     */
    private String businessOwnerIdCardFront;

    /**
     * 负责人身份证背面照片
     */
    private String businessOwnerIdCardBack;

    /**
     * 负责人手机号
     */
    private String businessOwnerPhone;

    /**
     * 食品业者登记字号
     */
    private String foodBusinessRegistrationNo;

    /**
     * 申请状态
     */
    private String applicationStatus;

    /**
     * 申请人
     */
    private String applicant;

    /**
     * 审核记录
     */
    private ReviewRecordDTO reviewRecord;

    /**
     * 商户申请创建时间
     */
    private LocalDateTime merchantCreatedTime;

    /**
     * 商户申请更新时间
     */
    private LocalDateTime merchantUpdatedTime;

    /**
     * 店铺申请ID
     */
    private Long shopApplicationId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺描述
     */
    private String shopDescription;

    /**
     * 店铺类型
     */
    private String shopType;

    /**
     * 省份ID
     */
    private Long provinceId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区县ID
     */
    private Long districtId;

    /**
     * 区县名称
     */
    private String districtName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * Google POI ID
     */
    private String googlePoiId;

    /**
     * Google Place ID
     */
    private String googlePlaceId;

    /**
     * 店铺照片，JSON格式
     */
    private String photos;

    /**
     * 店铺分类，JSON格式
     */
    private List<Map<String, Object>> categories;

    /**
     * 店铺申请创建时间
     */
    private LocalDateTime shopCreatedTime;

    /**
     * 店铺申请更新时间
     */
    private LocalDateTime shopUpdatedTime;

    /**
     * 相似的店铺申请列表
     */
    private List<ShopApplicationDTO> similarShopApplications;
}
