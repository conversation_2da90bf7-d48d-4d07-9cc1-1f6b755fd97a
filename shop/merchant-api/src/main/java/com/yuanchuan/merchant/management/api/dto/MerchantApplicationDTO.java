package com.yuanchuan.merchant.management.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商户申请DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MerchantApplicationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请ID
     */
    private Long applicationId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户类型
     */
    private String merchantType;

    /**
     * 营业执照号
     */
    private String businessLicenseNo;

    /**
     * 营业执照图片
     */
    private String businessLicenseImage;

    /**
     * 法定代表人姓名
     */
    private String legalRepresentative;

    /**
     * 法定代表人身份证号
     */
    private String legalIdCardNo;


    /**
     * 法定代表人手机号
     */
    private String legalPhone;

    /**
     * 负责人姓名
     */
    private String businessOwner;

    /**
     * 负责人身份证号
     */
    private String businessOwnerIdCardNo;

    /**
     * 负责人身份证正面照片
     */
    private String businessOwnerIdCardFront;

    /**
     * 负责人身份证背面照片
     */
    private String businessOwnerIdCardBack;

    /**
     * 负责人手机号
     */
    private String businessOwnerPhone;

    /**
     * 食品业者登记字号
     */
    private String foodBusinessRegistrationNo;

    /**
     * 卫生许可证号
     */
    private String healthPermitNo;

    /**
     * 建筑物使用执照号
     */
    private String buildingUsagePermitNo;

    /**
     * 消防安全检查合格证明号
     */
    private String fireSafetyCertificateNo;

    /**
     * 公共意外责任保险单号
     */
    private String publicLiabilityInsuranceNo;

    /**
     * 食品安全卫生管理人员证书号
     */
    private String foodSafetyManagerCertificateNo;

    /**
     * 酒类销售许可证号
     */
    private String alcoholSalesPermitNo;

    /**
     * 环保许可证号
     */
    private String environmentalPermitNo;

    /**
     * 地址
     */
    private String address;

    /**
     * 描述
     */
    private String description;

    /**
     * 申请状态
     */
    private String applicationStatus;

    /**
     * 申请人
     */
    private String applicant;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核记录
     */
    private ReviewRecordDTO reviewRecord;

    /**
     * 证明材料列表（已废弃，证明材料已内嵌到主表）
     * @deprecated
     */
    @Deprecated
    private List<DocumentDTO> documents;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 创建人
     */
    private Long createdBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 更新人
     */
    private Long updatedBy;
}
