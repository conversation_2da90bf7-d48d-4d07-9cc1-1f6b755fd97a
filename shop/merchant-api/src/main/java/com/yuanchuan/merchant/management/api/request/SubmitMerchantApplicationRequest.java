package com.yuanchuan.merchant.management.api.request;

import com.yuanchuan.merchant.management.api.dto.DocumentDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 提交商户申请请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubmitMerchantApplicationRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户名称 == 默认为店铺名
     */
    private String merchantName;

    @NotBlank(message = "商户ID不能为空")
    private Long merchantId;

    /**
     * 商户类型 暂时默认为 ENTERPRISE
     */
    //@NotBlank(message = "商户类型不能为空")
    private String merchantType;

    /**
     * 营业执照号
     */
    @NotBlank(message = "营业执照号不能为空")
    private String businessLicenseNo;

    /**
     * 营业执照图片
     */
    @NotBlank(message = "营业执照图片不能为空")
    private String businessLicenseImage;

    /**
     * 法定代表人姓名
     */
    @NotBlank(message = "法定代表人不能为空")
    private String legalRepresentative;

    /**
     * 法定代表人身份证号
     */
    private String legalIdCardNo;

    /**
     * 法定代表人手机号
     */
    private String legalPhone;

    /**
     * 负责人姓名
     */
    private String businessOwner;

    /**
     * 负责人身份证号
     */
    private String businessOwnerIdCardNo;

    /**
     * 负责人身份证正面照片
     */
    private String businessOwnerIdCardFront;

    /**
     * 负责人身份证背面照片
     */
    private String businessOwnerIdCardBack;

    /**
     * 负责人手机号
     */
    private String businessOwnerPhone;

    /**
     * 食品业者登记字号
     */
    private String foodBusinessRegistrationNo;

    /**
     * 卫生许可证号
     */
    private String healthPermitNo;

    /**
     * 建筑物使用执照号
     */
    private String buildingUsagePermitNo;

    /**
     * 消防安全检查合格证明号
     */
    private String fireSafetyCertificateNo;

    /**
     * 公共意外责任保险单号
     */
    private String publicLiabilityInsuranceNo;

    /**
     * 食品安全卫生管理人员证书号
     */
    private String foodSafetyManagerCertificateNo;

    /**
     * 酒类销售许可证号
     */
    private String alcoholSalesPermitNo;

    /**
     * 环保许可证号
     */
    private String environmentalPermitNo;


    /**
     * 地址
     */
    private String address;

    /**
     * 描述
     */
    private String description;


    /**
     * 操作人
     */
    private Long operator;

    /**
     * 备注
     */
    private String remark;
}
