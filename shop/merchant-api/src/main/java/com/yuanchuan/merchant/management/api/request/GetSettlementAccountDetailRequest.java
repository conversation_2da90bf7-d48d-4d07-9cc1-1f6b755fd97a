package com.yuanchuan.merchant.management.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 查询结算账户详情请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetSettlementAccountDetailRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 账户ID
     */
    @NotBlank(message = "账户ID不能为空")
    private String accountId;
}
