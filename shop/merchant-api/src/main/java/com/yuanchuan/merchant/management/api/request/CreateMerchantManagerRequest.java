package com.yuanchuan.merchant.management.api.request;

import com.yuanchuan.merchant.management.api.dto.PermissionDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 创建商户管理员请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateMerchantManagerRequest implements Serializable {

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 角色
     */
    private String role;

    /**
     * 权限列表
     */
    private List<PermissionDTO> permissions;

    /**
     * 操作人
     */
    private String operator;
}
