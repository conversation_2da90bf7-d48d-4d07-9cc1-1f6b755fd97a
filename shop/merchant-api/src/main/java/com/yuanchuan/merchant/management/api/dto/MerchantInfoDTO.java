package com.yuanchuan.merchant.management.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 商户信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MerchantInfoDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 商户名称
     */
    private String merchantName;
    
    /**
     * 商户类型
     */
    private String merchantType;
    
    /**
     * 营业执照号
     */
    private String businessLicenseNo;
    
    /**
     * 营业执照图片
     */
    private String businessLicenseImage;
    
    /**
     * 法定代表人
     */
    private String legalRepresentative;
    
    /**
     * 商户地址
     */
    private String merchantAddress;
    
    /**
     * 商户电话
     */
    private String merchantPhone;
    
    /**
     * 商户邮箱
     */
    private String merchantEmail;
    
    /**
     * 商户网站
     */
    private String merchantWebsite;
    
    /**
     * 商户描述
     */
    private String merchantDescription;
}
