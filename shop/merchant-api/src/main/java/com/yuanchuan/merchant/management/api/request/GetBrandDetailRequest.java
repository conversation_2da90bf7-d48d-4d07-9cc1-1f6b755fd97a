package com.yuanchuan.merchant.management.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 查询品牌详情请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetBrandDetailRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 品牌ID
     */
    @NotBlank(message = "品牌ID不能为空")
    private Long brandId;
}
