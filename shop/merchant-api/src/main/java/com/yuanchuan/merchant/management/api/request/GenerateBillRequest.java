package com.yuanchuan.merchant.management.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 生成账单请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GenerateBillRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 账户ID
     */
    @NotBlank(message = "账户ID不能为空")
    private String accountId;
    
    /**
     * 账单周期开始时间
     */
    @NotNull(message = "账单周期开始时间不能为空")
    private LocalDateTime billPeriodStart;
    
    /**
     * 账单周期结束时间
     */
    @NotNull(message = "账单周期结束时间不能为空")
    private LocalDateTime billPeriodEnd;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private Long operator;
}
