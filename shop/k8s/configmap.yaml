apiVersion: v1
kind: ConfigMap
metadata:
  name: merchant-config
  namespace: default
data:
  application.yml: |
    server:
      port: 8083
    
    # 禁用Hibernate自动配置，因为项目使用MyBatis-Plus而非JPA
    spring:
      autoconfigure:
        exclude: org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
      datasource:
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: ***************************************************************************************************************
        username: frchuser
        password: Frch2025@Dev
      data:
        redis:
          host: frch-redis-dev.redis.cache.windows.net
          port: 6380
          # 方案1：带用户名配置（推荐）
          username: default
          password: fX9W6BYENhvxUfaqRIV4rAlyvHVkKhvvPAzCaMMMpWk=
          # 方案2：如果上面不行，注释掉username行，只用密码
          # password: fX9W6BYENhvxUfaqRIV4rAlyvHVkKhvvPAzCaMMMpWk=
          database: 0
          ssl:
            enabled: true
          timeout: 10000
          lettuce:
            pool:
              max-active: 8
              max-wait: -1
              max-idle: 8
              min-idle: 0
            shutdown-timeout: 100ms
      cloud:
        function:
          definition: ;
          #consumeOrderPaidEvent;consumeOrderStatusChangedEvent
        stream:
          # bindings:
          #   # 订单创建事件消费者配置
          #   consumeOrderCreatedEvent-in-0:
          #     destination: order-created-events
          #     group: order-service
          #     content-type: application/json
          #   # 订单支付事件消费者配置
          #   consumeOrderPaidEvent-in-0:
          #     destination: order-paid-events
          #     group: order-service
          #     content-type: application/json
          #   # 订单状态变更事件消费者配置
          #   consumeOrderStatusChangedEvent-in-0:
          #     destination: order-status-changed-events
          #     group: order-service
          #     content-type: application/json
          # servicebus:
          #   bindings:
          #     consumeOrderCreatedEvent-in-0:
          #       consumer:
          #         auto-complete: true
          #         sessionEnabled: true
          #     # consumeOrderPaidEvent-in-0:
          #     #   consumer:
          #     #     auto-complete: true
          #     # consumeOrderStatusChangedEvent-in-0:
          #     #   consumer:
          #     #     auto-complete: true
          binders:
            servicebus:
              type: servicebus
              environment:
                spring:
                  cloud:
                    azure:
                      servicebus:
                        connection-string: Endpoint=sb://yuanchuan-asb.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=t54BvRmzN8etGGmk4STCIHXS/ztiFEchd+ASbOWQtPo=

    # Azure Service Bus配置
    # 是否启用Azure Service Bus（可以通过此配置在不同环境中切换消息系统）
    azure:
      servicebus:
        connection-string: Endpoint=sb://yuanchuan-asb.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=t54BvRmzN8etGGmk4STCIHXS/ztiFEchd+ASbOWQtPo=
        enabled: true
        retry:
          max-attempts: 3
          delay: 1000
      storage:
        # 连接字符串方式（推荐）
        connection-string: DefaultEndpointsProtocol=https;AccountName=qlqj2;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
        # 图片容器名称
        image-container: dev
        # 自定义基础URL（可选，如果使用CDN或自定义域名）
        # base-url: ${AZURE_STORAGE_BASE_URL:https://your-cdn-domain.com}

    # Zookeeper配置
    zookeeper:
      address: zookeeper-0.zookeeper.default.svc.cluster.local:2181,zookeeper-1.zookeeper.default.svc.cluster.local:2181
      name: com.yuanchuan.merchant

    # Dubbo配置
    dubbo:
      application:
        name: com.yuanchuan.merchant
        # 启用QoS功能
        qos-enable: true
        qos-port: 22222
        qos-accept-foreign-ip: false
      protocol:
        name: dubbo
        port: 20802
        host: 0.0.0.0
      scan:
        base-packages: com.yuanchuan.merchant
      registry:
        address: zookeeper://zookeeper-0.zookeeper.default.svc.cluster.local:2181,zookeeper-1.zookeeper.default.svc.cluster.local:2181
        register: true
        timeout: 60000
        parameters:
          blockUntilConnectedWait: 60000
          retryIntervalMillis: 5000
          retryTimes: 5
          sessionTimeoutMs: 180000
          connectionTimeoutMs: 30000
        client: curator
      group: merchant-dev
      provider:
        payload: 83886080
      consumer:
        timeout: 600000
        check: false

    # MyBatis-Plus配置
    mybatis-plus:
      mapper-locations:
        - classpath*:mapper/management/**/*.xml
        - classpath*:mapper/shop/**/*.xml
      type-aliases-package: com.yuanchuan.merchant.dto
      configuration:
        map-underscore-to-camel-case: true
        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
        # 允许重复的SQL片段ID
        variables:
          allowDuplicateFragmentNames: "true"
      global-config:
        banner: false
        db-config:
          id-type: auto
          table-underline: true

    # 日志配置
    logging:
      level:
        org.apache.dubbo: DEBUG
        org.apache.zookeeper: DEBUG

    # Google Maps API配置
    google:
      maps:
        api-key: AIzaSyBgDPxq2jfdR4rLCj4dVQfy6iBZT6CenE4
        base-url: https://places.googleapis.com/v1
        language: zh-TW
        connect-timeout: 5000
        read-timeout: 5000

    # dubbo 消费者group
    dubbo.consumer.group.user: user-dev
    dubbo.consumer.group.shop: shop-dev
    dubbo.consumer.group.order: order-dev
    dubbo.consumer.group.reservation: reservation-dev
    dubbo.consumer.group.review: review-dev
    dubbo.consumer.group.marketing: marketing-dev
    dubbo.consumer.group.tag: tag-dev