-- 运营系统权限管理示例数据
-- 版本：1.0
-- 作者：系统设计团队
-- 日期：2025-01-27
-- 说明：基于权限数据.md和SQL结构生成的示例数据

-- =============================================
-- 1. 组织表示例数据
-- =============================================

INSERT INTO `organization` (`id`, `parent_id`, `org_name`, `org_code`, `org_type`, `org_level`, `sort_order`, `description`, `created_by`, `updated_by`) VALUES
(1, NULL, '千里骑迹总部', 'QLJQ_HQ', '公司', 1, 1, '千里骑迹总部组织', 'system', 'system'),
(2, 1, '运营中心', 'QLJQ_OPS', '部门', 2, 1, '负责平台运营管理', 'system', 'system'),
(3, 1, '技术中心', 'QLJQ_TECH', '部门', 2, 2, '负责技术开发和维护', 'system', 'system'),
(4, 2, '商户运营部', 'QLJQ_MERCHANT_OPS', '部门', 3, 1, '负责商户管理和运营', 'system', 'system'),
(5, 2, '内容运营部', 'QLJQ_CONTENT_OPS', '部门', 3, 2, '负责内容审核和管理', 'system', 'system'),
(6, 2, '数据分析部', 'QLJQ_DATA_ANALYSIS', '部门', 3, 3, '负责数据统计和分析', 'system', 'system'),
(7, 4, '华北区运营组', 'QLJQ_NORTH_OPS', '小组', 4, 1, '负责华北地区商户运营', 'system', 'system'),
(8, 4, '华南区运营组', 'QLJQ_SOUTH_OPS', '小组', 4, 2, '负责华南地区商户运营', 'system', 'system'),
(9, 4, '华东区运营组', 'QLJQ_EAST_OPS', '小组', 4, 3, '负责华东地区商户运营', 'system', 'system');

-- =============================================
-- 2. 权限表示例数据（基于权限数据.md）
-- =============================================

-- 一级菜单权限
INSERT INTO `permission` (`id`, `permission_code`, `permission_name`, `type`, `parent_id`, `url`, `icon`, `order_num`, `description`, `created_by`, `updated_by`) VALUES
(1, 'MENU_AUDIT', '审核列表', 'MENU', NULL, '/audit', 'audit-icon', 1, '审核管理菜单', 'system', 'system'),
(2, 'MENU_MERCHANT', '商戶信息', 'MENU', NULL, '/merchant', 'merchant-icon', 2, '商户管理菜单', 'system', 'system'),
(3, 'MENU_DECORATION', '商戶裝修', 'MENU', NULL, '/decoration', 'decoration-icon', 3, '商户装修菜单', 'system', 'system'),
(4, 'MENU_MENU_DISH', '菜單&菜品', 'MENU', NULL, '/menu-dish', 'menu-icon', 4, '菜单菜品管理', 'system', 'system'),
(5, 'MENU_TASK', '任務列表', 'MENU', NULL, '/task', 'task-icon', 5, '任务管理菜单', 'system', 'system'),
(6, 'MENU_DATA_CENTER', '數據中心', 'MENU', NULL, '/data-center', 'data-icon', 6, '数据统计菜单', 'system', 'system'),
(7, 'MENU_ROLE', '角色管理', 'MENU', NULL, '/role', 'role-icon', 7, '角色管理菜单', 'system', 'system'),
(8, 'MENU_ACCOUNT', '帳號管理', 'MENU', NULL, '/account', 'account-icon', 8, '账号管理菜单', 'system', 'system'),
(9, 'MENU_PERMISSION', '權限管理', 'MENU', NULL, '/permission', 'permission-icon', 9, '权限管理菜单', 'system', 'system');

-- 审核列表相关权限
INSERT INTO `permission` (`id`, `permission_code`, `permission_name`, `type`, `parent_id`, `url`, `http_method`, `order_num`, `description`, `created_by`, `updated_by`) VALUES
(101, 'AUDIT:VIEW', '審核信息查看', 'BUTTON', 1, '/api/audit/list', 'GET', 1, '查看审核列表和详情', 'system', 'system'),
(102, 'AUDIT:SETTLE_PROCESS', '處理入駐審核', 'BUTTON', 1, '/api/audit/settle/process', 'POST', 2, '处理入驻审核', 'system', 'system'),
(103, 'AUDIT:COMPLAINT_PROCESS', '處理差評申訴審核', 'BUTTON', 1, '/api/audit/complaint/process', 'POST', 3, '处理差评申诉审核', 'system', 'system'),
(104, 'AUDIT:REVIEW_PROCESS', '處理評價投訴審核', 'BUTTON', 1, '/api/audit/review/process', 'POST', 4, '处理评价投诉审核', 'system', 'system'),
(105, 'AUDIT:PROCESS', '審核處理', 'BUTTON', 1, '/api/audit/process', 'POST', 5, '通用审核处理', 'system', 'system');

-- 商户信息相关权限
INSERT INTO `permission` (`id`, `permission_code`, `permission_name`, `type`, `parent_id`, `url`, `http_method`, `order_num`, `description`, `created_by`, `updated_by`) VALUES
(201, 'MERCHANT:VIEW_FULL', '查看完整數據', 'BUTTON', 2, '/api/merchant/list', 'GET', 1, '查看全量商户数据', 'system', 'system'),
(202, 'MERCHANT:VIEW_CONTACT', '查看聯繫方式', 'BUTTON', 2, '/api/merchant/contact', 'GET', 2, '查看商户联系方式', 'system', 'system'),
(203, 'MERCHANT:VIEW_ACCOUNT', '查看商戶帳號', 'BUTTON', 2, '/api/merchant/account', 'GET', 3, '查看商户账号信息', 'system', 'system'),
(204, 'MERCHANT:ASSIGN', '更改分配', 'BUTTON', 2, '/api/merchant/assign', 'POST', 4, '分配商户操作', 'system', 'system'),
(205, 'MERCHANT:UPDATE_BUSINESS_STATUS', '更改營業狀態', 'BUTTON', 2, '/api/merchant/business-status', 'PUT', 5, '修改商户营业状态', 'system', 'system'),
(206, 'MERCHANT:UPDATE_STATUS', '更改商戶狀態', 'BUTTON', 2, '/api/merchant/status', 'PUT', 6, '修改商户上架状态', 'system', 'system'),
(207, 'MERCHANT:BATCH_ASSIGN', '批量分配', 'BUTTON', 2, '/api/merchant/batch-assign', 'POST', 7, '批量分配商户', 'system', 'system'),
(208, 'MERCHANT:CREATE', '新增商戶', 'BUTTON', 2, '/api/merchant/create', 'POST', 8, '新增商户', 'system', 'system'),
(209, 'MERCHANT:VIEW_DETAIL', '查看商戶詳情', 'BUTTON', 2, '/api/merchant/detail', 'GET', 9, '查看商户详情', 'system', 'system'),
(210, 'MERCHANT:UPDATE_DETAIL', '修改商戶詳情', 'BUTTON', 2, '/api/merchant/detail', 'PUT', 10, '修改商户详情', 'system', 'system'),
(211, 'MERCHANT:UPDATE_ACCOUNT', '修改商戶帳號', 'BUTTON', 2, '/api/merchant/account', 'PUT', 11, '修改商户账号', 'system', 'system');

-- 商户装修相关权限
INSERT INTO `permission` (`id`, `permission_code`, `permission_name`, `type`, `parent_id`, `url`, `http_method`, `order_num`, `description`, `created_by`, `updated_by`) VALUES
(301, 'DECORATION:UPDATE', '修改商戶裝修', 'BUTTON', 3, '/api/decoration/update', 'PUT', 1, '修改商户装修信息', 'system', 'system');

-- 菜单菜品相关权限
INSERT INTO `permission` (`id`, `permission_code`, `permission_name`, `type`, `parent_id`, `url`, `http_method`, `order_num`, `description`, `created_by`, `updated_by`) VALUES
(401, 'MENU:UPDATE', '修改商戶菜單', 'BUTTON', 4, '/api/menu/update', 'PUT', 1, '修改商户菜单', 'system', 'system'),
(402, 'DISH:UPDATE', '修改商戶菜品', 'BUTTON', 4, '/api/dish/update', 'PUT', 2, '修改商户菜品', 'system', 'system');

-- 任务列表相关权限
INSERT INTO `permission` (`id`, `permission_code`, `permission_name`, `type`, `parent_id`, `url`, `http_method`, `order_num`, `description`, `created_by`, `updated_by`) VALUES
(501, 'TASK:VIEW_FULL', '查看完整數據', 'BUTTON', 5, '/api/task/list', 'GET', 1, '查看全量任务数据', 'system', 'system'),
(502, 'TASK:ASSIGN', '分配任務', 'BUTTON', 5, '/api/task/assign', 'POST', 2, '分配任务操作', 'system', 'system'),
(503, 'TASK:BATCH_ASSIGN', '批量分配', 'BUTTON', 5, '/api/task/batch-assign', 'POST', 3, '批量分配任务', 'system', 'system'),
(504, 'TASK:BATCH_CREATE', '批量創建', 'BUTTON', 5, '/api/task/batch-create', 'POST', 4, '批量创建任务', 'system', 'system');

-- 数据中心相关权限
INSERT INTO `permission` (`id`, `permission_code`, `permission_name`, `type`, `parent_id`, `url`, `http_method`, `order_num`, `description`, `created_by`, `updated_by`) VALUES
(601, 'DATA:REVIEW_STATS', '評價統計', 'BUTTON', 6, '/api/data/review-stats', 'GET', 1, '查看评价统计入口', 'system', 'system'),
(602, 'DATA:REVIEW_DATA', '評價數據', 'BUTTON', 6, '/api/data/review-data', 'GET', 2, '查看关联商户评价数据', 'system', 'system'),
(603, 'DATA:ALL_REVIEW_DATA', '全部評價數據', 'BUTTON', 6, '/api/data/all-review-data', 'GET', 3, '查看所有商户评价数据', 'system', 'system');

-- 角色管理相关权限
INSERT INTO `permission` (`id`, `permission_code`, `permission_name`, `type`, `parent_id`, `url`, `http_method`, `order_num`, `description`, `created_by`, `updated_by`) VALUES
(701, 'ROLE:VIEW', '查看角色', 'BUTTON', 7, '/api/role/list', 'GET', 1, '查看角色列表', 'system', 'system'),
(702, 'ROLE:MANAGE', '管理角色', 'BUTTON', 7, '/api/role/manage', 'POST', 2, '新增修改删除角色', 'system', 'system');

-- 账号管理相关权限
INSERT INTO `permission` (`id`, `permission_code`, `permission_name`, `type`, `parent_id`, `url`, `http_method`, `order_num`, `description`, `created_by`, `updated_by`) VALUES
(801, 'ACCOUNT:VIEW', '查看帳號', 'BUTTON', 8, '/api/account/list', 'GET', 1, '查看账号列表', 'system', 'system'),
(802, 'ACCOUNT:MANAGE', '管理帳號', 'BUTTON', 8, '/api/account/manage', 'POST', 2, '新增修改账号信息和状态', 'system', 'system');

-- 权限管理相关权限
INSERT INTO `permission` (`id`, `permission_code`, `permission_name`, `type`, `parent_id`, `url`, `http_method`, `order_num`, `description`, `created_by`, `updated_by`) VALUES
(901, 'PERMISSION:VIEW', '查看權限', 'BUTTON', 9, '/api/permission/list', 'GET', 1, '查看权限列表', 'system', 'system'),
(902, 'PERMISSION:MANAGE', '管理權限', 'BUTTON', 9, '/api/permission/manage', 'POST', 2, '新增修改权限信息和状态', 'system', 'system'),
(903, 'PERMISSION:CREATE_ENTRY', '創建權限入口', 'BUTTON', 9, '/api/permission/create-entry', 'POST', 3, '创建新的权限入口', 'system', 'system');

-- =============================================
-- 3. 角色表示例数据
-- =============================================

INSERT INTO `role` (`id`, `role_code`, `role_name`, `org_id`, `description`, `created_by`, `updated_by`) VALUES
(1, 'SUPER_ADMIN', '超级管理员', 1, '拥有系统所有权限的超级管理员', 'system', 'system'),
(2, 'OPS_MANAGER', '运营经理', 2, '运营中心经理，负责整体运营管理', 'system', 'system'),
(3, 'MERCHANT_MANAGER', '商户经理', 4, '商户运营部经理，负责商户管理', 'system', 'system'),
(4, 'CONTENT_MANAGER', '内容经理', 5, '内容运营部经理，负责内容审核', 'system', 'system'),
(5, 'DATA_ANALYST', '数据分析师', 6, '数据分析部分析师，负责数据统计', 'system', 'system'),
(6, 'MERCHANT_OPERATOR', '商户运营专员', 4, '商户运营专员，负责具体商户运营工作', 'system', 'system'),
(7, 'CONTENT_AUDITOR', '内容审核员', 5, '内容审核员，负责具体审核工作', 'system', 'system'),
(8, 'REGIONAL_MANAGER', '区域经理', 7, '区域运营经理，负责特定区域商户管理', 'system', 'system'),
(9, 'TASK_COORDINATOR', '任务协调员', 4, '任务协调员，负责任务分配和管理', 'system', 'system'),
(10, 'PERMISSION_ADMIN', '权限管理员', 3, '权限管理员，负责系统权限配置', 'system', 'system');

-- =============================================
-- 4. 商户账户示例数据（假设已有business_account表）
-- =============================================

-- 注意：这里假设business_account表已存在，我们只添加org_id字段的更新
-- 实际使用时需要根据现有的business_account表结构调整

-- 更新现有商户账户的组织ID（示例）
-- UPDATE business_account SET org_id = 7 WHERE id IN (1, 2, 3); -- 华北区
-- UPDATE business_account SET org_id = 8 WHERE id IN (4, 5, 6); -- 华南区
-- UPDATE business_account SET org_id = 9 WHERE id IN (7, 8, 9); -- 华东区

-- 或者插入新的商户账户示例数据（根据实际表结构调整）
-- INSERT INTO business_account (username, email, phone, org_id, status, created_by) VALUES
-- ('merchant_001', '<EMAIL>', '***********', 7, 'ACTIVE', 'system'),
-- ('merchant_002', '<EMAIL>', '***********', 7, 'ACTIVE', 'system'),
-- ('merchant_003', '<EMAIL>', '***********', 8, 'ACTIVE', 'system'),
-- ('merchant_004', '<EMAIL>', '***********', 8, 'ACTIVE', 'system'),
-- ('merchant_005', '<EMAIL>', '***********', 9, 'ACTIVE', 'system');

-- =============================================
-- 5. 角色权限关联示例数据
-- =============================================

-- 超级管理员拥有所有权限
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`, `created_by`, `updated_by`) VALUES
-- 菜单权限
(1, 1, 'GRANT', 'system', 'system'), (1, 2, 'GRANT', 'system', 'system'), (1, 3, 'GRANT', 'system', 'system'),
(1, 4, 'GRANT', 'system', 'system'), (1, 5, 'GRANT', 'system', 'system'), (1, 6, 'GRANT', 'system', 'system'),
(1, 7, 'GRANT', 'system', 'system'), (1, 8, 'GRANT', 'system', 'system'), (1, 9, 'GRANT', 'system', 'system'),
-- 审核权限
(1, 101, 'GRANT', 'system', 'system'), (1, 102, 'GRANT', 'system', 'system'), (1, 103, 'GRANT', 'system', 'system'),
(1, 104, 'GRANT', 'system', 'system'), (1, 105, 'GRANT', 'system', 'system'),
-- 商户权限
(1, 201, 'GRANT', 'system', 'system'), (1, 202, 'GRANT', 'system', 'system'), (1, 203, 'GRANT', 'system', 'system'),
(1, 204, 'GRANT', 'system', 'system'), (1, 205, 'GRANT', 'system', 'system'), (1, 206, 'GRANT', 'system', 'system'),
(1, 207, 'GRANT', 'system', 'system'), (1, 208, 'GRANT', 'system', 'system'), (1, 209, 'GRANT', 'system', 'system'),
(1, 210, 'GRANT', 'system', 'system'), (1, 211, 'GRANT', 'system', 'system'),
-- 装修权限
(1, 301, 'GRANT', 'system', 'system'),
-- 菜单菜品权限
(1, 401, 'GRANT', 'system', 'system'), (1, 402, 'GRANT', 'system', 'system'),
-- 任务权限
(1, 501, 'GRANT', 'system', 'system'), (1, 502, 'GRANT', 'system', 'system'), (1, 503, 'GRANT', 'system', 'system'),
(1, 504, 'GRANT', 'system', 'system'),
-- 数据权限
(1, 601, 'GRANT', 'system', 'system'), (1, 602, 'GRANT', 'system', 'system'), (1, 603, 'GRANT', 'system', 'system'),
-- 角色权限
(1, 701, 'GRANT', 'system', 'system'), (1, 702, 'GRANT', 'system', 'system'),
-- 账号权限
(1, 801, 'GRANT', 'system', 'system'), (1, 802, 'GRANT', 'system', 'system'),
-- 权限管理权限
(1, 901, 'GRANT', 'system', 'system'), (1, 902, 'GRANT', 'system', 'system'), (1, 903, 'GRANT', 'system', 'system');

-- 运营经理权限（除了权限管理外的大部分权限）
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`, `created_by`, `updated_by`) VALUES
-- 菜单权限
(2, 1, 'GRANT', 'system', 'system'), (2, 2, 'GRANT', 'system', 'system'), (2, 3, 'GRANT', 'system', 'system'),
(2, 4, 'GRANT', 'system', 'system'), (2, 5, 'GRANT', 'system', 'system'), (2, 6, 'GRANT', 'system', 'system'),
(2, 7, 'GRANT', 'system', 'system'), (2, 8, 'GRANT', 'system', 'system'),
-- 审核权限
(2, 101, 'GRANT', 'system', 'system'), (2, 102, 'GRANT', 'system', 'system'), (2, 103, 'GRANT', 'system', 'system'),
(2, 104, 'GRANT', 'system', 'system'), (2, 105, 'GRANT', 'system', 'system'),
-- 商户权限
(2, 201, 'GRANT', 'system', 'system'), (2, 202, 'GRANT', 'system', 'system'), (2, 203, 'GRANT', 'system', 'system'),
(2, 204, 'GRANT', 'system', 'system'), (2, 205, 'GRANT', 'system', 'system'), (2, 206, 'GRANT', 'system', 'system'),
(2, 207, 'GRANT', 'system', 'system'), (2, 208, 'GRANT', 'system', 'system'), (2, 209, 'GRANT', 'system', 'system'),
(2, 210, 'GRANT', 'system', 'system'), (2, 211, 'GRANT', 'system', 'system'),
-- 装修权限
(2, 301, 'GRANT', 'system', 'system'),
-- 菜单菜品权限
(2, 401, 'GRANT', 'system', 'system'), (2, 402, 'GRANT', 'system', 'system'),
-- 任务权限
(2, 501, 'GRANT', 'system', 'system'), (2, 502, 'GRANT', 'system', 'system'), (2, 503, 'GRANT', 'system', 'system'),
(2, 504, 'GRANT', 'system', 'system'),
-- 数据权限
(2, 601, 'GRANT', 'system', 'system'), (2, 602, 'GRANT', 'system', 'system'), (2, 603, 'GRANT', 'system', 'system'),
-- 角色权限
(2, 701, 'GRANT', 'system', 'system'), (2, 702, 'GRANT', 'system', 'system'),
-- 账号权限
(2, 801, 'GRANT', 'system', 'system'), (2, 802, 'GRANT', 'system', 'system');

-- 商户经理权限（主要是商户相关权限）
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`, `created_by`, `updated_by`) VALUES
-- 菜单权限
(3, 2, 'GRANT', 'system', 'system'), (3, 3, 'GRANT', 'system', 'system'), (3, 4, 'GRANT', 'system', 'system'),
(3, 5, 'GRANT', 'system', 'system'), (3, 6, 'GRANT', 'system', 'system'),
-- 商户权限
(3, 201, 'GRANT', 'system', 'system'), (3, 202, 'GRANT', 'system', 'system'), (3, 203, 'GRANT', 'system', 'system'),
(3, 204, 'GRANT', 'system', 'system'), (3, 205, 'GRANT', 'system', 'system'), (3, 206, 'GRANT', 'system', 'system'),
(3, 207, 'GRANT', 'system', 'system'), (3, 208, 'GRANT', 'system', 'system'), (3, 209, 'GRANT', 'system', 'system'),
(3, 210, 'GRANT', 'system', 'system'), (3, 211, 'GRANT', 'system', 'system'),
-- 装修权限
(3, 301, 'GRANT', 'system', 'system'),
-- 菜单菜品权限
(3, 401, 'GRANT', 'system', 'system'), (3, 402, 'GRANT', 'system', 'system'),
-- 任务权限
(3, 501, 'GRANT', 'system', 'system'), (3, 502, 'GRANT', 'system', 'system'), (3, 503, 'GRANT', 'system', 'system'),
(3, 504, 'GRANT', 'system', 'system'),
-- 数据权限（部分）
(3, 601, 'GRANT', 'system', 'system'), (3, 602, 'GRANT', 'system', 'system');

-- 内容经理权限（主要是审核相关权限）
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`, `created_by`, `updated_by`) VALUES
-- 菜单权限
(4, 1, 'GRANT', 'system', 'system'), (4, 6, 'GRANT', 'system', 'system'),
-- 审核权限
(4, 101, 'GRANT', 'system', 'system'), (4, 102, 'GRANT', 'system', 'system'), (4, 103, 'GRANT', 'system', 'system'),
(4, 104, 'GRANT', 'system', 'system'), (4, 105, 'GRANT', 'system', 'system'),
-- 数据权限（评价相关）
(4, 601, 'GRANT', 'system', 'system'), (4, 602, 'GRANT', 'system', 'system'), (4, 603, 'GRANT', 'system', 'system');

-- 数据分析师权限（主要是数据查看权限）
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`, `created_by`, `updated_by`) VALUES
-- 菜单权限
(5, 6, 'GRANT', 'system', 'system'),
-- 数据权限
(5, 601, 'GRANT', 'system', 'system'), (5, 602, 'GRANT', 'system', 'system'), (5, 603, 'GRANT', 'system', 'system'),
-- 商户查看权限（只读）
(5, 2, 'GRANT', 'system', 'system'), (5, 201, 'GRANT', 'system', 'system'), (5, 209, 'GRANT', 'system', 'system');

-- 商户运营专员权限（基础商户操作权限）
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`, `created_by`, `updated_by`) VALUES
-- 菜单权限
(6, 2, 'GRANT', 'system', 'system'), (6, 3, 'GRANT', 'system', 'system'), (6, 4, 'GRANT', 'system', 'system'),
(6, 5, 'GRANT', 'system', 'system'),
-- 商户权限（部分）
(6, 201, 'GRANT', 'system', 'system'), (6, 202, 'GRANT', 'system', 'system'), (6, 203, 'GRANT', 'system', 'system'),
(6, 204, 'GRANT', 'system', 'system'), (6, 209, 'GRANT', 'system', 'system'), (6, 210, 'GRANT', 'system', 'system'),
-- 装修权限
(6, 301, 'GRANT', 'system', 'system'),
-- 菜单菜品权限
(6, 401, 'GRANT', 'system', 'system'), (6, 402, 'GRANT', 'system', 'system'),
-- 任务权限（部分）
(6, 501, 'GRANT', 'system', 'system'), (6, 502, 'GRANT', 'system', 'system');

-- 内容审核员权限（基础审核权限）
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`, `created_by`, `updated_by`) VALUES
-- 菜单权限
(7, 1, 'GRANT', 'system', 'system'),
-- 审核权限（部分）
(7, 101, 'GRANT', 'system', 'system'), (7, 103, 'GRANT', 'system', 'system'), (7, 104, 'GRANT', 'system', 'system');

-- 区域经理权限（区域内商户管理权限）
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`, `created_by`, `updated_by`) VALUES
-- 菜单权限
(8, 2, 'GRANT', 'system', 'system'), (8, 3, 'GRANT', 'system', 'system'), (8, 4, 'GRANT', 'system', 'system'),
(8, 5, 'GRANT', 'system', 'system'), (8, 6, 'GRANT', 'system', 'system'),
-- 商户权限
(8, 201, 'GRANT', 'system', 'system'), (8, 202, 'GRANT', 'system', 'system'), (8, 203, 'GRANT', 'system', 'system'),
(8, 204, 'GRANT', 'system', 'system'), (8, 205, 'GRANT', 'system', 'system'), (8, 206, 'GRANT', 'system', 'system'),
(8, 207, 'GRANT', 'system', 'system'), (8, 209, 'GRANT', 'system', 'system'), (8, 210, 'GRANT', 'system', 'system'),
-- 装修权限
(8, 301, 'GRANT', 'system', 'system'),
-- 菜单菜品权限
(8, 401, 'GRANT', 'system', 'system'), (8, 402, 'GRANT', 'system', 'system'),
-- 任务权限
(8, 501, 'GRANT', 'system', 'system'), (8, 502, 'GRANT', 'system', 'system'), (8, 503, 'GRANT', 'system', 'system'),
-- 数据权限（部分）
(8, 601, 'GRANT', 'system', 'system'), (8, 602, 'GRANT', 'system', 'system');

-- 任务协调员权限（主要是任务管理权限）
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`, `created_by`, `updated_by`) VALUES
-- 菜单权限
(9, 5, 'GRANT', 'system', 'system'),
-- 任务权限
(9, 501, 'GRANT', 'system', 'system'), (9, 502, 'GRANT', 'system', 'system'), (9, 503, 'GRANT', 'system', 'system'),
(9, 504, 'GRANT', 'system', 'system'),
-- 商户查看权限（只读）
(9, 2, 'GRANT', 'system', 'system'), (9, 201, 'GRANT', 'system', 'system'), (9, 209, 'GRANT', 'system', 'system');

-- 权限管理员权限（权限和角色管理）
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`, `created_by`, `updated_by`) VALUES
-- 菜单权限
(10, 7, 'GRANT', 'system', 'system'), (10, 8, 'GRANT', 'system', 'system'), (10, 9, 'GRANT', 'system', 'system'),
-- 角色权限
(10, 701, 'GRANT', 'system', 'system'), (10, 702, 'GRANT', 'system', 'system'),
-- 账号权限
(10, 801, 'GRANT', 'system', 'system'), (10, 802, 'GRANT', 'system', 'system'),
-- 权限管理权限
(10, 901, 'GRANT', 'system', 'system'), (10, 902, 'GRANT', 'system', 'system'), (10, 903, 'GRANT', 'system', 'system');

-- =============================================
-- 6. 商户账户角色关联示例数据
-- =============================================

-- 假设有一些商户账户ID，为他们分配角色
INSERT INTO `business_account_role` (`business_account_id`, `role_id`, `org_id`, `effective_date`, `expiry_date`, `status`, `created_by`, `updated_by`) VALUES
-- 超级管理员
(1001, 1, 1, '2025-01-01', '2025-12-31', 'ACTIVE', 'system', 'system'),
-- 运营经理
(1002, 2, 2, '2025-01-01', '2025-12-31', 'ACTIVE', 'system', 'system'),
-- 商户经理
(1003, 3, 4, '2025-01-01', '2025-12-31', 'ACTIVE', 'system', 'system'),
(1004, 3, 4, '2025-01-01', '2025-12-31', 'ACTIVE', 'system', 'system'),
-- 内容经理
(1005, 4, 5, '2025-01-01', '2025-12-31', 'ACTIVE', 'system', 'system'),
-- 数据分析师
(1006, 5, 6, '2025-01-01', '2025-12-31', 'ACTIVE', 'system', 'system'),
(1007, 5, 6, '2025-01-01', '2025-12-31', 'ACTIVE', 'system', 'system'),
-- 商户运营专员
(1008, 6, 4, '2025-01-01', '2025-12-31', 'ACTIVE', 'system', 'system'),
(1009, 6, 4, '2025-01-01', '2025-12-31', 'ACTIVE', 'system', 'system'),
(1010, 6, 4, '2025-01-01', '2025-12-31', 'ACTIVE', 'system', 'system'),
-- 内容审核员
(1011, 7, 5, '2025-01-01', '2025-12-31', 'ACTIVE', 'system', 'system'),
(1012, 7, 5, '2025-01-01', '2025-12-31', 'ACTIVE', 'system', 'system'),
-- 区域经理
(1013, 8, 7, '2025-01-01', '2025-12-31', 'ACTIVE', 'system', 'system'), -- 华北区
(1014, 8, 8, '2025-01-01', '2025-12-31', 'ACTIVE', 'system', 'system'), -- 华南区
(1015, 8, 9, '2025-01-01', '2025-12-31', 'ACTIVE', 'system', 'system'), -- 华东区
-- 任务协调员
(1016, 9, 4, '2025-01-01', '2025-12-31', 'ACTIVE', 'system', 'system'),
(1017, 9, 4, '2025-01-01', '2025-12-31', 'ACTIVE', 'system', 'system'),
-- 权限管理员
(1018, 10, 3, '2025-01-01', '2025-12-31', 'ACTIVE', 'system', 'system');

-- =============================================
-- 7. 审计日志示例数据
-- =============================================

INSERT INTO `audit_log` (`biz_type`, `biz_id`, `action_type`, `operator_id`, `operator_name`, `operate_time`, `change_fields`, `remark`, `created_by`, `updated_by`) VALUES
-- 角色创建日志
('ROLE', 1, 'CREATE', 1001, '系统管理员', '2025-01-27 10:00:00',
 '{"role_name": {"before": null, "after": "超级管理员"}, "role_code": {"before": null, "after": "SUPER_ADMIN"}, "org_id": {"before": null, "after": 1}}',
 '创建超级管理员角色', 'system', 'system'),

('ROLE', 2, 'CREATE', 1001, '系统管理员', '2025-01-27 10:05:00',
 '{"role_name": {"before": null, "after": "运营经理"}, "role_code": {"before": null, "after": "OPS_MANAGER"}, "org_id": {"before": null, "after": 2}}',
 '创建运营经理角色', 'system', 'system'),

-- 权限分配日志
('ROLE_PERMISSION', 1, 'CREATE', 1001, '系统管理员', '2025-01-27 10:10:00',
 '{"role_id": {"before": null, "after": 1}, "permission_id": {"before": null, "after": 101}, "grant_type": {"before": null, "after": "GRANT"}}',
 '为超级管理员分配审核信息查看权限', 'system', 'system'),

('ROLE_PERMISSION', 2, 'CREATE', 1001, '系统管理员', '2025-01-27 10:15:00',
 '{"role_id": {"before": null, "after": 2}, "permission_id": {"before": null, "after": 201}, "grant_type": {"before": null, "after": "GRANT"}}',
 '为运营经理分配商户查看权限', 'system', 'system'),

-- 用户角色分配日志
('BUSINESS_ACCOUNT_ROLE', 1, 'CREATE', 1001, '系统管理员', '2025-01-27 10:20:00',
 '{"business_account_id": {"before": null, "after": 1002}, "role_id": {"before": null, "after": 2}, "org_id": {"before": null, "after": 2}, "status": {"before": null, "after": "ACTIVE"}}',
 '为用户1002分配运营经理角色', 'system', 'system'),

-- 权限修改日志
('PERMISSION', 101, 'UPDATE', 1018, '权限管理员', '2025-01-27 11:00:00',
 '{"permission_name": {"before": "審核信息查看", "after": "审核信息查看"}, "description": {"before": "查看审核列表和详情", "after": "查看审核列表和详情信息"}}',
 '更新权限名称和描述', 'admin', 'admin'),

-- 角色权限撤销日志
('ROLE_PERMISSION', 50, 'DELETE', 1018, '权限管理员', '2025-01-27 11:30:00',
 '{"role_id": {"before": 6, "after": null}, "permission_id": {"before": 208, "after": null}, "grant_type": {"before": "GRANT", "after": null}}',
 '撤销商户运营专员的新增商户权限', 'admin', 'admin'),

-- 用户角色变更日志
('BUSINESS_ACCOUNT_ROLE', 10, 'UPDATE', 1003, '商户经理', '2025-01-27 12:00:00',
 '{"status": {"before": "ACTIVE", "after": "INACTIVE"}, "expiry_date": {"before": "2025-12-31", "after": "2025-01-27"}}',
 '停用用户角色', 'manager', 'manager'),

-- 组织变更日志
('ORGANIZATION', 7, 'UPDATE', 1002, '运营经理', '2025-01-27 14:00:00',
 '{"org_name": {"before": "华北区运营组", "after": "华北区运营中心"}, "description": {"before": "负责华北地区商户运营", "after": "负责华北地区商户运营管理"}}',
 '更新华北区组织信息', 'ops_manager', 'ops_manager'),

-- 批量权限分配日志
('ROLE_PERMISSION', 100, 'CREATE', 1001, '系统管理员', '2025-01-27 15:00:00',
 '{"batch_operation": {"before": null, "after": "批量为区域经理分配商户管理权限"}, "affected_count": {"before": null, "after": 15}}',
 '批量为区域经理角色分配商户管理相关权限', 'system', 'system');

-- =============================================
-- 8. 查询示例（注释形式，供参考）
-- =============================================

/*
-- 查询用户的所有权限
SELECT DISTINCT
    p.permission_code,
    p.permission_name,
    p.type,
    p.url,
    p.http_method
FROM business_account_role bar
JOIN role_permission rp ON bar.role_id = rp.role_id
JOIN permission p ON rp.permission_id = p.id
WHERE bar.business_account_id = 1008
  AND bar.status = 'ACTIVE'
  AND bar.active = 1
  AND rp.grant_type = 'GRANT'
  AND rp.active = 1
  AND p.status = 1
  AND p.active = 1;

-- 查询角色的权限树
SELECT
    p1.id as menu_id,
    p1.permission_name as menu_name,
    p2.id as permission_id,
    p2.permission_name as permission_name,
    p2.permission_code,
    p2.type
FROM role_permission rp
JOIN permission p2 ON rp.permission_id = p2.id
LEFT JOIN permission p1 ON p2.parent_id = p1.id
WHERE rp.role_id = 3
  AND rp.grant_type = 'GRANT'
  AND rp.active = 1
  AND p2.active = 1
ORDER BY p1.order_num, p2.order_num;

-- 查询组织下的所有用户及其角色
SELECT
    o.org_name,
    bar.business_account_id,
    r.role_name,
    bar.status,
    bar.effective_date,
    bar.expiry_date
FROM organization o
JOIN business_account_role bar ON o.id = bar.org_id
JOIN role r ON bar.role_id = r.id
WHERE o.id = 4
  AND bar.active = 1
ORDER BY bar.business_account_id;

-- 查询权限操作日志
SELECT
    al.operate_time,
    al.biz_type,
    al.action_type,
    al.operator_name,
    al.change_fields,
    al.remark
FROM audit_log al
WHERE al.biz_type IN ('ROLE', 'PERMISSION', 'ROLE_PERMISSION')
  AND al.operate_time >= '2025-01-27 00:00:00'
ORDER BY al.operate_time DESC;
*/
