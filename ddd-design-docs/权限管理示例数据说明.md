# 运营系统权限管理示例数据说明

## 概述

本文档说明了基于`运营系统权限管理优化版本.sql`和`权限数据.md`生成的示例数据结构和使用方法。

## 数据结构说明

### 1. 组织架构 (organization)

建立了4层组织架构：

```
千里骑迹总部 (1)
├── 运营中心 (2)
│   ├── 商户运营部 (4)
│   │   ├── 华北区运营组 (7)
│   │   ├── 华南区运营组 (8)
│   │   └── 华东区运营组 (9)
│   ├── 内容运营部 (5)
│   └── 数据分析部 (6)
└── 技术中心 (3)
```

### 2. 权限体系 (permission)

#### 2.1 菜单权限 (MENU类型)
- 审核列表 (1)
- 商戶信息 (2)
- 商戶裝修 (3)
- 菜單&菜品 (4)
- 任務列表 (5)
- 數據中心 (6)
- 角色管理 (7)
- 帳號管理 (8)
- 權限管理 (9)

#### 2.2 功能权限 (BUTTON类型)

**审核相关权限 (101-105)**
- 審核信息查看 (AUDIT:VIEW)
- 處理入駐審核 (AUDIT:SETTLE_PROCESS)
- 處理差評申訴審核 (AUDIT:COMPLAINT_PROCESS)
- 處理評價投訴審核 (AUDIT:REVIEW_PROCESS)
- 審核處理 (AUDIT:PROCESS)

**商户相关权限 (201-211)**
- 查看完整數據 (MERCHANT:VIEW_FULL)
- 查看聯繫方式 (MERCHANT:VIEW_CONTACT)
- 查看商戶帳號 (MERCHANT:VIEW_ACCOUNT)
- 更改分配 (MERCHANT:ASSIGN)
- 更改營業狀態 (MERCHANT:UPDATE_BUSINESS_STATUS)
- 更改商戶狀態 (MERCHANT:UPDATE_STATUS)
- 批量分配 (MERCHANT:BATCH_ASSIGN)
- 新增商戶 (MERCHANT:CREATE)
- 查看商戶詳情 (MERCHANT:VIEW_DETAIL)
- 修改商戶詳情 (MERCHANT:UPDATE_DETAIL)
- 修改商戶帳號 (MERCHANT:UPDATE_ACCOUNT)

**其他功能权限**
- 商戶裝修 (301)
- 菜單&菜品 (401-402)
- 任務管理 (501-504)
- 數據中心 (601-603)
- 角色管理 (701-702)
- 帳號管理 (801-802)
- 權限管理 (901-903)

### 3. 角色体系 (role)

| 角色ID | 角色编码 | 角色名称 | 所属组织 | 权限范围 |
|--------|----------|----------|----------|----------|
| 1 | SUPER_ADMIN | 超级管理员 | 总部 | 所有权限 |
| 2 | OPS_MANAGER | 运营经理 | 运营中心 | 除权限管理外的所有权限 |
| 3 | MERCHANT_MANAGER | 商户经理 | 商户运营部 | 商户相关权限 |
| 4 | CONTENT_MANAGER | 内容经理 | 内容运营部 | 审核相关权限 |
| 5 | DATA_ANALYST | 数据分析师 | 数据分析部 | 数据查看权限 |
| 6 | MERCHANT_OPERATOR | 商户运营专员 | 商户运营部 | 基础商户操作权限 |
| 7 | CONTENT_AUDITOR | 内容审核员 | 内容运营部 | 基础审核权限 |
| 8 | REGIONAL_MANAGER | 区域经理 | 各区域组 | 区域商户管理权限 |
| 9 | TASK_COORDINATOR | 任务协调员 | 商户运营部 | 任务管理权限 |
| 10 | PERMISSION_ADMIN | 权限管理员 | 技术中心 | 权限和角色管理权限 |

### 4. 用户角色分配 (business_account_role)

示例中为18个用户(1001-1018)分配了不同的角色，包括：
- 1个超级管理员
- 1个运营经理
- 2个商户经理
- 1个内容经理
- 2个数据分析师
- 3个商户运营专员
- 2个内容审核员
- 3个区域经理（分别负责华北、华南、华东）
- 2个任务协调员
- 1个权限管理员

### 5. 审计日志 (audit_log)

包含了以下类型的操作日志：
- 角色创建日志
- 权限分配日志
- 用户角色分配日志
- 权限修改日志
- 角色权限撤销日志
- 用户角色变更日志
- 组织变更日志
- 批量权限分配日志

## 使用说明

### 1. 数据导入

```sql
-- 按顺序执行以下步骤：
-- 1. 先执行建表SQL
source 运营系统权限管理优化版本.sql;

-- 2. 再导入示例数据
source 权限管理示例数据.sql;
```

### 2. 常用查询

#### 2.1 查询用户权限
```sql
-- 查询用户1008的所有权限
SELECT DISTINCT
    p.permission_code,
    p.permission_name,
    p.type,
    p.url,
    p.http_method
FROM business_account_role bar
JOIN role_permission rp ON bar.role_id = rp.role_id
JOIN permission p ON rp.permission_id = p.id
WHERE bar.business_account_id = 1008
  AND bar.status = 'ACTIVE'
  AND bar.active = 1
  AND rp.grant_type = 'GRANT'
  AND rp.active = 1
  AND p.status = 1
  AND p.active = 1;
```

#### 2.2 查询角色权限树
```sql
-- 查询商户经理角色的权限树
SELECT
    p1.id as menu_id,
    p1.permission_name as menu_name,
    p2.id as permission_id,
    p2.permission_name as permission_name,
    p2.permission_code,
    p2.type
FROM role_permission rp
JOIN permission p2 ON rp.permission_id = p2.id
LEFT JOIN permission p1 ON p2.parent_id = p1.id
WHERE rp.role_id = 3
  AND rp.grant_type = 'GRANT'
  AND rp.active = 1
  AND p2.active = 1
ORDER BY p1.order_num, p2.order_num;
```

#### 2.3 查询组织用户
```sql
-- 查询商户运营部下的所有用户及其角色
SELECT
    o.org_name,
    bar.business_account_id,
    r.role_name,
    bar.status,
    bar.effective_date,
    bar.expiry_date
FROM organization o
JOIN business_account_role bar ON o.id = bar.org_id
JOIN role r ON bar.role_id = r.id
WHERE o.id = 4
  AND bar.active = 1
ORDER BY bar.business_account_id;
```

### 3. 权限验证示例

#### 3.1 验证用户是否有特定权限
```sql
-- 验证用户1008是否有商户查看权限
SELECT COUNT(*) > 0 as has_permission
FROM business_account_role bar
JOIN role_permission rp ON bar.role_id = rp.role_id
JOIN permission p ON rp.permission_id = p.id
WHERE bar.business_account_id = 1008
  AND p.permission_code = 'MERCHANT:VIEW_FULL'
  AND bar.status = 'ACTIVE'
  AND bar.active = 1
  AND rp.grant_type = 'GRANT'
  AND rp.active = 1
  AND p.active = 1;
```

#### 3.2 获取用户菜单权限
```sql
-- 获取用户1008的菜单权限
SELECT DISTINCT
    p.id,
    p.permission_code,
    p.permission_name,
    p.url,
    p.icon,
    p.order_num
FROM business_account_role bar
JOIN role_permission rp ON bar.role_id = rp.role_id
JOIN permission p ON rp.permission_id = p.id
WHERE bar.business_account_id = 1008
  AND p.type = 'MENU'
  AND bar.status = 'ACTIVE'
  AND bar.active = 1
  AND rp.grant_type = 'GRANT'
  AND rp.active = 1
  AND p.active = 1
  AND p.is_visible = 1
ORDER BY p.order_num;
```

#### 3.3 获取用户按钮权限
```sql
-- 获取用户1008的按钮权限
SELECT DISTINCT
    p.id,
    p.permission_code,
    p.permission_name,
    p.parent_id,
    p.url,
    p.http_method
FROM business_account_role bar
JOIN role_permission rp ON bar.role_id = rp.role_id
JOIN permission p ON rp.permission_id = p.id
WHERE bar.business_account_id = 1008
  AND p.type = 'BUTTON'
  AND bar.status = 'ACTIVE'
  AND bar.active = 1
  AND rp.grant_type = 'GRANT'
  AND rp.active = 1
  AND p.active = 1
ORDER BY p.parent_id, p.order_num;
```

## 注意事项

1. **商户账户表**: 示例数据中假设`business_account`表已存在，实际使用时需要根据现有表结构调整
2. **ID范围**: 示例中使用的用户ID(1001-1018)为假设数据，实际使用时需要替换为真实的用户ID
3. **时间有效性**: 角色分配设置了有效期，实际查询时需要考虑时间范围
4. **软删除**: 所有表都支持软删除(active字段)，查询时需要过滤已删除数据
5. **审计日志**: change_fields字段使用JSON格式存储变更内容，便于追踪具体变更

## 扩展建议

1. **缓存策略**: 用户权限查询频繁，建议增加Redis缓存
2. **权限继承**: 可以考虑实现组织级权限继承机制
3. **动态权限**: 可以扩展支持基于数据范围的动态权限控制
4. **权限模板**: 可以创建角色模板，简化权限分配流程
