# 运营系统权限管理需求实现说明

## 文档信息
- **版本**：2.0
- **作者**：系统设计团队
- **日期**：2025-01-27
- **状态**：需求分析版

## 1. 需求分析与数据库调整

### 1.1 主要调整内容

根据新需求，对原有数据库设计进行了以下调整：

1. **新增权限入口表**：支持权限按入口分组管理
2. **调整权限表**：增加权限入口关联、唯一性约束
3. **调整角色表**：状态字段改为ACTIVE/INACTIVE
4. **扩展账户表**：增加员工编码、真实姓名、手机、邮箱等字段
5. **新增修改日志表**：详细记录所有修改操作

### 1.2 核心业务逻辑

1. **权限入口管理**：
   - 权限按入口分组，如"用户管理"、"订单管理"等
   - 支持权限入口的创建和管理
   - 权限选择时按入口-权限名称结构展示

2. **状态管理**：
   - 角色和账号都有ACTIVE/INACTIVE状态
   - 只有有效状态的角色和账号才能应用权限
   - 状态变更需要二次确认

3. **唯一性校验**：
   - 角色名称在有效状态下唯一
   - 权限名称全局唯一
   - 权限key值全局唯一

4. **修改日志**：
   - 记录所有修改操作的详细信息
   - 包含修改前后的内容对比
   - 支持按表、记录、操作人等维度查询

## 2. 核心功能实现

### 2.1 角色管理功能

#### 2.1.1 角色列表查询

```sql
-- 查询角色列表（包含最近更新信息）
SELECT 
    r.id,
    r.role_code,
    r.role_name,
    r.description,
    r.status,
    r.created_at,
    r.updated_at,
    r.updated_by,
    -- 权限数量
    (SELECT COUNT(*) FROM role_permission rp WHERE rp.role_id = r.id) as permission_count
FROM role r
ORDER BY r.updated_at DESC;
```

#### 2.1.2 创建角色

```sql
-- 1. 校验角色名称唯一性（仅在有效状态下）
SELECT COUNT(*) FROM role 
WHERE role_name = ? AND status = 'ACTIVE';

-- 2. 创建角色
INSERT INTO role (role_code, role_name, description, status, created_by, updated_by)
VALUES (?, ?, ?, 'ACTIVE', ?, ?);

-- 3. 批量分配权限
INSERT INTO role_permission (role_id, permission_id, created_by, updated_by)
SELECT ?, permission_id, ?, ? FROM (
    SELECT ? as permission_id UNION ALL
    SELECT ? as permission_id
    -- ... 更多权限ID
) as permissions;

-- 4. 记录创建日志
INSERT INTO modification_log (table_name, record_id, operation_type, operator_id, operator_name, remark)
VALUES ('role', ?, 'CREATE', ?, ?, '创建角色');
```

#### 2.1.3 修改角色

```sql
-- 1. 记录修改前的状态
SELECT role_name, description, status FROM role WHERE id = ?;

-- 2. 更新角色信息
UPDATE role 
SET role_name = ?, description = ?, updated_by = ?, updated_at = NOW()
WHERE id = ?;

-- 3. 记录修改日志
INSERT INTO modification_log (table_name, record_id, operation_type, field_name, old_value, new_value, operator_id, operator_name)
VALUES ('role', ?, 'UPDATE', 'role_name', ?, ?, ?, ?);

-- 4. 更新权限关联（先删除后新增）
DELETE FROM role_permission WHERE role_id = ?;
INSERT INTO role_permission (role_id, permission_id, created_by, updated_by)
SELECT ?, permission_id, ?, ? FROM (
    -- 新的权限列表
) as new_permissions;
```

#### 2.1.4 角色状态变更

```sql
-- 1. 记录状态变更前的值
SELECT status FROM role WHERE id = ?;

-- 2. 更新状态
UPDATE role 
SET status = ?, updated_by = ?, updated_at = NOW()
WHERE id = ?;

-- 3. 记录状态变更日志
INSERT INTO modification_log (table_name, record_id, operation_type, field_name, old_value, new_value, operator_id, operator_name, remark)
VALUES ('role', ?, 'STATUS_CHANGE', 'status', ?, ?, ?, ?, '角色状态变更');

-- 4. 如果设置为无效，需要处理关联的用户角色
UPDATE business_account_role 
SET status = 'INACTIVE', updated_by = ?, updated_at = NOW()
WHERE role_id = ? AND status = 'ACTIVE';
```

### 2.2 账号管理功能

#### 2.2.1 账号列表查询

```sql
-- 查询账号列表（包含角色信息和最近更新信息）
SELECT 
    ba.id,
    ba.account_name,
    ba.employee_code,
    ba.real_name,
    ba.phone,
    ba.email,
    ba.status,
    ba.created_at,
    ba.updated_at,
    ba.updated_by,
    -- 关联的角色
    GROUP_CONCAT(r.role_name) as role_names
FROM business_account ba
LEFT JOIN business_account_role bar ON ba.id = bar.business_account_id AND bar.status = 'ACTIVE'
LEFT JOIN role r ON bar.role_id = r.id AND r.status = 'ACTIVE'
GROUP BY ba.id
ORDER BY ba.updated_at DESC;
```

#### 2.2.2 创建账号

```sql
-- 1. 校验邮箱格式和手机号格式（在应用层处理）

-- 2. 创建账号
INSERT INTO business_account (
    account_name, employee_code, real_name, phone, email, 
    password, account_type, status, created_by, updated_by
) VALUES (?, ?, ?, ?, ?, ?, 'OPERATION', 'ACTIVE', ?, ?);

-- 3. 分配角色
INSERT INTO business_account_role (business_account_id, role_id, status, created_by, updated_by)
SELECT ?, role_id, 'ACTIVE', ?, ? FROM (
    SELECT ? as role_id UNION ALL
    SELECT ? as role_id
    -- ... 更多角色ID
) as roles;

-- 4. 记录创建日志
INSERT INTO modification_log (table_name, record_id, operation_type, operator_id, operator_name, remark)
VALUES ('business_account', ?, 'CREATE', ?, ?, '创建账号');
```

#### 2.2.3 修改账号

```sql
-- 1. 记录修改前的信息
SELECT real_name, phone, email FROM business_account WHERE id = ?;

-- 2. 更新账号信息
UPDATE business_account 
SET real_name = ?, phone = ?, email = ?, updated_by = ?, updated_at = NOW()
WHERE id = ?;

-- 3. 记录修改日志
INSERT INTO modification_log (table_name, record_id, operation_type, field_name, old_value, new_value, operator_id, operator_name)
VALUES 
('business_account', ?, 'UPDATE', 'real_name', ?, ?, ?, ?),
('business_account', ?, 'UPDATE', 'phone', ?, ?, ?, ?),
('business_account', ?, 'UPDATE', 'email', ?, ?, ?, ?);

-- 4. 更新角色关联
-- 先将现有角色设为无效
UPDATE business_account_role 
SET status = 'INACTIVE', updated_by = ?, updated_at = NOW()
WHERE business_account_id = ?;

-- 再添加新的角色关联
INSERT INTO business_account_role (business_account_id, role_id, status, created_by, updated_by)
SELECT ?, role_id, 'ACTIVE', ?, ? FROM (
    -- 新的角色列表
) as new_roles;
```

### 2.3 权限管理功能

#### 2.3.1 权限列表查询

```sql
-- 查询权限列表（按权限入口分组）
SELECT 
    pe.entry_name as permission_entry,
    p.permission_name,
    p.description,
    p.permission_key,
    p.status,
    p.created_at,
    p.updated_at,
    p.updated_by
FROM permission p
JOIN permission_entry pe ON p.permission_entry_id = pe.id
ORDER BY pe.sort_order, p.order_num;
```

#### 2.3.2 创建权限

```sql
-- 1. 校验权限名称和key值唯一性
SELECT COUNT(*) FROM permission WHERE permission_name = ?;
SELECT COUNT(*) FROM permission WHERE permission_key = ?;

-- 2. 创建权限入口（如果需要）
INSERT INTO permission_entry (entry_code, entry_name, description, created_by, updated_by)
VALUES (?, ?, ?, ?, ?)
ON DUPLICATE KEY UPDATE entry_name = entry_name; -- 如果已存在则不更新

-- 3. 创建权限
INSERT INTO permission (
    permission_entry_id, permission_code, permission_name, permission_key,
    type, description, status, created_by, updated_by
) VALUES (?, ?, ?, ?, ?, ?, 'ACTIVE', ?, ?);

-- 4. 记录创建日志
INSERT INTO modification_log (table_name, record_id, operation_type, operator_id, operator_name, remark)
VALUES ('permission', ?, 'CREATE', ?, ?, '创建权限');
```

#### 2.3.3 修改权限

```sql
-- 1. 记录修改前的信息
SELECT permission_name, description, permission_key FROM permission WHERE id = ?;

-- 2. 校验唯一性（排除当前记录）
SELECT COUNT(*) FROM permission WHERE permission_name = ? AND id != ?;
SELECT COUNT(*) FROM permission WHERE permission_key = ? AND id != ?;

-- 3. 更新权限信息
UPDATE permission 
SET permission_name = ?, description = ?, permission_key = ?, 
    permission_entry_id = ?, updated_by = ?, updated_at = NOW()
WHERE id = ?;

-- 4. 记录修改日志
INSERT INTO modification_log (table_name, record_id, operation_type, field_name, old_value, new_value, operator_id, operator_name)
VALUES 
('permission', ?, 'UPDATE', 'permission_name', ?, ?, ?, ?),
('permission', ?, 'UPDATE', 'description', ?, ?, ?, ?),
('permission', ?, 'UPDATE', 'permission_key', ?, ?, ?, ?);
```

## 3. 权限控制实现

### 3.1 基础权限定义

```sql
-- 插入基础权限入口
INSERT INTO permission_entry (entry_code, entry_name, description, sort_order) VALUES
('ROLE_MANAGEMENT', '角色管理', '角色相关权限', 1),
('ACCOUNT_MANAGEMENT', '账号管理', '账号相关权限', 2),
('PERMISSION_MANAGEMENT', '权限管理', '权限相关权限', 3);

-- 插入基础权限
INSERT INTO permission (permission_entry_id, permission_code, permission_name, permission_key, type, description) VALUES
-- 角色管理权限
(1, 'ROLE_VIEW', '查看角色', 'role:view', 'API', '查看角色列表权限'),
(1, 'ROLE_MANAGE', '管理角色', 'role:manage', 'API', '新增、修改、删除角色权限'),

-- 账号管理权限
(2, 'ACCOUNT_VIEW', '查看账号', 'account:view', 'API', '查看账号列表权限'),
(2, 'ACCOUNT_MANAGE', '管理账号', 'account:manage', 'API', '新增、修改账号信息、修改账号状态权限'),

-- 权限管理权限
(3, 'PERMISSION_VIEW', '查看权限', 'permission:view', 'API', '查看权限列表权限'),
(3, 'PERMISSION_MANAGE', '管理权限', 'permission:manage', 'API', '新增、修改权限信息、修改权限状态权限'),
(3, 'PERMISSION_ENTRY_CREATE', '创建权限入口', 'permission:entry:create', 'API', '创建权限入口权限');
```

### 3.2 权限校验逻辑

```java
// 权限校验服务
@Service
public class PermissionCheckService {
    
    /**
     * 检查用户是否有指定权限
     */
    public boolean hasPermission(Long userId, String permissionKey) {
        String sql = """
            SELECT COUNT(*) > 0
            FROM business_account ba
            JOIN business_account_role bar ON ba.id = bar.business_account_id
            JOIN role r ON bar.role_id = r.id
            JOIN role_permission rp ON r.id = rp.role_id
            JOIN permission p ON rp.permission_id = p.id
            WHERE ba.id = ?
              AND ba.status = 'ACTIVE'
              AND bar.status = 'ACTIVE'
              AND r.status = 'ACTIVE'
              AND p.status = 'ACTIVE'
              AND p.permission_key = ?
              AND rp.grant_type = 'GRANT'
            """;
        
        return jdbcTemplate.queryForObject(sql, Boolean.class, userId, permissionKey);
    }
    
    /**
     * 获取用户权限列表（按入口分组）
     */
    public Map<String, List<String>> getUserPermissionsByEntry(Long userId) {
        String sql = """
            SELECT pe.entry_name, p.permission_key
            FROM business_account ba
            JOIN business_account_role bar ON ba.id = bar.business_account_id
            JOIN role r ON bar.role_id = r.id
            JOIN role_permission rp ON r.id = rp.role_id
            JOIN permission p ON rp.permission_id = p.id
            JOIN permission_entry pe ON p.permission_entry_id = pe.id
            WHERE ba.id = ?
              AND ba.status = 'ACTIVE'
              AND bar.status = 'ACTIVE'
              AND r.status = 'ACTIVE'
              AND p.status = 'ACTIVE'
              AND pe.status = 'ACTIVE'
              AND rp.grant_type = 'GRANT'
            ORDER BY pe.sort_order, p.order_num
            """;
        
        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, userId);
        
        return results.stream()
            .collect(Collectors.groupingBy(
                row -> (String) row.get("entry_name"),
                Collectors.mapping(
                    row -> (String) row.get("permission_key"),
                    Collectors.toList()
                )
            ));
    }
}
```

## 4. 变更历史

| 版本 | 日期 | 作者 | 变更描述 | 关联功能/需求 |
|-----|------|------|---------|-------------|
| 2.0 | 2025-01-27 | 系统设计团队 | 根据新需求调整数据库设计和功能实现 | 运营系统权限管理新需求 |
| 1.0 | 2025-01-27 | 系统设计团队 | 初始版本 | 运营系统权限管理基础版本 |
