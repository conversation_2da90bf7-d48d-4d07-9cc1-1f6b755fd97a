server:
  port: 8092

# 禁用Hibernate自动配置，因为项目使用MyBatis-Plus而非JPA
spring:
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************
    username: liutao
    password: 4@1CS4mW
  data:
    redis:
      host: *************
      port: 16379
      password: O*mZz1rrA!S5q
      database: 0
  cloud:
    function:
      definition: ;
      #consumeOrderPaidEvent;consumeOrderStatusChangedEvent
    stream:
      bindings:
        # 订单创建事件消费者配置
        consumeOrderCreatedEvent-in-0:
          destination: order-created-events
          group: order-service
          content-type: application/json
        # # 订单支付事件消费者配置
        # consumeOrderPaidEvent-in-0:
        #   destination: order-paid-events
        #   group: order-service
        #   content-type: application/json
        # # 订单状态变更事件消费者配置
        # consumeOrderStatusChangedEvent-in-0:
        #   destination: order-status-changed-events
        #   group: order-service
        #   content-type: application/json
      servicebus:
        bindings:
          consumeOrderCreatedEvent-in-0:
            consumer:
              auto-complete: true
              sessionEnabled: true
          # consumeOrderPaidEvent-in-0:
          #   consumer:
          #     auto-complete: true
          # consumeOrderStatusChangedEvent-in-0:
          #   consumer:
          #     auto-complete: true
      binders:
        servicebus:
          type: servicebus
          environment:
            spring:
              cloud:
                azure:
                  servicebus:
                    connection-string: Endpoint=sb://yuanchuan-asb.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=t54BvRmzN8etGGmk4STCIHXS/ztiFEchd+ASbOWQtPo=

# Azure Service Bus配置
# 是否启用Azure Service Bus（可以通过此配置在不同环境中切换消息系统）
azure:
  servicebus:
    connection-string: Endpoint=sb://yuanchuan-asb.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=t54BvRmzN8etGGmk4STCIHXS/ztiFEchd+ASbOWQtPo=
    enabled: true
    retry:
      max-attempts: 3
      delay: 1000

zookeeper:
  address: **************:2181
  name: com.yuanchuan.content

dubbo:
  application:
    name: com.yuanchuan.content
  protocol:
    name: dubbo
    port: 20802
    host: 0.0.0.0
  scan:
    base-packages: com.yuanchuan.content
  registry:
    address: zookeeper://**************:2181
    register: true
    timeout: 60000
    parameters:
      blockUntilConnectedWait: 60000
      retryIntervalMillis: 5000
      retryTimes: 5
      sessionTimeoutMs: 180000
      connectionTimeoutMs: 30000
    client: curator
  group: content-local
  provider:
    payload: 83886080
  consumer:
    timeout: 600000
    check: false


mybatis-plus:
  # 添加多个路径，确保可以找到 XML 映射文件
  mapper-locations:
    - classpath*:mapper/**/*.xml
    - classpath*:com/yuanchuan/content/infrastructure/mapper/*.xml
    - classpath*:BOOT-INF/classes/mapper/**/*.xml
  type-aliases-package: com.yuanchuan.content.dto
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  level:
    org.apache.dubbo: DEBUG
    org.apache.zookeeper: DEBUG

# dubbo 消费者group
dubbo.consumer.group.user: user-local
dubbo.consumer.group.shop: shop-local
dubbo.consumer.group.order: order-local
dubbo.consumer.group.reservation: reservation-local
dubbo.consumer.group.review: review-local
dubbo.consumer.group.marketing: marketing-local
dubbo.consumer.group.tag: tag-local