package com.yuanchuan.content;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@EnableDubbo
@SpringBootApplication
public class ContentBootstrapApplication {

    public static void main(String[] args) {
        SpringApplication.run(ContentBootstrapApplication.class, args);
    }

}
